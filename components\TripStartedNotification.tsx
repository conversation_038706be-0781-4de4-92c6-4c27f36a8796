import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';

interface TripDetails {
  id: string;
  driverName?: string;
  origin: {
    name: string;
    lat: number;
    lng: number;
  };
  destination: {
    name: string;
    lat: number;
    lng: number;
  };
  timestamp: string;
  pricePerSeat?: number;
  passengers?: Array<{
    firstName: string;
    lastName: string;
  }>;
  // Additional fields for complete trip data
  type?: string;
  mode?: string;
  status?: string;
  noOfPassengers?: number;
  driver?: any;
  stops?: any[];
  preferences?: any[];
}

interface TripStartedNotificationProps {
  userRole: 'driver' | 'passenger';
  tripDetails: TripDetails;
  timestamp?: string;
  onViewTrip?: () => void;
  onTrackTrip?: () => void;
}

const TripStartedNotification: React.FC<TripStartedNotificationProps> = ({
  userRole,
  tripDetails,
  timestamp,
  onViewTrip,
  onTrackTrip
}) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const formatTripTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: true 
    });
  };

  const handleViewTrip = () => {
      router.push({
        pathname: "/(ride)/TripSummary",
        params: {
          trip: encodeURIComponent(JSON.stringify(tripDetails)),
        },
      });
  };

  const handleTrackTrip = () => {
    if (onTrackTrip) {
      onTrackTrip();
    } else {
      // Navigate to TripSummary which handles both roles and trip modes
      router.push({
        pathname: "/(ride)/TripSummary",
        params: {
          trip: encodeURIComponent(JSON.stringify(tripDetails)),
        },
      });
    }
  };

  const getTitle = () => {
    if (userRole === 'driver') {
      return 'Your trip has started';
    } else {
      return `${tripDetails.driverName} has started your trip`;
    }
  };

  const getDescription = () => {
    if (userRole === 'driver') {
      const passengerCount = tripDetails.passengers?.length || 0;
      return passengerCount > 0 
        ? `You're now driving ${passengerCount} passenger${passengerCount > 1 ? 's' : ''} to their destination.`
        : 'Your trip is now active. Safe travels!';
    } else {
      return 'You can track the progress in real-time. The driver is on their way!';
    }
  };

  return (
    <TouchableOpacity onPress={handleViewTrip} className="flex w-full flex-col bg-[#FFF] rounded-md p-4 mx-4 mb-[12px]">
      <View className="flex flex-row justify-between items-center mb-[6px]">
        <Text className="text-[#8388A2] text-xs font-semibold">Update</Text>
      </View>

      <View className="">
        <View className="flex flex-row justify-between items-center mb-[4px]">
        <View className='flex-row items-center'>
            <View className='bg-[#E05859] h-2 w-2 rounded-full mr-[6px]'  />
            <Text className="text-base font-semibold text-[#151B2D]">
              Your {userRole === 'driver' ? "trip" : "ride"} has started
            </Text>
          </View>
        <View className="flex flex-row items-center gap-1">
          <Text className="text-[#787A80] text-xs font-normal">
            {timestamp ? formatTime(timestamp) : 'Just now'}
          </Text>
          <Image 
            source={require("../assets/images/goNew.png")} 
            className="w-2 h-3" 
          />
        </View>
      </View>
        
        
        <View className='flex flex-row mb-[4px] items-center justify-between'>
                        <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm font-medium">
                          {tripDetails.origin.name || 'Unknown origin'}
                        </Text>
                        <Image source={require("../assets/images/fromandtoNew.png")} className="mx-2 w-3 h-3" />
                        <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm text-right font-medium">
                          {tripDetails.destination.name || 'Unknown destination'}
                        </Text>
                      </View>
              
                      {/* Trip Details */}
                      <View className="flex-row mb-2 items-center space-x-2">
                        <View className='gap-1 flex-row items-center'>
                          <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4' />
                          <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
                        </View>
                        <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                        <View className='flex-row items-center'>
                          <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                            {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleDateString([], {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'} -
                          </Text>
                          <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                            {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A'}
                          </Text>
                        </View>
                        <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                        <View>
                          <Text className="text-sm font-medium text-[#787A80]">₦{tripDetails?.pricePerSeat || '0'}</Text>
                        </View>
                      </View>
      </View>

      
    </TouchableOpacity>
  );
};

export default TripStartedNotification;
