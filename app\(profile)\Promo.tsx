import React, { useState } from 'react';
import { View, Text, Switch, TouchableOpacity } from 'react-native';
import { styled } from "nativewind";
import useCurrentUser from "@/hooks/useCurrentUser";
import GoBack from '@/components/GoBack';
import { router, Href } from 'expo-router';
import { Image } from 'react-native';

const StyledView = styled(View);
const StyledText = styled(Text);

const Promo = () => {
    const [isPromo, setIsPromo] = useState(false);

    const { data: user } = useCurrentUser();

    return (
        <StyledView className="flex-1 p-5 bg-white">
            <View className="flex-row items-center my-4">
                 <GoBack />
            </View>

            <StyledText className="text-2xl font-bold">Promo Code</StyledText>

            <View className="flex-1 justify-center items-center">
                <View className="items-center justify-center">
                    <Image
                        source={require("../../assets/images/promoimage.png")}
                        className="w-[60px] h-[60px]"
                        resizeMode="contain"
                    />
                    <Text className="text-[#4A4C50] text-center mt-4 text-sm">
                    You don’t have any promo codes
                    </Text>
                </View>
            </View>

            <View className="flex-1 justify-end">
                <TouchableOpacity 
                    className="bg-blue-500 p-4 rounded-full items-center"
                     onPress={() => router.push("/(profile)/EnterPromo" as Href)}
                >
                    <Text className="text-white font-bold">Apply Promo Code</Text>
                </TouchableOpacity>
            </View>
            
        </StyledView>
    );
};

export default Promo;
