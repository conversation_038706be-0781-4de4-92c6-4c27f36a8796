import { Realtime } from 'ably';
import { useNotificationStore } from '@/app/store/notificationStore';
import { pushNotificationHelpers } from './pushNotificationService';

export interface RealtimeNotificationData {
  type: 'trip_started' | 'pickup_confirmed' | 'trip_ended' | 'payment_request' | 'trip_cancelled' | 'custom_message' | 'ride_request' | 'booking_accepted' | 'booking_declined';
  tripId: string;
  fromUserId: string;
  fromUserName: string;
  targetRole: 'driver' | 'passenger' | 'all';
  title: string;
  content: string;
  data?: any;
  timestamp: string;
}

class RealtimeNotificationService {
  private ably: Realtime | null = null;
  private currentTripId: string | null = null;
  private currentUserId: string | null = null;
  private currentUserRole: 'driver' | 'passenger' | null = null;
  private allowSelfNotifications: boolean = __DEV__; // Allow self-notifications in development

  initialize(ablyInstance: Realtime, tripId: string, userId: string, userRole: 'driver' | 'passenger') {
    this.ably = ablyInstance;
    this.currentTripId = tripId;
    this.currentUserId = userId;
    this.currentUserRole = userRole;

    console.log('✅ RealtimeNotificationService initialized:', {
      tripId,
      userId,
      userRole,
      hasAbly: !!ablyInstance,
      isInitialized: this.isInitialized()
    });

    // Subscribe to trip notifications
    this.subscribeToNotifications();
  }

  // Method to toggle self-notifications for testing
  setAllowSelfNotifications(allow: boolean) {
    this.allowSelfNotifications = allow;
    console.log(`🔧 Self-notifications ${allow ? 'enabled' : 'disabled'} for testing`);
  }

  // Check if service is properly initialized
  isInitialized(): boolean {
    return !!(this.ably && this.currentTripId && this.currentUserId);
  }

  private subscribeToNotifications() {
    if (!this.ably || !this.currentTripId) return;

    const channel = this.ably.channels.get(`trip-${this.currentTripId}`);
    
    channel.subscribe('notification', (message) => {
      console.log('📱 Received realtime notification:', message.data);
      this.handleIncomingNotification(message.data);
    });

    // Subscribe to progress updates
    channel.subscribe('progress-update', (message) => {
      console.log('📱 Received progress update:', message.data);
      this.handleProgressUpdate(message.data);
    });

    console.log(`🔔 Subscribed to notifications for trip-${this.currentTripId}`);
  }

  private handleIncomingNotification(data: RealtimeNotificationData) {
    // Don't show notifications from yourself (unless explicitly allowed for testing)
    if (data.fromUserId === this.currentUserId && !this.allowSelfNotifications) {
      console.log('🚫 Ignoring notification from self');
      return;
    }

    // In development, show a warning but allow self-notifications for testing
    if (data.fromUserId === this.currentUserId && this.allowSelfNotifications) {
      console.log('⚠️ DEV MODE: Showing notification from self for testing purposes');
    }

    // Check if this notification is for the current user's role
    const shouldShow =
      data.targetRole === 'all' ||
      data.targetRole === this.currentUserRole;

    if (!shouldShow) {
      console.log(`🚫 Notification not for current role (${this.currentUserRole}), ignoring`);
      return;
    }

    console.log('✅ Notification received for current user:', data.type, data.title);
    console.log('📱 Push notifications are now handling this instead of modals');

    // Note: Modals are disabled in favor of push notifications
    // Push notifications are handled by the realtimeNotificationHelpers functions
  }

  private handleProgressUpdate(data: any) {
    // Only handle progress updates for passengers
    if (this.currentUserRole === 'driver') {
      console.log('🚫 Driver ignoring progress update (they generate it)');
      return;
    }

    console.log('📊 Received progress update for passenger:', data.progress + '%');

    // The progress update will be handled by individual components that subscribe to the channel
    // This method is mainly for logging and potential future centralized handling
  }

  private getButtonsForNotificationType(type: string) {
    switch (type) {
      case 'trip_started':
        return [
          {
            text: 'Track Trip',
            action: 'navigate' as const,
            actionData: { route: '/TripSummary' },
            style: 'primary' as const,
          },
          {
            text: 'OK',
            action: 'close' as const,
            style: 'default' as const,
          },
        ];
      
      case 'trip_ended':
        return [
          {
            text: 'Rate Trip',
            action: 'navigate' as const,
            actionData: { route: '/RateTrip' },
            style: 'primary' as const,
          },
          {
            text: 'OK',
            action: 'close' as const,
            style: 'default' as const,
          },
        ];

      case 'ride_request':
        return [
          {
            text: 'View Request',
            action: 'navigate' as const,
            actionData: { route: '/TripSummary' },
            style: 'primary' as const,
          },
          {
            text: 'Later',
            action: 'close' as const,
            style: 'default' as const,
          },
        ];

      case 'booking_accepted':
        return [
          {
            text: 'View Trip',
            action: 'navigate' as const,
            actionData: { route: '/TripSummary' },
            style: 'primary' as const,
          },
          {
            text: 'OK',
            action: 'close' as const,
            style: 'default' as const,
          },
        ];

      case 'booking_declined':
        return [
          {
            text: 'Find Another Trip',
            action: 'navigate' as const,
            actionData: { route: '/(tabs)/Search' },
            style: 'primary' as const,
          },
          {
            text: 'OK',
            action: 'close' as const,
            style: 'default' as const,
          },
        ];

      default:
        return [
          {
            text: 'OK',
            action: 'close' as const,
            style: 'primary' as const,
          },
        ];
    }
  }

  sendNotification(data: Omit<RealtimeNotificationData, 'fromUserId' | 'fromUserName' | 'timestamp'>, fromUserName: string) {
    if (!this.ably || !this.currentTripId || !this.currentUserId) {
      console.error('❌ RealtimeNotificationService not properly initialized:', {
        hasAbly: !!this.ably,
        hasTripId: !!this.currentTripId,
        hasUserId: !!this.currentUserId,
        notificationType: data.type
      });
      return false;
    }

    const notificationData: RealtimeNotificationData = {
      ...data,
      fromUserId: this.currentUserId,
      fromUserName,
      timestamp: new Date().toISOString(),
    };

    console.log('📤 Sending realtime notification:', notificationData.type, 'to', notificationData.targetRole);

    const channel = this.ably.channels.get(`trip-${this.currentTripId}`);
    channel.publish('notification', notificationData);
    return true;
  }

  cleanup() {
    if (this.ably && this.currentTripId) {
      const channel = this.ably.channels.get(`trip-${this.currentTripId}`);
      channel.unsubscribe('notification');
      channel.unsubscribe('progress-update');
      console.log('Cleaned up notification subscriptions');
    }
    
    this.ably = null;
    this.currentTripId = null;
    this.currentUserId = null;
    this.currentUserRole = null;
  }
}

// Export singleton instance
export const realtimeNotificationService = new RealtimeNotificationService();

// Helper functions for common notification types
export const realtimeNotificationHelpers = {
  notifyTripStarted: (tripId: string, driverName: string, tripDetails?: any) => {
    // Send push notification instead of modal
    pushNotificationHelpers.notifyTripStarted(tripId, driverName);

    // Still send realtime data for other listeners
    if (realtimeNotificationService.isInitialized()) {
      realtimeNotificationService.sendNotification({
        type: 'trip_started',
        tripId,
        targetRole: 'all',
        title: '🚗 Trip Started',
        content: `${driverName} has started the trip. You can track the progress in real-time.`,
        data: {
          tripDetails: tripDetails || {
            id: tripId,
            driverName,
            timestamp: new Date().toISOString()
          }
        }
      }, driverName);
    }
    return true;
  },

  notifyPickupConfirmed: (tripId: string, passengerName: string, tripDetails?: any) => {
    // Send push notification instead of modal
    pushNotificationHelpers.notifyPickupConfirmed(tripId, passengerName);

    // Still send realtime data for other listeners
    if (realtimeNotificationService.isInitialized()) {
      realtimeNotificationService.sendNotification({
        type: 'pickup_confirmed',
        tripId,
        targetRole: 'passenger',
        title: '✅ Pickup Confirmed',
        content: `Your pickup has been confirmed by the driver. The trip will begin shortly.`,
        data: {
          tripDetails: tripDetails || {
            id: tripId,
            passengerName,
            timestamp: new Date().toISOString()
          }
        }
      }, 'Driver');
    }
    return true;
  },

  notifyTripEnded: (tripId: string, driverName: string) => {
    // Send push notification instead of modal
    pushNotificationHelpers.notifyTripEnded(tripId);

    // Still send realtime data for other listeners
    if (realtimeNotificationService.isInitialized()) {
      realtimeNotificationService.sendNotification({
        type: 'trip_ended',
        tripId,
        targetRole: 'passenger',
        title: '🎉 Trip Completed',
        content: 'Your trip has been completed successfully. Please proceed with payment if required.',
      }, driverName);
    }
    return true;
  },

  sendCustomMessage: (tripId: string, title: string, content: string, targetRole: 'driver' | 'passenger' | 'all', fromUserName: string) => {
    if (!realtimeNotificationService.isInitialized()) {
      console.warn('⚠️ Cannot send custom message: Service not initialized');
      return false;
    }

    realtimeNotificationService.sendNotification({
      type: 'custom_message',
      tripId,
      targetRole,
      title,
      content,
    }, fromUserName);
    return true;
  },

  // New notification helpers for ride requests and booking status
  notifyRideRequest: (tripId: string, passengerName: string, requestData: any) => {
    if (!realtimeNotificationService.isInitialized()) {
      console.warn('⚠️ Cannot send ride request notification: Service not initialized');
      return false;
    }

    realtimeNotificationService.sendNotification({
      type: 'ride_request',
      tripId,
      targetRole: 'driver',
      title: '🚗 New Ride Request',
      content: `${passengerName} wants to join your trip. Check the details and respond.`,
      data: requestData,
    }, passengerName);
    return true;
  },

  notifyBookingAccepted: (tripId: string, driverName: string, tripData: any) => {
    // Send push notification instead of modal
    pushNotificationHelpers.notifyBookingAccepted(tripId, driverName);

    // Still send realtime data for other listeners
    if (realtimeNotificationService.isInitialized()) {
      realtimeNotificationService.sendNotification({
        type: 'booking_accepted',
        tripId,
        targetRole: 'passenger',
        title: '✅ Booking Accepted',
        content: `Great news! ${driverName} has accepted your ride request. Get ready for your trip.`,
        data: {
          tripDetails: tripData,
          fullTripData: tripData.fullTripData || tripData, // Ensure full trip data is available
          timestamp: new Date().toISOString()
        },
      }, driverName);
    }
    return true;
  },

  notifyBookingDeclined: (tripId: string, driverName: string, tripData: any) => {
    // Send push notification instead of modal
    pushNotificationHelpers.notifyBookingDeclined(tripId, driverName);

    // Still send realtime data for other listeners
    if (realtimeNotificationService.isInitialized()) {
      realtimeNotificationService.sendNotification({
        type: 'booking_declined',
        tripId,
        targetRole: 'passenger',
        title: '❌ Booking Declined',
        content: `Unfortunately, ${driverName} couldn't accept your ride request. Don't worry, you can find another trip.`,
        data: {
          tripDetails: tripData,
          fullTripData: tripData.fullTripData || tripData, // Ensure full trip data is available
          timestamp: new Date().toISOString()
        },
      }, driverName);
    }
    return true;
  },
};
