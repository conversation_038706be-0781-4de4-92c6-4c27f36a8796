import { View, Text, Image } from "react-native";
import React from "react";

interface Props {
  from: string;
  to: string;
}

const FromAndTo = ({ from, to }: Props) => {
  return (
    <View className="px-4 w-full justify-center items-center">
      <View className="w-full bg-white flex-row items-center rounded-[10px] p-[4px]">
        <View className="items-center">
          <Image
            source={require("../assets/images/radio.png")}
            className="w-[20px] h-[20px]"
            resizeMode="contain"
          />
          <View className="h-[17px] w-[1px] bg-[#151B2D]" />
          <Image
            source={require("../assets/images/location.png")}
            className="w-[20px] h-[20px]"
            resizeMode="contain"
          />
        </View>
        <View className="flex-1 gap-y-[2px] ml-3">
          <View className="bg-[#F6F6F6] p-2 rounded-[6px]">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-xs font-semibold"
            >
              {from}
            </Text>
          </View>
          <View className="bg-[#F6F6F6] p-2 rounded-[6px]">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-xs font-semibold"
            >
              {to}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default FromAndTo;
