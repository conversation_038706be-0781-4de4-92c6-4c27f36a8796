import { services } from "@/services";
import { useQuery } from "@tanstack/react-query";

const useActiveTrip = () => {
  const {
    data,
    isLoading,
    error,
    refetch: refetchActive,
  } = useQuery({
    queryKey: ["trip"],
    queryFn: () => services.getActiveTrip(),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: false,
    select: (response) => {
      if (!response) return null;
      
      if (response.data) return response;
      
      return { data: response };
    },
  });

  return { 
    data: data?.data || null, 
    isLoading, 
    error, 
    refetchActive,
    hasActiveTrip: !!data?.data 
  };
};

export default useActiveTrip;
