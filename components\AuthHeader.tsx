import { View, Text } from "react-native";
import React from "react";

interface Props {
  header: string;
  subHeader: string;
  subbHeader: string;
}

const AuthHeader = ({ header, subHeader, subbHeader}: Props) => {
  return (
    <View className="mt-4">
      <Text className="text-2xl font-semibold">{header}</Text>
      <Text className="text-[#787A80] text-base">{subHeader}</Text>
      <Text className="text-[#787A80] text-base">{subbHeader}</Text>
    </View>
  );
};

export default AuthHeader;
