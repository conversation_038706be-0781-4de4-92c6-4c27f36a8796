import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface TripState {
  // Trip Status
  currentTripId: string | null;
  tripStarted: boolean;
  tripProgress: number;
  showSeats: boolean;
  driverNote: string;
  
  // Modals
  showEndTripModal: boolean;
  showPaymentModal: boolean;
  showPassengerEndTripModal: boolean;
  showEndTripOptions: boolean;
  
  // End Trip Options
  selectedOption: 'option1' | 'option2' | null;
  reportIssue: boolean;
  selectedIssue: 'firstIssue' | 'secondIssue' | 'thirdIssue' | null;
  somethingElse: boolean;
  customIssueText: string;
  showEndTripEarly: boolean;
  
  // Request Statuses
  requestStatuses: Record<string, { 
    status: 'pending' | 'accepted' | 'rejected';
    pickupConfirmed: boolean;
  }>;
  
  // Actions
  setCurrentTripId: (tripId: string | null) => void;
  setTripStarted: (started: boolean) => void;
  setTripProgress: (progress: number) => void;
  setShowSeats: (show: boolean) => void;
  setDriverNote: (note: string) => void;
  setShowEndTripModal: (show: boolean) => void;
  setShowPaymentModal: (show: boolean) => void;
  setShowPassengerEndTripModal: (show: boolean) => void;
  setShowEndTripOptions: (show: boolean) => void;
  setSelectedOption: (option: 'option1' | 'option2' | null) => void;
  setReportIssue: (report: boolean) => void;
  setSelectedIssue: (issue: 'firstIssue' | 'secondIssue' | 'thirdIssue' | null) => void;
  setSomethingElse: (value: boolean) => void;
  setCustomIssueText: (text: string) => void;
  setShowEndTripEarly: (show: boolean) => void;
  setRequestStatuses: (statuses: Record<string, { status: 'pending' | 'accepted' | 'rejected'; pickupConfirmed: boolean; }>) => void;
  updateRequestStatus: (requestId: string, status: { status: 'pending' | 'accepted' | 'rejected'; pickupConfirmed: boolean; }) => void;
  resetTripState: () => void;
  resetForNewTrip: (tripId: string) => void;
}

const initialState = {
  currentTripId: null,
  tripStarted: false,
  tripProgress: 0,
  showSeats: false,
  driverNote: 'Please keep to time',
  showEndTripModal: false,
  showPaymentModal: false,
  showPassengerEndTripModal: false,
  showEndTripOptions: false,
  selectedOption: null,
  reportIssue: false,
  selectedIssue: null,
  somethingElse: false,
  customIssueText: '',
  showEndTripEarly: false,
  requestStatuses: {},
};

export const useTripStore = create<TripState>()(
  persist(
    (set) => ({
      ...initialState,

      setCurrentTripId: (tripId) => set({ currentTripId: tripId }),
      setTripStarted: (started) => set({ tripStarted: started }),
      setTripProgress: (progress) => set({ tripProgress: progress }),
      setShowSeats: (show) => set({ showSeats: show }),
      setDriverNote: (note) => set({ driverNote: note }),
      setShowEndTripModal: (show) => set({ showEndTripModal: show }),
      setShowPaymentModal: (show) => set({ showPaymentModal: show }),
      setShowPassengerEndTripModal: (show) => set({ showPassengerEndTripModal: show }),
      setShowEndTripOptions: (show) => set({ showEndTripOptions: show }),
      setSelectedOption: (option) => set({ selectedOption: option }),
      setReportIssue: (report) => set({ reportIssue: report }),
      setSelectedIssue: (issue) => set({ selectedIssue: issue }),
      setSomethingElse: (value) => set({ somethingElse: value }),
      setCustomIssueText: (text) => set({ customIssueText: text }),
      setShowEndTripEarly: (show) => set({ showEndTripEarly: show }),
      setRequestStatuses: (statuses) => set({ requestStatuses: statuses }),
      updateRequestStatus: (requestId, status) => 
        set((state) => ({
          requestStatuses: {
            ...state.requestStatuses,
            [requestId]: status
          }
        })),
      resetTripState: () => set(initialState),
      resetForNewTrip: (tripId) => set((state) => ({
        ...initialState,
        currentTripId: tripId,
        // Preserve driver note
        driverNote: state.driverNote,
      })),
    }),
    {
      name: 'trip-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // Persist trip state and request statuses
        currentTripId: state.currentTripId,
        tripStarted: state.tripStarted,
        tripProgress: state.tripProgress,
        requestStatuses: state.requestStatuses,
        driverNote: state.driverNote,
        // Don't persist modal states as they should reset on app restart
      }),
    }
  )
); 