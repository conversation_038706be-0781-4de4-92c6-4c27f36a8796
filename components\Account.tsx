import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import React, { Dispatch, SetStateAction } from "react";
import Toast from "react-native-toast-message";
import { performSafeLogout } from "../utils/LogoutHelper"; 

interface Props {
  handleOpenPress: () => void;
  setShowModal: Dispatch<SetStateAction<boolean>>;
}

const Account = ({ handleOpenPress, setShowModal }: Props) => {
  const handleLogout = () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Logout",
          onPress: async () => {
            Toast.show({
              type: "info",
              text1: "Logging out...",
              position: "bottom",
              visibilityTime: 1000,
            });
            
            const success = await performSafeLogout();
            
            if (!success) {
              Toast.show({
                type: "error",
                text1: "Logout failed. Please try again.",
                position: "bottom",
              });
            }
          }
        }
      ]
    );
  };

  return (
    <View className="bg-white mb-8 rounded-[9px] p-3 mt-5">
      <View className="mt-1">
        <Text className="text-[#4A4C50] text-[14px] font-medium">Account</Text>
        <View className="mt-5">
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={handleLogout}
            className="flex-row justify-between items-center py-2"
          >
            <View className="flex-row items-center">
              <Image
                source={require("../assets/images/log_out.png")}
                className="w-[30px] h-[30px]"
                resizeMode="contain"
              />
              <Text className="ml-3 text-[#151B2D] text-[14px]">Log out</Text>
            </View>
            <Image
              source={require("../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </TouchableOpacity>

          <View className="w-full h-[1px] my-2 bg-white-100" />

          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              setShowModal(true);
              handleOpenPress();
            }}
            className="flex-row justify-between items-center py-2"
          >
            <View className="flex-row items-center">
              <Image
                source={require("../assets/images/delete.png")}
                className="w-[30px] h-[30px]"
                resizeMode="contain"
              />
              <Text className="ml-3 text-[#151B2D] text-[14px]">
                Delete Account
              </Text>
            </View>
            <Image
              source={require("../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default Account;