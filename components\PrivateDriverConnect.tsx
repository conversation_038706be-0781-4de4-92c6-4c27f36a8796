import React, { useRef, useEffect, useState, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  Platform,
} from "react-native";
import { router } from "expo-router";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { Realtime } from "ably";
import { useUser } from "@/context/UserContext";
import { Trip } from "@/utils/types";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  refetchActive: any;
  trip: Trip;
}

const PrivateDriverConnect: React.FC<Props> = ({
  step,
  setStep,
  setCancelRide,
  refetchActive,
  trip,
}) => {
  const ably = new Realtime({
    key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
    autoConnect: true,
    // useTokenAuth: true
  });

  const { ride } = useUser();

  const bottomSheetRef = useRef<BottomSheet>(null);
  const screenHeight = Dimensions.get("window").height;

  const animationRef = useRef<Animated.CompositeAnimation>();
  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  const { updateRide } = useUser();

  const dynamicSnapPoint = useMemo(() => {
    const minHeight = 250;
    const percentHeight = screenHeight * 0.2;
    return Math.max(minHeight, percentHeight);
  }, [screenHeight]);

  const customSnapPoints = useMemo(
    () => [dynamicSnapPoint],
    [dynamicSnapPoint]
  );

  const [status, setStatus] = useState<"connecting" | "not_accepted" | "found">(
    "connecting"
  );
  const progressAnimation = useRef(new Animated.Value(0)).current;

  const handleDriverAccepted = () => {
    // Stop any ongoing animations or timeouts
    animationRef.current?.stop();
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    setStatus("found");
    refetchActive();
    setTimeout(() => {
      setStep(step + 1);
    }, 2000);
  };

  const handleDriverDeclined = () => {
    // Stop the current animation
    animationRef.current?.stop();
    setStatus("not_accepted");
    // Reset progress bar
    progressAnimation.setValue(0);
    // Start a new connection attempt after a delay
    retryTimeoutRef.current = setTimeout(() => {
      startDriverConnection();
    }, 3000);
  };

  const startDriverConnection = () => {
    // Reset states
    progressAnimation.setValue(0);
    setStatus("connecting");

    // Create and store the animation reference
    animationRef.current = Animated.timing(progressAnimation, {
      toValue: 100,
      duration: 30000,
      useNativeDriver: false,
    });

    // Start the animation and handle timeout
    animationRef.current.start(({ finished }) => {
      if (finished) {
        handleDriverDeclined();
      }
    });
  };

  useEffect(() => {
    // Subscribe to ride channel
    const channel = ably.channels.get(ride?.driverId || trip?.driver.id);

    // Handle ride acceptance
    channel.subscribe("private-ride-accepted", (message) => {
      console.log("Ride Accepted:", message.data);
      updateRide("rideId", message.data?.id);
      handleDriverAccepted();
    });

    // Handle ride decline
    channel.subscribe("private-ride-declined", (message) => {
      console.log("Ride Declined:", message.data);
      handleDriverDeclined();
    });

    // Start initial connection
    startDriverConnection();

    // Cleanup on unmount
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      animationRef.current?.stop();
      progressAnimation.setValue(0);
      channel.unsubscribe();
      channel.detach();
    };
  }, [trip]);

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "connecting":
        return "Connecting to driver";
      case "not_accepted":
        return "Ride declined. Finding another driver...";
      case "found":
        return "Driver found";
    }
  };

  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto my-4">
          <Text
            className={`text-[#151B2D] font-semibold ${
              Platform.OS === "ios" ? "text-xl" : "text-[19px]"
            }`}
          >
            {getStatusText()}
          </Text>
          <View className="w-full my-2">
            <View className="bg-[#D9D9D9] h-[2px] relative">
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: progressAnimation.interpolate({
                      inputRange: [0, 100],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                ]}
              />
            </View>
          </View>
          <View className="bg-[#473BF00A] p-2 rounded-[6px] w-full mt-3">
            <Text
              className={`w-full text-[#4A4C50] ${
                Platform.OS === "ios" ? "text-[15px]" : "text-[13px]"
              }`}
            >
              Waiting for the driver to accept your request, this might takes a
              few minutes
            </Text>
          </View>
        </View>
        <View className="absolute bottom-0 left-0 right-0 px-5 pb-5 bg-white">
          <Button
            text="Cancel Trip"
            buttonClassName="bg-[#EDEDED] w-full mb-2"
            textClassName="text-[#151B2D]"
            onClick={() => router.replace('/(drawer)/(tabs)/Home')}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    backgroundColor: "#473BF0",
    height: 2,
    position: "absolute",
    bottom: 0,
  },
});

export default PrivateDriverConnect;
