import { View, Text, TouchableOpacity, ActivityIndicator } from "react-native";
import React from "react";

interface Props {
  isLoading?: boolean;
  text: string;
  buttonClassName?: string;
  textClassName?: string;
  onClick?: () => void;
  buttonDisabled?: boolean;
  width?: string | number;
  height?: string | number;
  customStyle?: any;
}

const Button = ({
  isLoading,
  text,
  buttonClassName,
  textClassName,
  onClick,
  buttonDisabled,
  width = "w-full",
  height,
  customStyle,
}: Props) => {
  return (
    <TouchableOpacity
      disabled={isLoading || buttonDisabled}
      activeOpacity={0.8}
      onPress={onClick}
      style={customStyle}
      className={`${buttonClassName} ${width} justify-center items-center rounded-[100px] ${
        isLoading || buttonDisabled ? "opacity-50" : "opacity-100"
      } ${height || "py-[10px]"}`}
    >
      {isLoading ? (
        <View className="flex-row items-center">
          <ActivityIndicator size="small" color="#fff" />
        </View>
      ) : (
        <Text className={`font-semibold ${textClassName || "text-base"}`}>
          {text}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
