import { View, StyleSheet, StatusBar } from "react-native";
import React, { useEffect, useState } from "react";
import { useUser } from "../../context/UserContext";
import TripStarted from "@/components/TripStarted";
import TripEnded from "@/components/TripEnded";
import AvailablePrivateDrivers from "@/components/AvailablePrivateDrivers";
import DriverCancelRide from "@/components/DriverCancelRide";
import { router, useLocalSearchParams } from "expo-router";
import { Realtime } from "ably";
import { Trip } from "@/utils/types";
import Toast from "react-native-toast-message";
import PrivatePaymentMode from "@/components/PrivatePaymentMode";
import PrivateDriverConnect from "@/components/PrivateDriverConnect";
import DriverInformation from "@/components/DriverInformation";
import PrivateDriverArrived from "@/components/PrivateDriverArrived";
import useActiveTrip from "@/hooks/useActiveTrip";
import { useRide } from "@/context/RideProvider";
import TripRoute from "@/components/TripRoute";
import TripMap from "@/components/TripMap";

const PrivatePassengerMap = () => {
  const ably = new Realtime({
    key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
    autoConnect: true,
    // useTokenAuth: true
  });

  const { ride: userRide, initialRegion, updateRide } = useUser();
  const { privateRide } = useRide();

  const { trip: tripParams } = useLocalSearchParams();

  const trip: Trip = tripParams
    ? JSON.parse(decodeURIComponent(tripParams as string))
    : [];

  //Ride data passed through url
  const { rides: ridesParam } = useLocalSearchParams();
  const rides = ridesParam ? JSON.parse(ridesParam as string) : [];
  console.log("private Ride", rides);

  const [step, setStep] = useState(1);

  const [cancelRide, setCancelRide] = useState(false);
  const [showPassenger, setShowPassenger] = useState(false);

  useEffect(() => {
    // Subscribe to ride channel
    const channel = ably.channels.get(userRide?.driverId || trip?.driver?.id);

    // Handle ride cancelled
    channel.subscribe("ride-cancelled", (message) => {
      console.log("Ride Cancelled", message.data);
      Toast.show({
        type: "success",
        text1: "Driver Cancelled the ride",
      });
      setTimeout(() => {
        router.replace("/(drawer)/(tabs)/Home");
      }, 1000);
    });

    // Cleanup on unmount
    return () => {
      channel.unsubscribe();
      channel.detach();
    };
  }, []);

  const [direction, setDirection] = useState(false);
  const [directionOrigin, setDirectionOrigin] = useState({
    latitude: 0,
    longitude: 0,
  });

  const [directionDestination, setDirectionDestination] = useState({
    latitude: 0,
    longitude: 0,
  });

  const setMarkerDirection = (
    latitude: number,
    longitude: number,
    id: string,
    price: number
  ) => {
    setDirectionOrigin({
      latitude: initialRegion.latitude,
      longitude: initialRegion.longitude,
    });
    setDirectionDestination({
      latitude: latitude,
      longitude: longitude,
    });
    setDirection(true);
    updateRide("driverId", id);
    updateRide("price", price);
  };

  const { data: activeTrip, isLoading, refetchActive } = useActiveTrip();

  return (
    <View className="h-full w-full bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="transparent" />

      <TripMap
        step={step}
        markers={rides?.drivers || []}
        pin={activeTrip.data}
        direction={direction}
        directionOrigin={directionOrigin}
        directionDestination={directionDestination}
        setMarkerDirection={setMarkerDirection}
      />

      <TripRoute
        from={privateRide?.origin?.name || trip?.origin?.name}
        to={privateRide.destination.name || trip?.destination?.name}
      />

      {step === 1 && (
        <AvailablePrivateDrivers
          step={step}
          setStep={setStep}
          availableCoride={rides}
          setMarkerDirection={setMarkerDirection}
          trip={activeTrip?.data}
        />
      )}
      {step === 2 && <PrivatePaymentMode step={step} setStep={setStep} />}
      {step === 3 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} />
        ) : (
          <PrivateDriverConnect
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            refetchActive={refetchActive}
            trip={activeTrip.data}
          />
        ))}

      {step === 4 &&
        (showPassenger ? (
          <DriverInformation
            setShowPassenger={setShowPassenger}
            trip={activeTrip.data}
          />
        ) : cancelRide ? (
          <DriverCancelRide
            setCancelRide={setCancelRide}
            tripId={activeTrip.data.id}
          />
        ) : (
          <PrivateDriverArrived
            step={step}
            setStep={setStep}
            setShowPassenger={setShowPassenger}
            setCancelRide={setCancelRide}
            // userTripInformation={userTripInformation}
            // refetchActive={refetchActive}
            trip={activeTrip.data}
            isLoading={isLoading}
            refetchActive={refetchActive}
          />
        ))}
      {step === 5 &&
        (showPassenger ? (
          <DriverInformation
            setShowPassenger={setShowPassenger}
            trip={activeTrip.data}
          />
        ) : (
          <TripStarted
            step={step}
            setStep={setStep}
            setShowPassenger={setShowPassenger}
            trip={activeTrip.data}
          />
        ))}
      {step === 6 && (
        <TripEnded step={step} setStep={setStep} trip={activeTrip.data} />
      )}
    </View>
  );
};

export default PrivatePassengerMap;
