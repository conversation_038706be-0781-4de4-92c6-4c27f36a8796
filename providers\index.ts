import { QueryClient } from "@tanstack/react-query";
import { Platform } from "react-native";
import NetInfo from "@react-native-community/netinfo";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3, 
      retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000), 
      staleTime: 1 * 60 * 1000,

      refetchOnMount: true,
      refetchOnWindowFocus: Platform.OS === "web",
      refetchOnReconnect: true,
      refetchInterval: false,
    },
    mutations: {
      retry: 2,
    },
  },
});

NetInfo.addEventListener((state) => {
  if (state.isConnected) {
    queryClient.refetchQueries();
  }
});
