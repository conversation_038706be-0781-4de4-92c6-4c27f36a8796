import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import { notificationHelpers } from '@/utils/notificationHelpers';
import useCurrentUser from '@/hooks/useCurrentUser';
import useActiveTrip from '@/hooks/useActiveTrip';

const RoleBasedNotificationTester: React.FC = () => {
  const { data: currentUser } = useCurrentUser();
  const { data: activeTrip } = useActiveTrip();

  const testRideRequest = () => {
    if (!activeTrip?.id || !currentUser) return;
    
    realtimeNotificationHelpers.notifyRideRequest(
      activeTrip.id,
      `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
      {
        requestId: `test_${Date.now()}`,
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        pickup: { name: 'Test Pickup Location', lat: 6.5244, lng: 3.3792 },
        dropoff: { name: 'Test Dropoff Location', lat: 6.4474, lng: 3.3903 },
        requestedAt: new Date().toISOString(),
        modeOfPayment: 'Card',
      }
    );
  };

  const testBookingAccepted = () => {
    if (!activeTrip?.id || !currentUser) return;
    
    realtimeNotificationHelpers.notifyBookingAccepted(
      activeTrip.id,
      `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
      {
        id: activeTrip.id,
        driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
        origin: activeTrip.origin,
        destination: activeTrip.destination,
        timestamp: activeTrip.timestamp,
        pricePerSeat: activeTrip.pricePerSeat,
      }
    );
  };

  const testBookingDeclined = () => {
    if (!activeTrip?.id || !currentUser) return;
    
    realtimeNotificationHelpers.notifyBookingDeclined(
      activeTrip.id,
      `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
      {
        id: activeTrip.id,
        driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
        origin: activeTrip.origin,
        destination: activeTrip.destination,
        timestamp: activeTrip.timestamp,
        pricePerSeat: activeTrip.pricePerSeat,
      }
    );
  };

  const testTripStarted = () => {
    if (!activeTrip?.id || !currentUser) return;

    realtimeNotificationHelpers.notifyTripStarted(
      activeTrip.id,
      `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
      {
        id: activeTrip.id,
        driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
        origin: activeTrip.origin,
        destination: activeTrip.destination,
        timestamp: activeTrip.timestamp,
        pricePerSeat: activeTrip.pricePerSeat,
      }
    );

    // Note: Direct event publishing would require ablyRef access
    // The main trip start functionality in TripSummary handles direct events
  };

  const testLocalNotification = () => {
    if (!activeTrip?.id || !currentUser) return;
    
    notificationHelpers.showCustomModal(
      '🧪 Test Notification',
      'This is a test notification to verify the modal system is working correctly.',
      'all',
      activeTrip.id
    );
  };

  if (!activeTrip || !currentUser) {
    return (
      <View className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mx-4 mb-4">
        <Text className="text-yellow-800 text-sm font-medium">
          ⚠️ Notification Tester: No active trip or user found
        </Text>
        <Text className="text-yellow-600 text-xs mt-1">
          You need an active trip to test real-time notifications
        </Text>
      </View>
    );
  }

  return (
    <View className="bg-blue-50 border border-blue-200 rounded-md p-4 mx-4 mb-4">
      <Text className="text-blue-800 text-sm font-semibold mb-3">
        🧪 Notification System Tester
      </Text>
      
      <View className="space-y-2">
        <TouchableOpacity
          onPress={testRideRequest}
          className="bg-blue-500 py-2 px-3 rounded-md"
        >
          <Text className="text-white text-xs font-medium text-center">
            Test Ride Request (Driver)
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testBookingAccepted}
          className="bg-green-500 py-2 px-3 rounded-md"
        >
          <Text className="text-white text-xs font-medium text-center">
            Test Booking Accepted (Passenger)
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testBookingDeclined}
          className="bg-red-500 py-2 px-3 rounded-md"
        >
          <Text className="text-white text-xs font-medium text-center">
            Test Booking Declined (Passenger)
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testTripStarted}
          className="bg-purple-500 py-2 px-3 rounded-md"
        >
          <Text className="text-white text-xs font-medium text-center">
            Test Trip Started (Both)
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testLocalNotification}
          className="bg-gray-500 py-2 px-3 rounded-md"
        >
          <Text className="text-white text-xs font-medium text-center">
            Test Local Modal
          </Text>
        </TouchableOpacity>
      </View>
      
      <Text className="text-blue-600 text-xs mt-2">
        Active Trip: {activeTrip.id} | User: {currentUser.firstName}
      </Text>
    </View>
  );
};

export default RoleBasedNotificationTester;
