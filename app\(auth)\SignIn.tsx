import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from "react-native";
import React, { useEffect, useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { Href, router, Link, usePathname } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import * as SecureStore from "expo-secure-store";
import { z } from "zod";
import SocialSignIn from "@/components/SocialSignIn";
import useCurrentUser from "@/hooks/useCurrentUser";

const loginSchema = z.object({
  emailOrPhone: z.string().refine((value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\+?[1-9]\d{1,14}$/; // Basic regex for international phone numbers
    return emailRegex.test(value) || phoneRegex.test(value);
  }, "Invalid email address or phone number"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function SignIn() {
  const { isLoading: userDetailsLoading } = useCurrentUser();
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [formState, setFormState] = useState<LoginForm>({
    emailOrPhone: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const pathname = usePathname();

  const [errors, setErrors] = useState<Partial<Record<keyof LoginForm, string[]>>>({});
  const [isFormValid, setIsFormValid] = useState(false);

  const updateField = (value: string, key: keyof LoginForm) => {
    setFormState((prev) => ({ ...prev, [key]: value }));
  };

  useEffect(() => {
    const result = loginSchema.safeParse(formState);
    if (result.success) {
      setErrors({});
      setIsFormValid(true);
    } else {
      setErrors(result.error.flatten().fieldErrors);
      setIsFormValid(false);
    }
  }, [formState]);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const tokenData = await SecureStore.getItemAsync("authToken");
        if (tokenData) {
          const { expiration } = JSON.parse(tokenData);
          const currentTime = new Date().getTime();
          setIsSignedIn(currentTime < expiration);
        }
      } catch (error) {
        console.error("Auth check error:", error);
        setIsSignedIn(false);
      }
    };
    checkAuth();
  }, []);

  const { mutate, isPending } = useMutation({
    mutationFn: services.signIn,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      // Calculate expiration time (current time + 72 hours)
      const expirationTime = new Date().getTime() + 72 * 60 * 60 * 1000;
      const tokenData = JSON.stringify({
        token: data.data.token,
        expiration: expirationTime,
      });

      // Store token and expiration time in SecureStore
      await SecureStore.setItemAsync("authToken", tokenData);

      router.replace("/(tabs)/Home");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error signing in",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : -500}
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{
            flexGrow: 1,
            paddingHorizontal: 20,
          }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1">
            <AuthHeader
              header="Welcome back"
              subHeader="Log in with your email address phone "
              subbHeader="number"
            />

            <View className="mt-10">
              <Input
                text="Email address or Phone number"
                type="text"
                placeHolder="<EMAIL>"
                value={formState.emailOrPhone}
                error={errors.emailOrPhone?.[0]}
                setValue={updateField}
                ObjKey="emailOrPhone"
              />
              <View className="h-2" />
              <Input
                text="Password"
                type="password"
                placeHolder="Enter your password"
                value={formState.password}
                setValue={updateField}
                showPassword={showPassword}
                setShowPassword={setShowPassword}
                error={errors.password?.[0]}
                ObjKey="password"
              />
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => router.push("/ForgotPassword" as Href)}
              >
                <Text className="text-[#473BF0] font-medium text-[15px] mt-3">
                  Forgot password
                </Text>
              </TouchableOpacity>

              <View className="mt-6">
                <Button
                  buttonDisabled={!formState.emailOrPhone || !formState.password}
                  isLoading={isPending}
                  onClick={() => {
                    if (isFormValid) {
                      Keyboard.dismiss();
                      console.log('User data loading state:', userDetailsLoading);
                      console.log('Authentication state:', isSignedIn);
                      console.log('Current route:', pathname);
                      console.log("Google Maps API Key:", process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY);
                      mutate({
                        dataBody: {
                          email: formState.emailOrPhone,
                          password: formState.password,
                        },
                      });
                    }
                  }}
                  text="Log in"
                  textClassName="text-white"
                  buttonClassName="bg-[#473BF0] border border-[#FFFFFF4D]"
                />
              </View>
            </View>
          </View>

          {/* Footer Section */}
          <View className="mt-auto pt-6">
            <View className="flex-row justify-center items-center">
              <Text className="text-center text-lg font-medium">
                Don't have an account?{" "}
              </Text>
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => router.push("/SignUp")}
              >
                <Text className="text-[#473BF0] text-lg">Sign up</Text>
              </TouchableOpacity>
            </View>

            <Text className="text-[#747474] text-center text-lg mt-4 mb-6 px-4">
              By signing up, you agree to our{" "}
              <Link href={"/"} className="underline">
                Terms & Condition
              </Link>
              , acknowledge our{" "}
              <Link href={"/"} className="underline">
                Privacy Policy
              </Link>{" "}
              and confirm that you're over 18.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
