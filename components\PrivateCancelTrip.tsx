import React, { useRef, useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { extras } from "@/utils/data";
import { Href, router } from "expo-router";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}

const PrivateCancelTrip: React.FC<Props> = ({ step, setStep }) => {
  const bottomSheetRef = useRef<BottomSheet>(null);

  const customSnapPoints = ["32%"];

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 justify-center w-full bg-[#FFFFFF]">
        <View className="w-[90%] mx-auto items-center gap-y-3">
          <Image
            source={require("../assets/images/delete.png")}
            className="h-[46px] w-[46px]"
            resizeMode="contain"
          />

          <Text className="text-[#151B2D] text-[20px] font-semibold">
            Cancel trip?
          </Text>
          <Text className="text-[#151B2D] text-[15px]">
            Are you sure you want to cancel
          </Text>
          <View className="w-full">
            <Button
              text="Cancel"
              textClassName="text-[#E05859]"
              buttonClassName="bg-[#********] w-full mb-2"
              onClick={() => router.push("/PrivateDriver" as Href)}
            />
            <Button
              text="Back"
              textClassName="text-[#151B2D]"
              buttonClassName="bg-[#EDEDED] w-full mb-2"
              onClick={() => setStep(step - 1)}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PrivateCancelTrip;
