import GoBack from "@/components/GoBack";
import { Slot, Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: { backgroundColor: "#ffffff" },
        headerTintColor: "#000000",
        headerShadowVisible: true,
      }}
    >
      <Stack.Screen
        name="Onboarding"
        options={{ headerShown: false, title: "Onboarding" }}
      />
      <Stack.Screen
        name="SignIn"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="SignUp"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="ForgotPassword"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="VerifyOTP"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="ChangePassword"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="PasswordChanged"
        options={{ headerShown: false, title: "Password Changed" }}
      />
      <Stack.Screen
        name="VerifyPhone"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="ProfileSetUp"
        options={{
          headerShown: true,
          title: "",
          headerLeft: () => <GoBack />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="PersonalInfo"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "Personal Info",
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
    </Stack>
  );
}
