import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import Button from "@/components/Button";
import { router, useLocalSearchParams } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import { StyleSheet } from "react-native";
import { useMutation, useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { OtpInput } from "react-native-otp-entry";
import useCurrentUser from "@/hooks/useCurrentUser";

export default function VerifyOTP() {
  const [otp, setOtp] = useState("");
  const { data } = useCurrentUser();

  const { emailOrPhone } = useLocalSearchParams();
  const emailString = Array.isArray(emailOrPhone)
    ? emailOrPhone[0]
    : emailOrPhone;

  const [countDown, setCountdown] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);

  const startCountdown = () => {
    setIsResendDisabled(true);
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(interval);
          setCountdown(60);
          setIsResendDisabled(false);
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    startCountdown();
  }, []);

  const { refetch, isLoading } = useQuery({
    queryKey: ["email"],
    queryFn: () => services.requestPasswordReset(emailString),
    enabled: false, // Prevents query from running automatically
    staleTime: 0,
  });

  const handlePasswordReset = async () => {
    startCountdown();
    await refetch();
  };

  const { mutate: verifyOTp, isPending: otpVerificationPending } = useMutation({
    mutationFn: services.verifyPasswordResetOtp,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "OTP Verified",
      });
      router.push({
        pathname: "/ResetPassword",
        params: { token: data.token },
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            {/* <GoBack /> */}
            <AuthHeader
              header="Verify OTP to reset password"
              subHeader={`Enter the OTP sent to ${emailString} check the spam folder just in case.`}
            />
            <View className="py-4">
              <OtpInput
                numberOfDigits={4}
                onTextChange={(code) => setOtp(code)}
                focusStickBlinkingDuration={500}
                theme={{
                  pinCodeContainerStyle: styles.underlineStyleBase,
                  pinCodeTextStyle: styles.pinCodeText,
                  focusedPinCodeContainerStyle:
                    styles.underlineStyleHighLighted,
                }}
                focusColor="black"
                onFilled={(code) => {
                  verifyOTp({ dataBody: { email: emailString, pin: code } });
                }}
              />
            </View>
            <View>
              {isResendDisabled ? (
                <Text className="text-[#787A80]">
                  Didn't get code? Resend in{" "}
                  <Text className="text-[#151B2D] font-medium">
                    {countDown}
                  </Text>
                </Text>
              ) : (
                <View className="items-center flex-row">
                  <Text>Didn't get code? </Text>
                  <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={handlePasswordReset}
                  >
                    <Text className="text-[#473BF0] font-medium">
                      {isLoading ? "Please wait" : "Resend"}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                isLoading={otpVerificationPending}
                buttonDisabled={otpVerificationPending}
                text="Verify"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() =>
                  verifyOTp({
                    dataBody: { email: data.email, pin: otp },
                  })
                }
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  underlineStyleBase: {
    width: 78,
    height: 48,
    borderWidth: 0,
    backgroundColor: "#F6F6F6",
    color: "#151B2D",
    fontWeight: "500",
    fontSize: 20,
    borderRadius: 8,
  },
  underlineStyleHighLighted: {
    borderColor: "#473BF0",
    borderWidth: 1,
    backgroundColor: "#FFFFFF",
  },
  pinCodeText: {
    fontSize: 20,
    color: "#151B2D", // Text color (if text is added)
  },
});
