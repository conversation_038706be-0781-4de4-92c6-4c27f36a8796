import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';

interface RideRequestData {
  id: string;
  passengerName: string;
  pickup: {
    name: string;
    lat: number;
    lng: number;
  };
  dropoff: {
    name: string;
    lat: number;
    lng: number;
  };
  requestedAt: string;
  modeOfPayment: string;
  pricePerSeat?: number;
}

interface RideRequestNotificationProps {
  request: RideRequestData;
  tripId: string;
  tripData?: {
    timestamp: string;
    pricePerSeat: number;
  };
  fullTripData?: any; // Complete trip object for navigation
  onAccept?: (requestId: string) => void;
  onDecline?: (requestId: string) => void;
  timestamp?: string;
}

const RideRequestNotification: React.FC<RideRequestNotificationProps> = ({
  request,
  tripId,
  tripData,
  fullTripData,
  onAccept,
  onDecline,
  timestamp
}) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const handleViewTrip = () => {
    // Use full trip data if available, otherwise fallback to just ID
    const tripDataToPass = fullTripData || { id: tripId };

    router.push({
      pathname: "/TripSummary",
      params: {
        trip: encodeURIComponent(JSON.stringify(tripDataToPass)),
      },
    });
  };

  return (
    <TouchableOpacity onPress={handleViewTrip} className="flex w-full flex-col bg-[#FFF] rounded-md p-4 mx-4 mb-[12px]">
      {/* Header */}
      <View className="flex flex-row justify-between items-center mb-[6px]">
        <Text className="text-[#3A2EE5] text-[12px] font-semibold">Request</Text>
      </View>

      {/* Main Content */}
      <View className="">

        <View className="flex flex-row justify-between items-center mb-[4px]">
        <Text className="text-base font-semibold text-[#151B2D]">
          {request.passengerName} wants to join your trip
        </Text>
        <View className="flex flex-row items-center gap-1">
          <Text className="text-[#787A80] text-xs font-normal">
            {timestamp ? formatTime(timestamp) : formatTime(request.requestedAt)}
          </Text>
          <Image 
            source={require("../assets/images/goNew.png")} 
            className="w-2 h-3" 
          />
        </View>
      </View>

        {/* Route Information */}
        <View className='flex flex-row mb-[4px] items-center justify-between'>
          <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm font-medium">
            {request.pickup.name || 'Unknown origin'}
          </Text>
          <Image source={require("../assets/images/fromandtoNew.png")} className="mx-2 w-3 h-3" />
          <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm text-right font-medium">
            {request.dropoff.name || 'Unknown destination'}
          </Text>
        </View>

        {/* Trip Details */}
        <View className="flex-row mb-2 items-center space-x-2">
          <View className='gap-1 flex-row items-center'>
            <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4' />
            <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
          </View>
          <View className='h-1 w-1 bg-[#787A80] rounded-full' />
          <View className='flex-row items-center'>
            <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
              {tripData?.timestamp ? new Date(tripData.timestamp).toLocaleDateString([], {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'} -
            </Text>
            <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
              {tripData?.timestamp ? new Date(tripData.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A'}
            </Text>
          </View>
          <View className='h-1 w-1 bg-[#787A80] rounded-full' />
          <View>
            <Text className="text-sm font-medium text-[#787A80]">₦{tripData?.pricePerSeat || '0'}</Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      {/* <View className="flex flex-row gap-3">
        {onDecline && (
          <TouchableOpacity
            onPress={() => onDecline(request.id)}
            className="flex-1 bg-gray-100 py-2 px-4 rounded-md"
          >
            <Text className="text-center text-gray-700 font-medium">Decline</Text>
          </TouchableOpacity>
        )}
        
        {onAccept && (
          <TouchableOpacity
            onPress={() => onAccept(request.id)}
            className="flex-1 bg-[#473BF0] py-2 px-4 rounded-md"
          >
            <Text className="text-center text-white font-medium">Accept</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={handleViewTrip}
          className="bg-[#F8F9FA] py-2 px-4 rounded-md"
        >
          <Text className="text-center text-[#473BF0] font-medium">View Trip</Text>
        </TouchableOpacity>
      </View> */}
    </TouchableOpacity>
  );
};

export default RideRequestNotification;
