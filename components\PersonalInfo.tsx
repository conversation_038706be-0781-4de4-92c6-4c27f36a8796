import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import { Href, router } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";
import PrivateTipMode from "./PrivateTipMode";

const PersonalInfo = () => {
  const { data: user } = useCurrentUser();
  return (
    <View className="bg-white rounded-[9px] p-3">
      <View className="flex-row justify-between items-center">
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => router.push("/(profile)/UserProfileScreen" as Href)}
          className="flex-row items-center"
        >
          <Image
            source={{ uri: user?.profilePicture || "data:image/jpeg;base64,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" }}
            className="w-14 h-14 rounded-full mb-3"
            resizeMode="cover"
          />

          <View className="ml-2 gap-y-[2px]">
            <Text className="text-[#151B2D] text-[18px] font-semibold">
              {user?.firstName} {user?.lastName}
            </Text>
            <Text className="text-[#151B2D] text-[12px]">{user?.email}</Text>
          </View>
        </TouchableOpacity>
        {user?.isVerified && (
          <View className="flex-row items-center bg-[#34A8531A] rounded-[100px] px-[4px] py-[2px]">
            <Text className="mr-1 text-[#34A853] text-[10px] font-semibold">
              Verified
            </Text>
            <Image
              source={require("../assets/images/check_circle.png")}
              className="w-[10px] h-[10px]"
              resizeMode="contain"
            />
          </View>
        )}

      </View>

      {!user?.isVerified && (
        <View className="w-full bg-[#473BF01A] flex-row space-x-1 items-center border border-[#473BF0] rounded-[6px] h-[39px] px-1 mt-3">
          <Image
            source={require("../assets/images/safe_lock_fill.png")}
            className="w-[20px] h-[20px]"
            resizeMode="contain"
          />
          <Text className="text-[11px] text-[#473BF0] font-semibold">Please verify your account for better safety during rides</Text>
        </View>
      )}
      {/* <PrivateTipMode /> */}

      <View className="mt-6">
        <Text className="text-[#4A4C50] text-[14px] font-medium opacity-50">Profile</Text>
        <View className="mt-5">

          <View className="flex-row justify-between items-center">
            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => router.push("/(profile)/EditProfileScreen" as Href)}
              className="flex-row items-center"
            >
              <Image
                source={require("../assets/images/user.png")}
                className="w-[30px] h-[30px]"
                resizeMode="contain"
              />
              <Text className="ml-3 text-[#151B2D] text-[14px]">
                Edit Profile
              </Text>
            </TouchableOpacity>
            <Image
              source={require("../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </View>

          <View className="w-full h-[1px] my-2 bg-white-100" />

          <View className="flex-row justify-between items-center">
            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => router.push("/(profile)/Security" as Href)}
              className="flex-row items-center"
            >
              <Image
                source={require("../assets/images/security.png")}
                className="w-[30px] h-[30px]"
                resizeMode="contain"
              />
              <Text className="ml-3 text-[#151B2D] text-[14px]">
                Security & Log in
              </Text>
            </TouchableOpacity>
            <Image
              source={require("../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </View>
        </View>

        <View className="w-full h-[1px] my-2 bg-white-100" />

        <View className="flex-row justify-between items-center">
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => router.push("/(profile)/Notification" as Href)}
            className="flex-row items-center"
          >
            <Image
              source={require("../assets/images/notif.png")}
              className="w-[30px] h-[30px]"
              resizeMode="contain"
            />
            <Text className="ml-3 text-[#151B2D] text-[14px]">
              Notification
            </Text>
          </TouchableOpacity>
          <Image
            source={require("../assets/images/right_line.png")}
            className="w-[18px] h-[18px]"
            resizeMode="contain"
          />
        </View>
      </View>


    </View>
  );
};

export default PersonalInfo;
