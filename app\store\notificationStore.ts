import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NotificationData {
  id: string;
  type: 'trip_started' | 'pickup_confirmed' | 'trip_ended' | 'payment_request' | 'trip_cancelled' | 'custom';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  read: boolean;
  targetRole?: 'driver' | 'passenger' | 'all';
  tripId?: string;
  userId?: string;
}

export interface ModalData {
  id: string;
  type: 'trip_update' | 'payment' | 'confirmation' | 'alert' | 'custom';
  title: string;
  content: string;
  buttons?: Array<{
    text: string;
    action: 'close' | 'navigate' | 'custom';
    actionData?: any;
    style?: 'default' | 'destructive' | 'primary';
  }>;
  targetRole?: 'driver' | 'passenger' | 'all';
  tripId?: string;
  userId?: string;
  autoClose?: number; // Auto close after X milliseconds
  persistent?: boolean; // Whether modal should persist across app restarts
}

interface NotificationState {
  // Notifications
  notifications: NotificationData[];
  unreadCount: number;
  
  // Active Modals
  activeModals: ModalData[];
  
  // User Role Context
  currentUserRole: 'driver' | 'passenger' | null;
  currentTripId: string | null;
  currentUserId: string | null;
  
  // Actions
  addNotification: (notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  markAllNotificationsRead: () => void;
  clearNotifications: () => void;
  
  showModal: (modal: Omit<ModalData, 'id'>) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  setUserContext: (role: 'driver' | 'passenger' | null, tripId: string | null, userId: string | null) => void;
  
  // Filtered getters
  getNotificationsForUser: () => NotificationData[];
  getModalsForUser: () => ModalData[];
}

export const useNotificationStore = create<NotificationState>()(
  persist(
    (set, get) => ({
      notifications: [],
      unreadCount: 0,
      activeModals: [],
      currentUserRole: null,
      currentTripId: null,
      currentUserId: null,
      
      addNotification: (notification) => {
        const newNotification: NotificationData = {
          ...notification,
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          read: false,
        };
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1,
        }));
      },
      
      markNotificationRead: (id) => {
        set((state) => ({
          notifications: state.notifications.map((notif) =>
            notif.id === id ? { ...notif, read: true } : notif
          ),
          unreadCount: Math.max(0, state.unreadCount - 1),
        }));
      },
      
      markAllNotificationsRead: () => {
        set((state) => ({
          notifications: state.notifications.map((notif) => ({ ...notif, read: true })),
          unreadCount: 0,
        }));
      },
      
      clearNotifications: () => {
        set({ notifications: [], unreadCount: 0 });
      },
      
      showModal: (modal) => {
        const newModal: ModalData = {
          ...modal,
          id: `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };
        
        set((state) => ({
          activeModals: [...state.activeModals, newModal],
        }));
        
        // Auto close if specified
        if (modal.autoClose) {
          setTimeout(() => {
            get().closeModal(newModal.id);
          }, modal.autoClose);
        }
      },
      
      closeModal: (id) => {
        set((state) => ({
          activeModals: state.activeModals.filter((modal) => modal.id !== id),
        }));
      },
      
      closeAllModals: () => {
        set({ activeModals: [] });
      },
      
      setUserContext: (role, tripId, userId) => {
        set({
          currentUserRole: role,
          currentTripId: tripId,
          currentUserId: userId,
        });
      },
      
      getNotificationsForUser: () => {
        const state = get();
        return state.notifications.filter((notif) => {
          // Show all notifications if no role specified
          if (!notif.targetRole || notif.targetRole === 'all') return true;
          
          // Show notifications for current user role
          if (notif.targetRole === state.currentUserRole) return true;
          
          // Show notifications for specific user
          if (notif.userId && notif.userId === state.currentUserId) return true;
          
          // Show notifications for current trip
          if (notif.tripId && notif.tripId === state.currentTripId) return true;
          
          return false;
        });
      },
      
      getModalsForUser: () => {
        const state = get();

        console.log('NotificationStore - getModalsForUser:', {
          currentUserRole: state.currentUserRole,
          currentTripId: state.currentTripId,
          currentUserId: state.currentUserId,
          totalModals: state.activeModals.length,
          modals: state.activeModals.map(m => ({
            id: m.id,
            type: m.type,
            targetRole: m.targetRole,
            tripId: m.tripId,
            userId: m.userId
          }))
        });

        const filteredModals = state.activeModals.filter((modal) => {
          // Show all modals if no role specified
          if (!modal.targetRole || modal.targetRole === 'all') {
            console.log(`Modal ${modal.id} - showing (no role or all)`);
            return true;
          }

          // Show modals for current user role
          if (modal.targetRole === state.currentUserRole) {
            console.log(`Modal ${modal.id} - showing (role match: ${modal.targetRole})`);
            return true;
          }

          // Show modals for specific user
          if (modal.userId && modal.userId === state.currentUserId) {
            console.log(`Modal ${modal.id} - showing (user match)`);
            return true;
          }

          // Show modals for current trip
          if (modal.tripId && modal.tripId === state.currentTripId) {
            console.log(`Modal ${modal.id} - showing (trip match)`);
            return true;
          }

          console.log(`Modal ${modal.id} - filtered out (no match)`);
          return false;
        });

        console.log('NotificationStore - filtered modals:', filteredModals.length);
        return filteredModals;
      },
    }),
    {
      name: 'notification-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        notifications: state.notifications,
        unreadCount: state.unreadCount,
        activeModals: state.activeModals.filter(modal => modal.persistent),
      }),
    }
  )
);
