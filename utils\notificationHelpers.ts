import { useNotificationStore } from '@/app/store/notificationStore';
import { pushNotificationHelpers } from './pushNotificationService';

// Helper functions for common notification scenarios
export const notificationHelpers = {
  // Trip-related notifications
  notifyTripStarted: (tripId: string, driverName: string) => {
    console.log('NotificationHelpers - notifyTripStarted called:', { tripId, driverName });

    // Use push notification instead of modal
    pushNotificationHelpers.notifyTripStarted(tripId, driverName);

    // Still add to notification store for history
    const { addNotification } = useNotificationStore.getState();
    addNotification({
      type: 'trip_started',
      title: 'Trip Started',
      message: `${driverName} has started the trip`,
      targetRole: 'passenger',
      tripId,
    });
  },

  notifyPickupConfirmed: (tripId: string, passengerName: string) => {
    console.log('NotificationHelpers - notifyPickupConfirmed called:', { tripId, passengerName });

    const { addNotification, showModal } = useNotificationStore.getState();

    // Notification for driver
    addNotification({
      type: 'pickup_confirmed',
      title: 'Pickup Confirmed',
      message: `${passengerName}'s pickup has been confirmed`,
      targetRole: 'driver',
      tripId,
    });

    // Modal for passengers
    console.log('NotificationHelpers - Creating passenger modal for pickup confirmed');
    showModal({
      type: 'trip_update',
      title: '✅ Pickup Confirmed',
      content: `Your pickup has been confirmed by the driver. The trip will begin shortly.`,
      targetRole: 'passenger',
      tripId,
      autoClose: 4000,
      buttons: [
        {
          text: 'OK',
          action: 'close',
          style: 'primary',
        },
      ],
    });
  },

  notifyTripEnded: (tripId: string, driverName: string) => {
    const { addNotification, showModal } = useNotificationStore.getState();
    
    // Notification for passengers
    addNotification({
      type: 'trip_ended',
      title: 'Trip Completed',
      message: 'Your trip has been completed',
      targetRole: 'passenger',
      tripId,
    });

    // Modal for passengers
    showModal({
      type: 'trip_update',
      title: '🎉 Trip Completed',
      content: 'Your trip has been completed successfully. Please proceed with payment if required.',
      targetRole: 'passenger',
      tripId,
      buttons: [
        {
          text: 'Rate Trip',
          action: 'navigate',
          actionData: { route: '/RateTrip' },
          style: 'primary',
        },
        {
          text: 'OK',
          action: 'close',
          style: 'default',
        },
      ],
    });
  },

  requestPayment: (tripId: string, amount: number, passengerName: string) => {
    const { addNotification, showModal } = useNotificationStore.getState();
    
    // Notification for passenger
    addNotification({
      type: 'payment_request',
      title: 'Payment Required',
      message: `Payment of ₦${amount} is required for your trip`,
      targetRole: 'passenger',
      tripId,
    });

    // Modal for passenger
    showModal({
      type: 'payment',
      title: '💳 Payment Required',
      content: `Please complete your payment of ₦${amount} for the trip.`,
      targetRole: 'passenger',
      tripId,
      persistent: true, // Don't auto-close payment modals
      buttons: [
        {
          text: 'Pay Now',
          action: 'navigate',
          actionData: { route: '/Payment' },
          style: 'primary',
        },
        {
          text: 'Later',
          action: 'close',
          style: 'default',
        },
      ],
    });
  },

  notifyTripCancelled: (tripId: string, cancelledBy: string, reason?: string) => {
    const { addNotification, showModal } = useNotificationStore.getState();
    
    // Notification for all users in the trip
    addNotification({
      type: 'trip_cancelled',
      title: 'Trip Cancelled',
      message: `Trip has been cancelled by ${cancelledBy}`,
      targetRole: 'all',
      tripId,
    });

    // Modal for all users
    showModal({
      type: 'alert',
      title: '❌ Trip Cancelled',
      content: `This trip has been cancelled by ${cancelledBy}.${reason ? ` Reason: ${reason}` : ''}`,
      targetRole: 'all',
      tripId,
      buttons: [
        {
          text: 'Find Another Trip',
          action: 'navigate',
          actionData: { route: '/(tabs)/Home' },
          style: 'primary',
        },
        {
          text: 'OK',
          action: 'close',
          style: 'default',
        },
      ],
    });
  },

  // Custom notification/modal
  showCustomModal: (
    title: string,
    content: string,
    targetRole: 'driver' | 'passenger' | 'all',
    tripId?: string,
    buttons?: Array<{
      text: string;
      action: 'close' | 'navigate' | 'custom';
      actionData?: any;
      style?: 'default' | 'destructive' | 'primary';
    }>
  ) => {
    const { showModal } = useNotificationStore.getState();
    
    showModal({
      type: 'custom',
      title,
      content,
      targetRole,
      tripId,
      buttons: buttons || [
        {
          text: 'OK',
          action: 'close',
          style: 'primary',
        },
      ],
    });
  },

  // Clear all notifications for current user
  clearUserNotifications: () => {
    const { clearNotifications } = useNotificationStore.getState();
    clearNotifications();
  },

  // Close all modals
  closeAllModals: () => {
    const { closeAllModals } = useNotificationStore.getState();
    closeAllModals();
  },
};
