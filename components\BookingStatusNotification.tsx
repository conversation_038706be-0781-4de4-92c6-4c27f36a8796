import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';

interface TripDetails {
  id: string;
  driverName: string;
  origin: {
    name: string;
    lat: number;
    lng: number;
  };
  destination: {
    name: string;
    lat: number;
    lng: number;
  };
  timestamp: string;
  pricePerSeat: number;
}

interface BookingStatusNotificationProps {
  status: 'accepted' | 'declined';
  tripDetails: TripDetails;
  fullTripData?: any; // Complete trip object for navigation
  timestamp?: string;
  onCancel?: () => void;
}

const BookingStatusNotification: React.FC<BookingStatusNotificationProps> = ({
  status,
  tripDetails,
  fullTripData,
  timestamp,
  onCancel
}) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const formatTripTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: true 
    });
  };

  const handleViewTrip = () => {
    // Only navigate if the booking was accepted
    if (status === 'accepted') {
      // Use full trip data if available, otherwise fallback to tripDetails
      const tripDataToPass = fullTripData || tripDetails;

      router.push({
        pathname: "/(ride)/TripSummary",
        params: {
          trip: encodeURIComponent(JSON.stringify(tripDataToPass)),
        },
      });
    }
    // If declined, do nothing (no navigation)
  };

  const getStatusColor = () => {
    return status === 'accepted' ? '#10B981' : '#EF4444';
  };

  const getStatusIcon = () => {
    return status === 'accepted' ? '✅' : '❌';
  };

  const getStatusText = () => {
    return status === 'accepted' 
      ? `Your booking was accepted by ${tripDetails.driverName}` 
      : `Your booking was declined by ${tripDetails.driverName}`;
  };

  return (
    <TouchableOpacity
      onPress={handleViewTrip}
      disabled={status !== 'accepted'}
      className={`flex w-full flex-col bg-[#FFF] rounded-md p-4 mx-4 mb-[12px] ${status === 'accepted' ? 'opacity-100' : 'opacity-90'}`}
    >
          <View className="flex flex-row justify-between items-center mb-[6px]">
            <Text className="text-[#8388A2] text-xs font-semibold">Update</Text>
          </View>
    
          <View className="">
            <View className="flex flex-row justify-between items-center mb-[4px]">
            <View className='flex-row items-center'>
                <Text className="text-base font-semibold text-[#151B2D]">
                  Your booking has been {status === 'accepted' ? "accepted" : "declined"}
                </Text>
              </View>
            <View className="flex flex-row items-center gap-1">
              <Text className="text-[#787A80] text-xs font-normal">
                {timestamp ? formatTime(timestamp) : 'Just now'}
              </Text>
              <Image 
                source={require("../assets/images/goNew.png")} 
                className="w-2 h-3" 
              />
            </View>
          </View>
            
            
            <View className='flex flex-row mb-[4px] items-center justify-between'>
                            <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm font-medium">
                              {tripDetails.origin.name || 'Unknown origin'}
                            </Text>
                            <Image source={require("../assets/images/fromandtoNew.png")} className="mx-2 w-3 h-3" />
                            <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm text-right font-medium">
                              {tripDetails.destination.name || 'Unknown destination'}
                            </Text>
                          </View>
                  
                          {/* Trip Details */}
                          <View className="flex-row mb-2 items-center space-x-2">
                            <View className='gap-1 flex-row items-center'>
                              <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4' />
                              <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
                            </View>
                            <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                            <View className='flex-row items-center'>
                              <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                                {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleDateString([], {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'} -
                              </Text>
                              <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                                {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A'}
                              </Text>
                            </View>
                            <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                            <View>
                              <Text className="text-sm font-medium text-[#787A80]">₦{tripDetails?.pricePerSeat || '0'}</Text>
                            </View>
                          </View>
          </View>
    
          
        </TouchableOpacity>
  );
};

export default BookingStatusNotification;
