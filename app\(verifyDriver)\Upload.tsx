import { View, Text, Image, StatusBar, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import ProgressComponent from "@/components/ProgressComponent";
import VehicleDetails from "@/components/VehicleDetails";
import UploadDocuments from "@/components/UploadDocuments";
import { router } from "expo-router";
import UploadDocuments2 from "@/components/UploadDocuments2";
import VerifyIdentity from "@/components/VerifyIdentity";
import { SafeAreaView } from "react-native-safe-area-context";

const Upload = () => {
  const [step, setStep] = useState(0);
  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <View className="w-[90%] h-full mx-auto">
        <View className="flex-row items-center justify-between relative mt-3">
          <TouchableOpacity
            onPress={() => {
              step === 0 ? router.back() : setStep(step - 1);
            }}
            className="bg-[#F6F6F6] rounded-[6px] p-2 w-[10%]"
          >
            <Image
              source={require("../../assets/images/Back.png")}
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
          <ProgressComponent step={step} />
        </View>
        <Text className="text-[#151B2D] text-[24px] font-semibold my-3">
          Upload Documents
        </Text>

        {step === 0 && <VehicleDetails step={step} setStep={setStep} />}
        {step === 1 && <UploadDocuments step={step} setStep={setStep} />}
        {step === 2 && <UploadDocuments2 step={step} setStep={setStep} />}
        {step === 3 && <VerifyIdentity step={step} setStep={setStep} />}
      </View>
    </SafeAreaView>
  );
};

export default Upload;
