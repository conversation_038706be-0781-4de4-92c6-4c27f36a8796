{"name": "co<PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@clerk/clerk-expo": "^2.2.26", "@expo/vector-icons": "^14.0.3", "@gorhom/bottom-sheet": "^4.6.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/drawer": "^7.0.0", "@react-navigation/native": "^7.0.0", "@stripe/stripe-react-native": "0.38.6", "@tanstack/react-query": "^5.56.2", "@twotalltotems/react-native-otp-input": "^1.3.11", "ably": "^2.4.1", "axios": "^1.7.7", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "expo": "^52.0.0", "expo-apple-authentication": "~7.1.3", "expo-auth-session": "~6.0.1", "expo-constants": "~17.0.3", "expo-crypto": "~14.0.1", "expo-dev-client": "~5.0.6", "expo-device": "~7.0.1", "expo-font": "~13.0.1", "expo-image-picker": "~16.0.3", "expo-linking": "~7.0.3", "expo-location": "~18.0.4", "expo-notifications": "~0.29.11", "expo-random": "~14.0.1", "expo-router": "4.0.11", "expo-secure-store": "~14.0.0", "expo-splash-screen": "~0.29.18", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.6", "expo-updates": "~0.26.10", "expo-web-browser": "~14.0.1", "nativewind": "^2.0.11", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.5", "react-native-country-picker-modal": "^2.0.0", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.18.0", "react-native-maps-directions": "^1.9.0", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-otp-entry": "^1.7.2", "react-native-phone-input": "^1.3.7", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.7", "@types/uuid": "^10.0.0", "expo-module-scripts": "^4.0.3", "jest": "^29.2.1", "jest-expo": "~52.0.2", "react-test-renderer": "18.2.0", "tailwindcss": "3.3.2", "typescript": "~5.3.3"}, "private": true}