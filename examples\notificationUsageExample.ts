/**
 * COMPREHENSIVE NOTIFICATION USAGE EXAMPLES
 * 
 * This file shows how to use the new role-based and time-based notification system
 * in your CoRide app. Copy these examples into your actual components.
 */

import { tripNotificationHelpers } from '../utils/tripNotificationManager';
import { pushNotificationHelpers } from '../utils/pushNotificationService';

// Example trip data structure
const exampleTrip = {
  id: "trip_123",
  origin: { name: "Downtown Mall, City Center" },
  destination: { name: "Airport Terminal, International Airport" },
  timestamp: "2024-01-15T14:30:00Z", // Trip starts at 2:30 PM
  driver: { id: "driver_456", firstName: "<PERSON>", lastName: "Doe" },
  passengers: [
    { id: "passenger_789", firstName: "<PERSON>", lastName: "Smith" },
    { id: "passenger_101", firstName: "Bob", lastName: "Johnson" }
  ],
  price: "$25"
};

const currentUser = { id: "passenger_789", firstName: "<PERSON>", lastName: "<PERSON>" };

/**
 * 1. INITIALIZE NOTIFICATIONS (Call this in your App.tsx or main component)
 */
export const initializeNotifications = async () => {
  try {
    await tripNotificationHelpers.initialize();
    console.log('✅ Notification system ready');
  } catch (error) {
    console.error('❌ Failed to initialize notifications:', error);
  }
};

/**
 * 2. SCHEDULE TRIP REMINDERS (Call when trip is booked/created)
 */
export const scheduleRemindersExample = async () => {
  try {
    // This will automatically schedule:
    // - 30-minute reminder
    // - 5-minute reminder  
    // - "Start now" reminder
    // Based on the user's role (driver vs passenger)
    await tripNotificationHelpers.scheduleTripReminders(exampleTrip, currentUser);
    
    console.log('📅 Trip reminders scheduled successfully');
  } catch (error) {
    console.error('❌ Failed to schedule reminders:', error);
  }
};

/**
 * 3. TRIP STARTED NOTIFICATIONS (Call when trip actually starts)
 */
export const notifyTripStartedExample = async () => {
  try {
    // Sends different notifications based on user role:
    // Driver: "Trip started with X passengers"
    // Passenger: "Your ride with [Driver Name] has started"
    await tripNotificationHelpers.notifyTripStarted(exampleTrip, currentUser);
    
    console.log('🚗 Trip started notification sent');
  } catch (error) {
    console.error('❌ Failed to send trip started notification:', error);
  }
};

/**
 * 4. TRIP ENDED NOTIFICATIONS (Call when trip is completed)
 */
export const notifyTripEndedExample = async () => {
  try {
    // For drivers, you can include earnings
    const earnings = currentUser.id === exampleTrip.driver.id ? "$20.50" : undefined;
    
    // Sends different notifications based on user role:
    // Driver: "Trip completed! You earned $20.50"
    // Passenger: "Your ride with [Driver Name] has been completed"
    await tripNotificationHelpers.notifyTripEnded(exampleTrip, currentUser, earnings);
    
    console.log('🎉 Trip ended notification sent');
  } catch (error) {
    console.error('❌ Failed to send trip ended notification:', error);
  }
};

/**
 * 5. BOOKING ACCEPTED/DECLINED (Call when driver responds to booking request)
 */
export const notifyBookingResponseExample = async () => {
  try {
    const passengerUser = { id: "passenger_789", firstName: "Jane", lastName: "Smith" };
    
    // When driver accepts booking
    await tripNotificationHelpers.notifyBookingAccepted(exampleTrip, passengerUser);
    // This also automatically schedules trip reminders for the passenger
    
    // When driver declines booking
    // await tripNotificationHelpers.notifyBookingDeclined(exampleTrip);
    
    console.log('✅ Booking response notification sent');
  } catch (error) {
    console.error('❌ Failed to send booking response notification:', error);
  }
};

/**
 * 6. PICKUP CONFIRMED (Call when passenger is picked up)
 */
export const notifyPickupExample = async () => {
  try {
    await tripNotificationHelpers.notifyPickupConfirmed(exampleTrip, "Jane Smith");
    
    console.log('✅ Pickup confirmed notification sent');
  } catch (error) {
    console.error('❌ Failed to send pickup notification:', error);
  }
};

/**
 * 7. TRIP CANCELLATION (Call when trip is cancelled)
 */
export const handleTripCancellationExample = async () => {
  try {
    // This will:
    // - Cancel all scheduled reminders
    // - Send cancellation notifications to other users
    await tripNotificationHelpers.handleTripCancellation(
      exampleTrip, 
      currentUser, 
      "Emergency came up"
    );
    
    console.log('🚫 Trip cancellation handled');
  } catch (error) {
    console.error('❌ Failed to handle trip cancellation:', error);
  }
};

/**
 * 8. MANUAL REMINDER NOTIFICATIONS (For testing or manual triggers)
 */
export const sendManualRemindersExample = async () => {
  try {
    const userRole = currentUser.id === exampleTrip.driver.id ? 'driver' : 'passenger';
    const tripDetails = {
      origin: exampleTrip.origin.name.split(',')[0],
      destination: exampleTrip.destination.name.split(',')[0],
      time: new Date(exampleTrip.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      driverName: `${exampleTrip.driver.firstName} ${exampleTrip.driver.lastName}`,
      passengerCount: exampleTrip.passengers.length,
    };

    // Send 30-minute reminder manually
    await pushNotificationHelpers.notifyTripReminder30Min(
      exampleTrip.id, 
      userRole, 
      tripDetails
    );

    // Send 5-minute reminder manually
    await pushNotificationHelpers.notifyTripReminder5Min(
      exampleTrip.id, 
      userRole, 
      tripDetails
    );

    // Send "start now" reminder manually
    await pushNotificationHelpers.notifyTripStartNow(
      exampleTrip.id, 
      userRole, 
      tripDetails
    );
    
    console.log('⏰ Manual reminders sent');
  } catch (error) {
    console.error('❌ Failed to send manual reminders:', error);
  }
};

/**
 * 9. UTILITY FUNCTIONS
 */
export const utilityFunctionsExample = () => {
  // Check if notifications are initialized
  const isReady = tripNotificationHelpers.isInitialized();
  console.log('Notifications ready:', isReady);
  
  // Get all scheduled reminders (for debugging)
  const scheduled = tripNotificationHelpers.getScheduledReminders();
  console.log('Scheduled reminders:', scheduled);
  
  // Clear all reminders (for cleanup)
  // await tripNotificationHelpers.clearAllReminders();
};

/**
 * 10. INTEGRATION WITH YOUR EXISTING COMPONENTS
 */

// In your HomeTripCard component:
export const integrateWithHomeTripCard = async (trip: any, currentUser: any) => {
  // When trip starts
  const handleStartTrip = async () => {
    // Your existing trip start logic...
    
    // Add notification
    await tripNotificationHelpers.notifyTripStarted(trip, currentUser);
  };
  
  // When trip ends  
  const handleEndTrip = async () => {
    // Your existing trip end logic...
    
    // Add notification
    await tripNotificationHelpers.notifyTripEnded(trip, currentUser);
  };
};

// In your booking/request components:
export const integrateWithBookingFlow = async (trip: any, passenger: any) => {
  // When driver accepts booking
  const handleAcceptBooking = async () => {
    // Your existing accept logic...
    
    // Add notification and schedule reminders
    await tripNotificationHelpers.notifyBookingAccepted(trip, passenger);
  };
  
  // When driver declines booking
  const handleDeclineBooking = async () => {
    // Your existing decline logic...
    
    // Add notification
    await tripNotificationHelpers.notifyBookingDeclined(trip);
  };
};

/**
 * NOTIFICATION TYPES SUMMARY:
 * 
 * Role-based notifications:
 * - Trip Started: Different messages for drivers vs passengers
 * - Trip Ended: Different messages for drivers vs passengers
 * - Booking Responses: Acceptance/decline notifications
 * 
 * Time-based notifications:
 * - 30 minutes before trip: "Get ready" reminders
 * - 5 minutes before trip: "Almost time" reminders  
 * - At trip time: "Start now" reminders
 * 
 * All notifications are automatically:
 * - Scheduled based on trip timestamp
 * - Cancelled when trip is completed/cancelled
 * - Customized based on user role (driver/passenger)
 * - Delivered even when app is closed/backgrounded
 */
