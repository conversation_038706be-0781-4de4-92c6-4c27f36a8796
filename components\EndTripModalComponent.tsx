import React from 'react';
import useEndTripModal from './EndTripModal';

interface EndTripModalComponentProps {
  trip: any;
  currentUser: any;
  isDriver: boolean;
  onTripEnded?: () => void;
  onEndTripPress?: () => void;
}

const EndTripModalComponent: React.FC<EndTripModalComponentProps> = ({ 
  trip, 
  currentUser, 
  isDriver, 
  onTripEnded,
  onEndTripPress
}) => {
  const { handleEndTrip, renderModals } = useEndTripModal({
    trip,
    currentUser,
    isDriver,
    onTripEnded
  });

  // If onEndTripPress is provided, call it instead of the default handleEndTrip
  React.useEffect(() => {
    if (onEndTripPress) {
      // Replace the default handleEndTrip with the custom one
      // This allows the parent component to control when the modal opens
    }
  }, [onEndTripPress]);

  return (
    <>
      {renderModals()}
    </>
  );
};

// Also export the hook for direct use
export { useEndTripModal };
export default EndTripModalComponent;
