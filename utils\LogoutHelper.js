import * as SecureStore from "expo-secure-store";
import { router } from "expo-router";

export const performSafeLogout = async () => {
  try {
    await SecureStore.deleteItemAsync("authToken");
    
    setTimeout(() => {
      router.push({
        pathname: "/(auth)",
      });
      setTimeout(() => {
        router.replace("/(auth)/Onboarding");
      }, 50);
    }, 100);
    
    return true;
  } catch (error) {
    console.error("Safe logout error:", error);
    return false;
  }
}