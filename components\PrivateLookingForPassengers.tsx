import { View, Text, Animated, StyleSheet } from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { router } from "expo-router";
import Button from "./Button";
import Modal from "./Modal";
import BottomSheet from "@gorhom/bottom-sheet";
import { Trip, TripRequestsResponse } from "@/utils/types";
import { Realtime } from "ably";
import useGetAblyToken from "@/hooks/useGetAblyToken";
import {
  QueryObserverResult,
  RefetchOptions,
  useMutation,
} from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { useRide } from "@/context/RideProvider";
import { queryClient } from "@/providers";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  rideRequests: TripRequestsResponse;
  noPassengerAvailable: boolean;
  setNoPassengerAvailable: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
  tripId: string;
  activeTrip: Trip;
  refetchActive: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
}

const PrivateLookingForPassengers: React.FC<Props> = ({
  step,
  setStep,
  rideRequests,
  noPassengerAvailable,
  setNoPassengerAvailable,
  refetch,
  tripId,
  activeTrip,
  refetchActive,
}) => {
  const { ablyToken } = useGetAblyToken();

  const { toggleSwitch, setIsEnabled } = useRide();

  const { mutate: handleCancelTrip, isPending } = useMutation({
    mutationFn: services.cancelTrip,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setIsEnabled(false);
      queryClient.invalidateQueries({ queryKey: ["trip"] });
      setTimeout(() => {
        router.replace("/(tabs)/Home");
      }, 1000);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const ably = new Realtime({
    key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
    autoConnect: true,
    // useTokenAuth: true
  });

  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["25%"];
  const [status, setStatus] = useState<"connecting" | "not_found" | "found">(
    "connecting"
  );
  const progressAnimation = useRef(new Animated.Value(0)).current;
  const animationRef = useRef<Animated.CompositeAnimation>();

  const handlePassengerFound = () => {
    // Stop the current animation
    animationRef.current?.stop();
    setStatus("found");
    refetchActive();
    setStep(step + 1);
  };

  const handleNoPassenger = () => {
    setStatus("not_found");
    setNoPassengerAvailable(true);
  };

  const simulateDriverConnection = () => {
    // Reset animation value and states
    progressAnimation.setValue(0);
    setStatus("connecting");
    setNoPassengerAvailable(false);

    // Create and store the animation reference
    animationRef.current = Animated.timing(progressAnimation, {
      toValue: 100,
      duration: 30000,
      useNativeDriver: false,
    });

    // Start the animation and handle completion
    animationRef.current.start(({ finished }) => {
      if (finished) {
        handleNoPassenger();
      }
    });
  };

  useEffect(() => {
    // Subscribe to drivers channel
    const channel = ably.channels.get("drivers");

    // Subscribe to ride-request event
    channel.subscribe("private-ride-request", async (message) => {
      console.log("New ride request:", message.data);
      if (message.data) {
        await refetch();
        // Check if there are any ride requests after refetching
        if (rideRequests?.data?.length > 0) {
          handlePassengerFound();
        }
      }
    });

    // Start the initial connection simulation
    simulateDriverConnection();

    // Cleanup on unmount
    return () => {
      channel.unsubscribe();
      channel.detach();
      animationRef.current?.stop();
      progressAnimation.setValue(0);
    };
  }, []);

  // Watch for changes in rideRequests
  useEffect(() => {
    if (rideRequests?.data?.length > 0 && status === "connecting") {
      handlePassengerFound();
    }

    if (
      activeTrip?.status === "pending" &&
      activeTrip?.passengers?.length === activeTrip?.noOfPassengers
    ) {
      setStep(3);
    }

    activeTrip.status === "ongoing" && setStep(4);
  }, [rideRequests, activeTrip]);

  console.log(activeTrip);

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto py-2">
          <Text className="text-[#151B2D] font-semibold text-xl">
            Looking for passengers
          </Text>

          <Text className="w-full text-[#4A4C50] text-[15px] mt-2">
            Pairing you to people going your direction
          </Text>

          <View className="w-full mt-5">
            <View className="bg-[#D9D9D9] h-[2px] relative">
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: progressAnimation.interpolate({
                      inputRange: [0, 100],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                ]}
              />
            </View>
          </View>
        </View>
        <View className="absolute bottom-6 left-0 right-0 px-5 bg-white">
          <Button
            isLoading={isPending}
            buttonDisabled={isPending}
            text="Cancel Trip"
            buttonClassName="bg-[#EDEDED] w-full mb-2"
            textClassName="text-[#151B2D]"
            onClick={() => {
              router.replace("/(drawer)/(tabs)/Home");
              toggleSwitch();
            }}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    backgroundColor: "#473BF0",
    height: 2,
    position: "absolute",
    bottom: 0,
  },
});

export default PrivateLookingForPassengers;
