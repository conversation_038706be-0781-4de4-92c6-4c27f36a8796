import React from "react";
import { View, Text } from "react-native";

interface Props {
  estimatedTime: string;
}

const MapETAOverlay = ({ estimatedTime }: Props) => (
  <View className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg overflow-hidden">
    <View className="flex-row items-center p-3 border-b border-gray-100">
      <View className="h-2 w-2 rounded-full bg-blue-500 mr-2" />
      <Text className="text-sm font-medium text-gray-900">
        Will arrive by {estimatedTime}
      </Text>
    </View>
    <View className="h-1 bg-blue-500" style={{ width: "60%" }} />
  </View>
);

export default MapETAOverlay;
