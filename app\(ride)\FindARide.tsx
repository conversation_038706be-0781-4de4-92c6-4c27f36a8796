import {
  View,
  StatusBar,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
} from "react-native";
import React, { useState, useEffect } from "react";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import GooglePlaces from "@/components/GooglePlaces";
import DateTimeSelector from "@/components/DateAndTimeSelector";
import { useUser } from "@/context/UserContext";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import * as Location from 'expo-location';

const FindARide = () => {
  const [isOriginFocused, setIsOriginFocused] = useState(false);
  const [isDestinationFocused, setIsDestinationFocused] = useState(false);
  const [date, setDate] = useState(new Date());
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [isTimePickerVisible, setTimePickerVisibility] = useState(false);
  const [isdateSelected, setIsDateSelected] = useState(false);
  const [isTimeSelected, setIsTimeSelected] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [currentLocationAddress, setCurrentLocationAddress] = useState("");
  const [locationLoading, setLocationLoading] = useState(false);

  const { ride, updateLocation, updateRide } = useUser();
  const { displayCurrentAddress } = useUser();

  const formatAddress = (address: string) => {
    if (!address || address === "Location Loading.....") return address;
    
    const parts = address.split(',');
    if (parts.length < 3) return address;
    const middlePart = parts[1].trim();
    const cleanedMiddlePart = middlePart.replace(/\s*\d{6}\s*/, '');
    const state = parts[2].trim();
    return `${cleanedMiddlePart}, ${state}`;
  };

  // Function to get current location
  const getCurrentLocation = async () => {
    try {
      setLocationLoading(true);
      
      // Request permission
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        setLocationLoading(false);
        return;
      }

      // Get current position
      let location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      // Reverse geocode to get address
      let reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        
        // Format address similar to Google Places format
        const addressParts = [
          address.streetNumber,
          address.street,
          address.subregion || address.city,
          address.region,
          address.country
        ].filter(Boolean);
        
        const formattedAddress = addressParts.join(', ');
        
        // Create location object similar to Google Places format
        const currentLocationData = {
          name: formattedAddress,
          lat: location.coords.latitude,
          lng: location.coords.longitude,
        };

        // Set the formatted address for display
        setCurrentLocationAddress(formattedAddress);
        
        // Set as default pickup location in context
        updateLocation("pickup", currentLocationData);
        
        console.log("Current location set:", currentLocationData);
      }
    } catch (error) {
      console.error("Error getting current location:", error);
      // Don't show error toast, just log it - we don't want to annoy users
    } finally {
      setLocationLoading(false);
    }
  };

  // Load current location on component mount
  useEffect(() => {
    // Only get location if we don't already have a pickup location
    if (!ride.pickup?.name && !currentLocationAddress) {
      getCurrentLocation();
    }
  }, []);

  const testHandleRides = () => {
    router.push('/(ride)/TestComponent')
  }

  // Check if locations are selected
  useEffect(() => {
    console.log("Current ride state:", ride);
  }, [ride]);

  const { mutate: getAvailableCoRides, isPending } = useMutation({
    mutationFn: services.getAvailableCoRides,
    onSuccess: (data: any) => {
      console.log("API response:", data);
      
      if (data.status) {
        if (Array.isArray(data.data) && data.data.length === 0) {
          Toast.show({
            type: "info",
            text1: "No rides available for your selection",
            text2: "Please try different options",
          });
          return;
        }
        
        console.log("Navigating to BookRide with data:", data.data);
        const serializedData = JSON.stringify(data.data);
        console.log("Serialized data length:", serializedData.length);
        
        try {
          router.push({
            pathname: "/BookRide",
            params: { rides: serializedData },
          } as Href);
        } catch (error) {
          console.error("Navigation error:", error);
          Toast.show({
            type: "error",
            text1: "Navigation failed",
            text2: "Please try again",
          });
        }
      } else {
        Toast.show({
          type: "warning",
          text1: data.message || "No rides found",
          text2: "Please try different criteria",
        });
      }
    },
    onError: (error: any) => {
      console.error("API error full details:", error);
      console.error("Error message:", error.message);
      console.error("Error code:", error.code);
      console.error("Response status:", error.response?.status);
      console.error("Response data:", error.response?.data);
      console.error("Request config:", error.config);

      setApiError(error);

      // Handle specific error types
      let errorMessage = "Network error";
      let errorDescription = "Please check your connection and try again";

      if (error.message === "Request has not been opened") {
        errorMessage = "Connection error";
        errorDescription = "Unable to connect to server. Please try again.";
      } else if (error.response) {
        // Server responded with error status
        errorMessage = error.response.data?.description || error.response.data?.message || "Server error";
        errorDescription = `Status: ${error.response.status}`;
      } else if (error.request) {
        // Request was made but no response received
        errorMessage = "No response from server";
        errorDescription = "Please check your internet connection";
      } else {
        // Something else happened
        errorMessage = error.message || "Unknown error";
      }

      Toast.show({
        type: "error",
        text1: errorMessage,
        text2: errorDescription,
      });
    },
  });

  const placeholderText = isOriginFocused
    ? "Enter pick up location"
    : "From (Origin)";

  const destinationPlaceholderText = isDestinationFocused
    ? "Enter drop off location"
    : "To (Destination)";

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const showTimePicker = () => {
    setTimePickerVisibility(true);
  };

  const hideTimePicker = () => {
    setTimePickerVisibility(false);
  };

  const handleDateConfirm = (selectedDate: Date) => {
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      return newDate;
    });
    hideDatePicker();
    setIsDateSelected(true);
  };

  const handleFindRides = () => {
    if (!ride.pickup?.name) {
      Toast.show({
        type: "error",
        text1: "Pick up location required",
      });
      return;
    }

    if (!ride.dropoff?.name) {
      Toast.show({
        type: "error",
        text1: "Drop off location required",
      });
      return;
    }

    // Validate that pickup and dropoff have required coordinates
    if (!ride.pickup.lat || !ride.pickup.lng) {
      Toast.show({
        type: "error",
        text1: "Invalid pickup location",
        text2: "Please select a valid pickup location",
      });
      return;
    }

    if (!ride.dropoff.lat || !ride.dropoff.lng) {
      Toast.show({
        type: "error",
        text1: "Invalid dropoff location",
        text2: "Please select a valid dropoff location",
      });
      return;
    }

    const payload = {
      pickup: ride.pickup,
      dropoff: ride.dropoff,
      mode: "carpool" as const,
    };

    console.log("Finding rides with payload:", payload);
    console.log("Base URL:", process.env.EXPO_PUBLIC_BASE_URL);

    // Check if base URL is configured
    if (!process.env.EXPO_PUBLIC_BASE_URL) {
      Toast.show({
        type: "error",
        text1: "Configuration error",
        text2: "Base URL not configured",
      });
      return;
    }

    try {
      getAvailableCoRides({
        dataBody: payload,
      });
    } catch (error) {
      console.error("Error calling getAvailableCoRides:", error);
      Toast.show({
        type: "error",
        text1: "Request failed",
        text2: "Please try again",
      });
    }
  };

  const showDebugInfo = () => {
    if (apiError) {
      Alert.alert(
        "Debug Info",
        `Error: ${apiError}\n`,
        [{ text: "OK" }]
      );
    } else {
      Alert.alert(
        "Debug Info",
        `Current ride state:\n${JSON.stringify(ride, null, 2)}\n\nCurrent Address: ${currentLocationAddress}`,
        [{ text: "OK" }]
      );
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="w-full h-full bg-white">
          <StatusBar
            barStyle={"dark-content"}
            backgroundColor={"transparent"}
          />
          <View className="w-[90%] h-full mx-auto mt-2 relative">
            <GooglePlaces
              placeholderText={locationLoading ? "Getting current location..." : placeholderText}
              defaultValue={currentLocationAddress || (ride.pickup?.name ? formatAddress(ride.pickup.name) : "")}
              isFocused={isOriginFocused}
              onPlaceSelected={(location) => {
                console.log("Origin selected:", location);
                updateLocation("pickup", location);
                setCurrentLocationAddress(""); // Clear current location address when user selects new one
              }}
              setIsFocused={setIsOriginFocused}
              leftFocusedImage={require("../../assets/images/radio.png")}
              ObjKey="origin"
            />

            <GooglePlaces
              placeholderText={destinationPlaceholderText}
              isFocused={isDestinationFocused}
              onPlaceSelected={(location) => {
                console.log("Destination selected:", location);
                updateLocation("dropoff", location);
              }}
              setIsFocused={setIsDestinationFocused}
              leftFocusedImage={require("../../assets/images/location.png")}
              ObjKey="destination"
            />

            <DateTimeSelector
              showDatePicker={showDatePicker}
              showTimePicker={showTimePicker}
              date={date}
              isDatePickerVisible={isDatePickerVisible}
              isTimePickerVisible={isTimePickerVisible}
              handleTimeConfirm={() => {}}
              handleDateConfirm={handleDateConfirm}
              hideDatePicker={hideDatePicker}
              hideTimePicker={hideTimePicker}
              dateSelected={isdateSelected}
              timeSelected={isTimeSelected}
              showTitle={false}
              enableTimePicker={false}
              textToBeShown="Departing date (Optional)"
            />

            <Button
              buttonDisabled={
                isPending ||
                !ride.pickup?.name ||
                !ride.dropoff?.name ||
                locationLoading
              }
              isLoading={isPending || locationLoading}
              onClick={handleFindRides}
              text="Next"
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] absolute bottom-16"
            />
            
            {/* Uncomment for debugging */}
            {/* <Button
              buttonDisabled={isPending}
              onClick={showDebugInfo}
              text="Debug Info"
              textClassName="text-white text-sm"
              buttonClassName="bg-gray-500 absolute bottom-5 h-8"
            /> */}
            
            <Toast />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default FindARide;