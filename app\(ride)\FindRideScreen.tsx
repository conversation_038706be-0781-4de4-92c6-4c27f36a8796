import { View, Text, TouchableOpacity, Image } from 'react-native'
import React from 'react'
import { router } from 'expo-router'

const TestComponent = () => {
  return (
    <View className='flex-1 bg-gray-50 px-4 pt-6'>
      <Text className="text-xl font-semibold text-black mb-4">Going somewhere?</Text>
      <TouchableOpacity className="bg-white p-4 py-20 mt-11 rounded-2xl shadow mb-4 flex-row items-center" onPress={() => router.push('/(ride)/FindARide')}>
        <View className='flex-1'>
          <View className="w-12 h-12 rounded-full flex justify-center items-center mr-4">
            <Image
                source={require("../../assets/images/carrNew.png")}
                className="w-[54px] h-[54px]" />
           </View>
          <Text className="text-base font-semibold text-black mt-3">Join a Coride</Text>
          <Text className="text-sm text-gray-500">Find a Corider going to your destination</Text>
        </View>
      </TouchableOpacity>

      <TouchableOpacity className="bg-white p-4 py-20 mt-11 rounded-2xl shadow mb-4 flex-row items-center" onPress={() => router.push('/(verifyDriver)/Verification')}>
        <View className='flex-1'>
         <View className="w-12 h-12 rounded-full flex justify-center items-center mr-4">
          <Image source={require("../../assets/images/priNew.png")} className="w-[54px] h-[54px]" />
          </View>
          <Text className="text-base font-semibold text-black mt-3">Find a private driver</Text>
          <Text className="text-sm text-gray-500">Schedule or book an instant private ride</Text>
        </View>
      </TouchableOpacity>
    </View>
  )
}

export default TestComponent