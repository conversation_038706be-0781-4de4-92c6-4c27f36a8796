import React, { useRef } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import { useMutation } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';
import { useTripStore } from '@/app/store/tripStore';
import { useRide } from '@/context/RideProvider';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import { driverTripEndedPublisherHelpers } from '@/utils/driverTripEndedPublisher';
import CustomModal from '@/components/Modal';
import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import Button from '@/components/Button';

interface EndTripModalProps {
  trip: any;
  currentUser: any;
  isDriver: boolean;
  onTripEnded?: () => void;
}

const useEndTripModal = ({
  trip,
  currentUser,
  isDriver,
  onTripEnded
}: EndTripModalProps) => {
  const endTripModalRef = useRef<any>(null);
  const paymentModalRef = useRef<any>(null);
  const { resetRideState } = useRide();

  const {
    tripProgress,
    showEndTripModal,
    setShowEndTripModal,
    showPaymentModal,
    setShowPaymentModal,
    showPassengerEndTripModal,
    setShowPassengerEndTripModal,
    showEndTripOptions,
    setShowEndTripOptions,
    selectedOption,
    setSelectedOption,
    reportIssue,
    setReportIssue,
    selectedIssue,
    setSelectedIssue,
    somethingElse,
    setSomethingElse,
    customIssueText,
    setCustomIssueText,
    showEndTripEarly,
    setShowEndTripEarly,
  } = useTripStore();

  const { mutate: endTrip, isPending: isEndingTrip } = useMutation({
    mutationFn: services.endTrip,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data?.message,
      });
      setShowEndTripModal(false);
      setShowPassengerEndTripModal(true);

      // Send realtime notification to passengers
      if (trip?.id && currentUser?.firstName) {
        realtimeNotificationHelpers.notifyTripEnded(trip.id, currentUser.firstName);
      }

      // Reset ride state to clear preferences and form data
      resetRideState();

      // Publish Ably event for passenger trip ended modal
      if (trip && currentUser && isDriver) {
        try {
          const endReason = selectedOption === 'option1' ? 'early' :
                           selectedIssue ? 'issue' : 'completed';

          await driverTripEndedPublisherHelpers.publishTripEnded(trip, currentUser, endReason);
          console.log('📡 Published trip-ended event to passengers');
        } catch (error) {
          console.error('❌ Failed to publish trip-ended event:', error);
        }
      }

      // Call the callback if provided
      if (onTripEnded) {
        onTripEnded();
      }
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error?.response?.data?.message || "Failed to end trip",
      });
    },
  });

  const { mutate: confirmPayment, isPending: isConfirmingPayment } = useMutation({
    mutationFn: services.confirmPayment,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message || "Payment confirmed",
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error?.response?.data?.message || "Failed to confirm payment",
      });
    },
  });

  const handleEndTrip = () => {
    console.log('End Trip Pressed - Current Trip Progress:', tripProgress);
    setShowEndTripModal(true);
    setTimeout(() => {
      endTripModalRef.current?.expand();
    }, 100);
  };

  const handleConfirmEndTrip = () => {
    console.log('Confirm End Trip Pressed - Current Trip Progress:', tripProgress);
    console.log('Selected Option:', selectedOption);
    console.log('Selected Issue:', selectedIssue);
    if (trip?.id) {
      // Allow ending trip if progress is 100% OR if ending early
      if (tripProgress >= 100 || selectedOption === 'option1' || selectedIssue) {
        console.log('Ending trip with conditions:', {
          tripProgress,
          selectedOption,
          selectedIssue
        });
        endTrip({ dataBody: { tripId: trip.id } });
      }
    }
  };

  // const handleConfirmPayment = (passengerId: string) => {
  //   confirmPayment({ dataBody: { passengerId, tripId: trip?.id } });
  // };

  const renderDriverModals = () => (
    <>
      {showEndTripModal && (
        <CustomModal
          ref={endTripModalRef}
          index={0}
          customSnapPoints={["30%"]}
        >
          <View className="flex-1 w-full relative bg-[#FFFFFF]">
            {(!showEndTripOptions && !showEndTripEarly) && (
              <View className="flex-1 items-center justify-center w-[90%] mx-auto">
                <>
                  {tripProgress >= 100
                    ? <Text className='text-[40px] mb-[6px]'>🎉</Text>
                    : <Image source={require("../assets/images/endTripNew.png")} className='w-[40px] h-[40px]' />
                  }

                  <Text className="text-xl font-semibold mb-4 text-center">
                    {tripProgress >= 100 ? "Trip Ended" : "End Trip"}
                  </Text>
                  <Text className="text-center text-[15px] font-normal mb-6">
                    {tripProgress >= 100
                      ? "Press 'Okay' to return home"
                      : "Are you sure you want to end this trip early?"}
                  </Text>
                  {tripProgress >= 100
                    ? <Button
                        text="Okay"
                        buttonClassName="bg-[#F4F4F4] mb-3"
                        textClassName="text-black"
                        onClick={handleConfirmEndTrip}
                      />
                    : <View className="flex-row justify-between">
                        <Button
                          text="Yes"
                          buttonClassName="bg-[#473BF0] flex-1 mr-2"
                          textClassName="text-white"
                          onClick={() => {
                            setShowEndTripOptions(true)
                            console.log('Confirm End Trip Pressed - Current Trip Progress:', tripProgress);
                          }}
                        />
                        <Button
                          text="No"
                          buttonClassName="bg-[#F4F4F4] flex-1 ml-2"
                          textClassName="text-[#151B2D]"
                          onClick={() => {
                            endTripModalRef.current?.close();
                            setShowEndTripModal(false);
                          }}
                        />
                      </View>
                  }
                </>
              </View>
            )}

            {showEndTripOptions && (
              <View className="flex-1 p-2 w-[90%] mx-auto">
                <Text className='font-semibold text-[17px] mb-4'>Reason for ending trip</Text>
                <View>
                  <View className="flex-row items-center mb-[18px]">
                    <TouchableOpacity
                      onPress={() => setSelectedOption('option1')}
                      className="flex-row items-center"
                    >
                      <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedOption === 'option1' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                        {selectedOption === 'option1' && (
                          <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                        )}
                      </View>
                      <Text className='text-[15px] font-normal'>Trip completed earlier than expected time</Text>
                    </TouchableOpacity>
                  </View>
                  <View className="flex-row items-center">
                    <TouchableOpacity
                      onPress={() => setSelectedOption('option2')}
                      className="flex-row items-center"
                    >
                      <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedOption === 'option2' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                        {selectedOption === 'option2' && (
                          <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                        )}
                      </View>
                      <Text className='text-[15px] font-normal'>Report and issue</Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View className="flex-row justify-between mb-2 mt-8">
                  <Button
                    text="Cancel"
                    buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                    textClassName="text-[#151B2D]"
                    onClick={() => {
                      setShowEndTripOptions(false)
                    }}
                  />

                  <Button
                    text="Continue"
                    buttonClassName="bg-[#473BF0] flex-1 ml-2"
                    textClassName="text-white"
                    onClick={() => {
                      if (selectedOption === 'option1') {
                        setShowEndTripOptions(false)
                        setShowEndTripEarly(true)
                        setTimeout(() => {
                          handleConfirmEndTrip()
                        }, 2000);
                      } else if (selectedOption === 'option2') {
                        setShowEndTripModal(false)
                        setReportIssue(true)
                      }
                    }}
                  />
                </View>
              </View>
            )}
          </View>
        </CustomModal>
      )}

      {reportIssue && (
        <CustomModal
          ref={endTripModalRef}
          index={0}
          customSnapPoints={["35%"]}
        >
          <View className="flex-1 w-full relative bg-[#FFFFFF]">
            <View className="flex-1 p-2 w-[90%] mx-auto">
              <Text className='font-semibold text-[17px] mb-4'>Report an issue</Text>
              <View>
                <View className="flex-row items-center mb-[18px]">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('firstIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'firstIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'firstIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Car broke down (flat tire, low fuel etc)</Text>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center mb-[18px]">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('secondIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'secondIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'secondIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Changing routes</Text>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('thirdIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'thirdIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'thirdIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Something else</Text>
                  </TouchableOpacity>
                </View>
              </View>
              <View className="flex-row justify-between mb-2 mt-8">
                <Button
                  text="Back"
                  buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                  textClassName="text-[#151B2D]"
                  onClick={() => {
                    setReportIssue(false)
                    setShowEndTripModal(true)
                    setShowEndTripOptions(true)
                  }}
                />

                <Button
                  text="Continue"
                  buttonClassName="bg-[#473BF0] flex-1 ml-2"
                  textClassName="text-white"
                  onClick={() => {
                    if (selectedIssue === 'firstIssue') {
                      setReportIssue(false)
                      setShowEndTripEarly(true)
                    } else if (selectedIssue === 'secondIssue') {
                      setReportIssue(false)
                      setShowEndTripEarly(true)
                    } else if (selectedIssue === 'thirdIssue') {
                      setReportIssue(false)
                      setSomethingElse(true)
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </CustomModal>
      )}

      {somethingElse && (
        <CustomModal
          ref={endTripModalRef}
          index={0}
          customSnapPoints={["48%"]}
        >
          <View className="flex-1 w-full relative bg-[#FFFFFF]">
            <View className="flex-1 p-2 w-[90%] mx-auto">
              <Text className='font-semibold text-[17px] mb-4'>Report an issue</Text>
              <View>
                <View className="flex-row items-center mb-[18px]">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('firstIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'firstIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'firstIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Car broke down (flat tire, low fuel etc)</Text>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center mb-[18px]">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('secondIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'secondIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'secondIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Changing routes</Text>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center mb-[18px]">
                  <TouchableOpacity
                    onPress={() => setSelectedIssue('thirdIssue')}
                    className="flex-row items-center"
                  >
                    <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'thirdIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                      {selectedIssue === 'thirdIssue' && (
                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                      )}
                    </View>
                    <Text className='text-[15px] font-normal'>Something else</Text>
                  </TouchableOpacity>
                </View>
                <BottomSheetTextInput
                  placeholder="Please describe the issue..."
                  value={customIssueText}
                  onChangeText={setCustomIssueText}
                  multiline
                  numberOfLines={4}
                  style={{
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    borderRadius: 8,
                    padding: 12,
                    textAlignVertical: 'top',
                    minHeight: 80,
                    marginTop: 10,
                  }}
                />
              </View>
              <View className="flex-row justify-between mb-2 mt-8">
                <Button
                  text="Back"
                  buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                  textClassName="text-[#151B2D]"
                  onClick={() => {
                    setSomethingElse(false)
                    setReportIssue(true)
                  }}
                />

                <Button
                  text="Continue"
                  buttonClassName="bg-[#473BF0] flex-1 ml-2"
                  textClassName="text-white"
                  onClick={() => {
                    setSomethingElse(false)
                    setShowEndTripEarly(true)
                    setTimeout(() => {
                      handleConfirmEndTrip()
                    }, 2000);
                  }}
                />
              </View>
            </View>
          </View>
        </CustomModal>
      )}

      {showEndTripEarly && (
        <CustomModal
          ref={endTripModalRef}
          index={0}
          customSnapPoints={["30%"]}
        >
          <View className="flex-1 p-2 items-center w-[90%] mx-auto">
            <Image source={require("../assets/images/tripEndedNew.png")} className='h-[40px] w-[40px] mb-4' />
            <Text className='font-semibold text-xl text-center mb-[6px]'>Trip ended</Text>
            <Text className='text-[15px] font-normal text-center mb-6'>
              {selectedOption === 'option1'
                ? 'Trip completed earlier than expected time'
                : 'Report and issue'
              }
              {selectedIssue === 'firstIssue'
                ? 'Car broke down (flat tire, low fuel etc)'
                : selectedIssue === 'secondIssue'
                  ? 'Changing routes'
                  : customIssueText
              }
            </Text>
            <Button
              text="Okay"
              buttonClassName="bg-[#F4F4F4] mb-3"
              textClassName="text-black"
              onClick={() => {
                setShowEndTripEarly(false);
                router.replace("/(tabs)/Home");
              }}
            />
          </View>
        </CustomModal>
      )}
    </>
  );

  const renderPassengerModals = () => (
    <>
      {showPassengerEndTripModal && (
        <CustomModal
          ref={endTripModalRef}
          index={0}
          customSnapPoints={["30%"]}
        >
          <View className="flex-1 items-center justify-center w-[90%] mx-auto">
            <Text className='text-[40px] mb-[6px]'>🎉</Text>
            <Text className="text-xl font-semibold mb-4 text-center">
              Trip Ended
            </Text>
            <Text className="text-center text-[15px] font-normal mb-6">
              We hope you enjoyed your trip!
            </Text>
            <Button
              text="Okay"
              buttonClassName="bg-[#F4F4F4] mb-3"
              textClassName="text-black"
              onClick={() => {
                setShowPassengerEndTripModal(false);
                router.replace("/(tabs)/Home");
              }}
            />
          </View>
        </CustomModal>
      )}
    </>
  );

  // Export the handleEndTrip function and render method
  return {
    handleEndTrip,
    renderModals: () => (
      <>
        {isDriver ? renderDriverModals() : renderPassengerModals()}
      </>
    ),
  };
};

export default useEndTripModal;
