import { View, Text, Animated, StyleSheet, Platform } from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { router } from "expo-router";
import Button from "./Button";
import Modal from "./Modal";
import BottomSheet from "@gorhom/bottom-sheet";
import { Trip } from "@/utils/types";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Passenger {
  name: string;
  destination: string;
}

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  trip: Trip;
}

const PrivateRide: React.FC<Props> = ({
  step,
  setStep,
  setCancelRide,
  trip,
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const iosCustomSnapPoints = ["22%"];
  const androidCustomSnapPoints = ["25%"];
  const progressAnimation = useRef(new Animated.Value(0)).current;
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState<number>(0);

  const passenger = trip?.passengers.map((passenger) => {
    return {
      name: `${passenger?.firstName} ${passenger?.lastName}`,
      destination: passenger?.dropoff?.name,
    };
  });

  useEffect(() => {
    // Calculate estimated time when component mounts
    calculateEstimatedTime();
  }, []);

  const { travelData } = useTravelTimeAndDistance(
    trip?.origin?.name,
    trip?.destination?.name
  );

  const calculateEstimatedTime = () => {
    // Convert distance to minutes (this is just an example calculation)
    const distance = travelData?.durationInSeconds
    const timeInMinutes = Math.ceil(distance! * 100);
    setEstimatedTime(timeInMinutes);

    // Start the trip animation with calculated duration
    startTrip(timeInMinutes);
  };

  const startTrip = (duration: number) => {
    // Convert minutes to milliseconds for animation
    const animationDuration = duration * 60 * 1000;

    Animated.timing(progressAnimation, {
      toValue: 100,
      duration: animationDuration,
      useNativeDriver: false,
    }).start(({ finished }) => {
      if (finished) {
        setIsAnimationComplete(true);
      }
    });
  };

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: services.endTrip,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setStep(step + 1);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const handleEndTrip = () => {
    mutate({ dataBody: { tripId: trip.id } });
  };

  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={
        Platform.OS === "ios" ? iosCustomSnapPoints : androidCustomSnapPoints
      }
      index={0}
      ref={bottomSheetRef}
    >
      <View className="w-full relative bg-[#FFFFFF] flex-1">
        <View className="w-[90%] mx-auto py-2">
          <Text className="text-[#151B2D] font-semibold text-xl">
            {isAnimationComplete
              ? "You've arrived at your destination"
              : `Arriving destination in ${estimatedTime} mins`}
          </Text>

          <Text className="w-full text-[#4A4C50] text-[15px] mt-2">
            {passenger[0]?.destination}
          </Text>

          {/* Progress Bar */}
          <View className="w-full my-5">
            <View className="bg-[#D9D9D9] h-[2px] relative">
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: progressAnimation.interpolate({
                      inputRange: [0, 100],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                ]}
              />
            </View>
          </View>

          {isAnimationComplete ? (
            <Button
              isLoading={isPending}
              buttonDisabled={isPending}
              text="End trip"
              buttonClassName="bg-[#473BF0] w-full mb-2"
              textClassName="text-white"
              onClick={() => handleEndTrip()}
            />
          ) : (
            <Button
              text="Cancel trip"
              buttonClassName="bg-[#EDEDED] w-full mb-2"
              textClassName="text-[#151B2D]"
              onClick={() => setCancelRide(true)}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    backgroundColor: "#473BF0",
    height: 2,
    position: "absolute",
    bottom: 0,
  },
});

export default PrivateRide;
