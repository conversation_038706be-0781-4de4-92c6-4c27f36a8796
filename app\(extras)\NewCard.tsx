import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>ield,
  StripeProvider,
  useConfirmSetupIntent,
  useStripe,
} from "@stripe/stripe-react-native";
import axios from "axios";
import * as SecureStore from "expo-secure-store";
import { router } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";

const AddPaymentCard = () => {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const { confirmSetupIntent } = useConfirmSetupIntent();
  const [cardComplete, setCardComplete] = useState(false);
  const [loading, setLoading] = useState(false);
  const { refetch } = useCurrentUser();

  useEffect(() => {
    const fetchClientSecret = async () => {
      try {
        const tokenData = await SecureStore.getItemAsync("authToken");

        if (!tokenData) {
          router.push("/(auth)/SignIn");
          return;
        }

        const parsedToken = JSON.parse(tokenData);
        const authToken = parsedToken.token;

        if (!authToken) {
          return;
        }

        const response = await axios.post(
          `${process.env.EXPO_PUBLIC_BASE_URL}/stripe/add-card-intent`,
          {},
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${authToken}`,
            },
          }
        );

        setClientSecret(response.data.client_secret);
      } catch (error: any) {
        Alert.alert(
          "Error",
          error.response
            ? error.response.data.description || error.response.data.message
            : error.message
        );
      }
    };

    fetchClientSecret();
  }, []);

  const handleAddCard = async () => {
    if (!clientSecret) return;

    setLoading(true);
    try {
      const { error } = await confirmSetupIntent(clientSecret, {
        paymentMethodType: "Card",
      });
      refetch();

      if (error) {
        console.error("Stripe setup error:", error);
        Alert.alert("Error", error.message || "Something went wrong");
      } else {
        Alert.alert(
          "Card Added Successfully",
          "Your card has been securely linked to your account",
          [
            {
              text: "OK",
              onPress: () => router.back(),
            },
          ]
        );
      }
    } catch (err) {
      console.error("Card setup error:", err);
      Alert.alert("Error", "Failed to add card. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="flex-1 bg-white">
          <StripeProvider
            publishableKey={
              process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY as string
            }
          >
            <View className="flex-1 p-4 relative">
              {!clientSecret ? (
                <View className="flex-1 justify-center items-center">
                  <ActivityIndicator size="small" className="#fff" />
                </View>
              ) : (
                <View className="flex-1">
                  {/* Header */}
                  <View className="items-center mb-8 mt-4">
                    <View className="w-16 h-16 bg-blue-50 rounded-full items-center justify-center mb-4">
                      {/* <CreditCard size={32} color="#2563EB" /> */}
                    </View>
                    <Text className="text-xl font-semibold text-gray-800">
                      Add Payment Method
                    </Text>
                    <Text className="text-sm text-gray-500 text-center mt-2">
                      Add a credit or debit card to your account
                    </Text>
                  </View>

                  {/* Card Input Section */}
                  <View className="bg-gray-50 rounded-xl p-4 mb-6">
                    <CardField
                      postalCodeEnabled={false}
                      cardStyle={{
                        backgroundColor: "#F9FAFB",
                        textColor: "#1F2937",
                        placeholderColor: "#9CA3AF",
                        borderWidth: 1,
                        borderColor: "#E5E7EB",
                        borderRadius: 8,
                      }}
                      style={styles.cardField}
                      onCardChange={(cardDetails) => {
                        const isComplete =
                          cardDetails.complete &&
                          cardDetails.validNumber === "Valid" &&
                          cardDetails.validExpiryDate === "Valid" &&
                          cardDetails.validCVC === "Valid";

                        setCardComplete(isComplete);
                      }}
                    />
                  </View>

                  <TouchableOpacity
                    onPress={handleAddCard}
                    disabled={!cardComplete || loading}
                    className={`absolute bottom-0 w-full h-14 rounded-xl flex items-center justify-center ${
                      cardComplete && !loading ? "bg-[#473BF0]" : "bg-gray-400"
                    }`}
                  >
                    {loading ? (
                      <ActivityIndicator color="white" />
                    ) : (
                      <Text className="text-white font-semibold text-sm">
                        Add Card
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </StripeProvider>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  cardField: {
    width: "100%",
    height: 50,
    marginVertical: 4,
  },
});

export default AddPaymentCard;
