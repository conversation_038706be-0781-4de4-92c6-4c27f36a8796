import AuthHeader from "@/components/AuthHeader";
import Button from "@/components/Button";
import SocialSignUp from "@/components/SocialSignUp";
import { useUser } from "@/context/UserContext";
import { services } from "@/services";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import React, { useState, useRef } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import CountryPicker, { CountryCode } from "react-native-country-picker-modal";
import PhoneInput from "react-native-phone-input";
import Toast from "react-native-toast-message";
import { Link } from "expo-router";

const SignUp = () => {
  const [phoneCountryCode, setPhoneCountryCode] = useState<CountryCode>("CA");
  const [showPhoneCountryPicker, setShowPhoneCountryPicker] = useState(false);
  const { userInfo, setUserDetails } = useUser();
  const phoneInput = useRef<PhoneInput>(null);

  const handleCountrySelect = (country: any) => {
    setPhoneCountryCode(country.cca2);
    const newPhoneNumber = `+${country.callingCode[0]}`;
    setUserDetails(newPhoneNumber, "phoneNumber");
    if (phoneInput.current) {
      phoneInput.current.selectCountry(country.cca2.toLowerCase());
      phoneInput.current.setValue(newPhoneNumber);
    }
    setShowPhoneCountryPicker(false);
  };

  // Prevent multiple OTP requests
  const [isOtpRequested, setIsOtpRequested] = useState(false);

  const { mutate: sendOTP, isPending } = useMutation({
    mutationFn: services.sendOtp,
    onSuccess: (data) => {
      setIsOtpRequested(false); // Reset OTP request state after success
      Toast.show({
        type: "success",
        text1: "SMS sent!",
        text2: `OTP sent to ${data.phone_number}`,
      });
      router.push({ pathname: "/VerifyPhone", params: { pinId: data.pinId } });
    },
    onError: (error: any) => {
      setIsOtpRequested(false); // Reset OTP request state even if there's an error
      Toast.show({
        type: "error",
        text1: "Error Sending OTP",
        text2: error.response?.data?.description || error.message,
      });
    },
  });

  const handleSendOTP = () => {
    const isValid = phoneInput.current?.isValidNumber();
    if (!isValid) {
      Toast.show({
        type: "error",
        text1: "Invalid Phone Number",
        text2: "Please enter a valid phone number.",
      });
      return;
    }
    if (!isOtpRequested) {
      setIsOtpRequested(true); // Lock OTP request
      sendOTP({ dataBody: { phoneNumber: userInfo.phoneNumber } });
    }
  };

  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <ScrollView
        className="w-[90%] mx-auto"
        showsVerticalScrollIndicator={false}
      >
        <AuthHeader
          header="Enter your phone number"
          subHeader="Sign up with your mobile number"
          subbHeader=""
        />
        <View className="mt-5">
          <PhoneInput
            ref={phoneInput}
            initialValue={userInfo.phoneNumber}
            initialCountry={phoneCountryCode.toLowerCase()}
            onPressFlag={() => setShowPhoneCountryPicker(true)}
            onChangePhoneNumber={(text) => {
              setUserDetails(text, "phoneNumber");
            }}
            style={{
              backgroundColor: "#F6F6F6",
              height: 48,
              borderRadius: 6,
              paddingHorizontal: 10,
            }}
          />

          <CountryPicker
            countryCode={phoneCountryCode}
            visible={showPhoneCountryPicker}
            onSelect={handleCountrySelect}
            onClose={() => setShowPhoneCountryPicker(false)}
            withFlagButton={false}
            withFilter
          />
          <Text className="text-red-500 text-xs mt-1">
            {phoneInput.current?.isValidNumber()
              ? ""
              : "Please enter a valid phone number."}
          </Text>
        </View>

        <View className="mt-6">
          <Button
            onClick={handleSendOTP}
            buttonDisabled={!phoneInput.current?.isValidNumber() || isPending}
            isLoading={isPending}
            text="Continue"
            textClassName="text-white"
            buttonClassName="bg-[#473BF0] border border-[#FFFFFF4D]"
          />

          <View className="w-full relative items-center my-7">
            <View className="w-full h-[1px] bg-[#151B2D33]"></View>

            <View className="bg-white h-7 w-7 justify-center items-center rounded-full absolute -top-3">
              <Text className="text-[#787A80] text-xs font-medium">OR</Text>
            </View>
          </View>
        </View>

        <SocialSignUp />

        <View className="items-center flex-row mt-[60%] mb-30 justify-center">
          <Text className="text-center text-[15px] mb-6 font-medium">
            Already have an account?{" "}
          </Text>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => router.push("/SignIn")}
          >
            <Text className="text-[#473BF0] mb-6">Log in</Text>
          </TouchableOpacity>
        </View>
        <Text className="text-[#747474] text-center text-lg mb-12 px-4">
          By signing up, you agree to our{" "}
          <Link href={"/"} className="underline">
            Terms & Condition
          </Link>
          , acknowledge our{" "}
          <Link href={"/"} className="underline">
            Privacy Policy
          </Link>{" "}
          and confirm that you’re over 18.
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SignUp;
