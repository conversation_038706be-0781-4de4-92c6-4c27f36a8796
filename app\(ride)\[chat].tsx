import {
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import React, { useEffect, useState } from "react";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useRef } from "react";
import { chats } from "@/libs/data";

const Chat = () => {
  const [value, onChangeText] = useState("");
  const scrollViewRef = useRef<ScrollView | null>(null);

  useEffect(() => {
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
    >
      <View className="flex-1 bg-white">
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 60 }}
          className="flex-1"
          keyboardShouldPersistTaps="handled"
        >
          <View className="w-[85%] mx-auto py-6">
            {chats && chats.length > 0 ? (
              <View className="space-y-4">
                {chats.map((chat) => {
                  const isUser = chat.senderUser;
                  return (
                    <View key={chat.id}>
                      <View
                        className={`border-[1px] rounded-[100px] border-[#F4F4F4] drop-shadow-sm p-[10px] ${
                          isUser
                            ? "bg-[#F4F4F4] self-end"
                            : "bg-[#FFFFFF] self-start"
                        }`}
                      >
                        <Text className="text-[14px] text-[#151B2D]">
                          {chat.message}
                        </Text>
                      </View>
                      <Text
                        className={`text-[10px] text-[#787A80] mt-1 ${
                          isUser
                            ? "text-right self-end"
                            : "text-left self-start"
                        }`}
                      >
                        {new Date(chat.createdAt).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: true,
                        })}
                      </Text>
                    </View>
                  );
                })}
              </View>
            ) : (
              <View className="flex-1 items-center justify-center">
                <Text>Send a message to start a chat</Text>
              </View>
            )}
          </View>
        </ScrollView>

        <View className="absolute bottom-6 left-0 right-0">
          <View className="w-[90%] mx-auto mb-2 relative flex-row items-center">
            <TextInput
              placeholder="Type a message"
              onChangeText={(text) => onChangeText(text)}
              value={value}
              className="flex-1 h-10 px-3 text-black bg-[#F1F3F5] rounded-full"
              onSubmitEditing={() => {
                if (value.length > 1) {
                  // do something
                }
              }}
              returnKeyType="send"
            />
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                if (value.length > 1) {
                  //DO something
                }
              }}
              className="absolute right-2"
            >
              <Image
                source={require("../../assets/images/send_plane_fill.png")}
                className="h-[24px] w-[24px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default Chat;
