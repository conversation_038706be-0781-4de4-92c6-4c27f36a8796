import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Image,
} from "react-native";
import React, { Dispatch, SetStateAction, useState } from "react";

interface Props {
  placeHolder: string;
  type: string;
  text: string;
  value: string;
  setValue: (value: any, key: any) => void;
  showPassword?: boolean;
  setShowPassword?: Dispatch<SetStateAction<boolean>>;
  ObjKey?: string;
  error?: string;
}

const Input = ({
  placeHolder,
  text,
  type,
  value,
  setValue,
  showPassword,
  setShowPassword,
  ObjKey,
  error,
}: Props) => {
  const [isFocused, setIsFocused] = useState(false);

  const showError = !isFocused;
  return (
    <View className="w-full gap-y-1">
      <Text className="text-[#4A4C50] font-medium text-[15px]">{text}</Text>
      <View className="w-full relative">
        <TextInput
          className={`w-full h-[48px] rounded-[6px] bg-[#F6F6F6] px-3 mt-1 focus:outline-none ${
            isFocused ? "border border-[#473BF0]" : ""
          }`} // Add border when focused
          placeholder={placeHolder}
          placeholderTextColor="#A9A9A9"
          secureTextEntry={type === "password" && !showPassword}
          value={value}
          onChangeText={(text) => setValue(text, ObjKey)}
          onFocus={() => setIsFocused(true)} // Set focus state
          onBlur={() => setIsFocused(false)} // Reset focus state
        />
        {value.length > 0 && (
          <TouchableOpacity
            className={`absolute top-[19px] ${
              type === "password" ? "right-10" : "right-3"
            }`}
            activeOpacity={0.9}
            onPress={() => setValue("", ObjKey)}
          >
            <Image
              source={require("../assets/images/close.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        )}

        {type === "password" && (
          <TouchableOpacity
            className="absolute right-3 top-[15px]"
            activeOpacity={0.9}
            onPress={() => setShowPassword && setShowPassword(!showPassword)}
          >
            <Image
              source={
                showPassword
                  ? require("../assets/images/eye.png")
                  : require("../assets/images/eye_close.png")
              }
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        )}
      </View>

      {value.length >= 1 && error?.length! >= 0 ? (
        <View className="mt-1">
          <Text className="text-red-500 text-xs">{error}</Text>
        </View>
      ) : (
        <Text className="hidden"></Text>
      )}
    </View>
  );
};

export default Input;
