import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Image } from 'react-native';

interface TripDetails {
  id: string;
  driverName?: string;
  origin: {
    name: string;
    lat: number;
    lng: number;
  };
  destination: {
    name: string;
    lat: number;
    lng: number;
  };
  timestamp: string;
  pricePerSeat?: number;
  // Additional fields for complete trip data
  type?: string;
  mode?: string;
  status?: string;
  noOfPassengers?: number;
  driver?: any;
  stops?: any[];
  preferences?: any[];
  passengers?: any[];
}

interface PickupConfirmedNotificationProps {
  tripDetails: TripDetails;
  passengerName?: string;
  timestamp: string;
  onViewTrip?: () => void;
  onDismiss?: () => void;
}

const PickupConfirmedNotification: React.FC<PickupConfirmedNotificationProps> = ({
  tripDetails,
  passengerName,
  timestamp,
  onViewTrip,
  onDismiss,
}) => {
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleViewTrip = () => {
    if (onViewTrip) {
      onViewTrip();
    } else {
      router.push({
        pathname: "/(ride)/TripSummary",
        params: {
          trip: encodeURIComponent(JSON.stringify(tripDetails)),
        },
      });
    }
  };

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
  };

  return (
    <View className="flex w-full flex-col bg-[#FFF] rounded-md p-4 mx-4 mb-[12px]">
      {/* Header */}
      <View className="flex flex-row justify-between items-center mb-[6px]">
        <Text className="text-green-500 text-[12px] font-semibold">Pickup Confirmed</Text>
        <View className="flex flex-row items-center gap-1">
          <Text className="text-[#787A80] text-xs font-normal">
            {formatTime(timestamp)}
          </Text>
          <Image 
            source={require("../assets/images/goNew.png")} 
            className="w-2 h-3" 
          />
        </View>
      </View>

      {/* Content */}
      <View className="">
              <Text className="text-base font-semibold text-[#151B2D] mb-[4px]">
                The driver has confirmed your pickup
              </Text>
      
              {/* Route Information */}
              <View className='flex flex-row mb-[4px] items-center justify-between'>
                <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm font-medium">
                  {tripDetails.origin.name || 'Unknown origin'}
                </Text>
                <Image source={require("../assets/images/fromandtoNew.png")} className="mx-2 w-3 h-3" />
                <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm text-right font-medium">
                  {tripDetails.destination.name || 'Unknown destination'}
                </Text>
              </View>
      
              {/* Trip Details */}
              <View className="flex-row mb-2 items-center space-x-2">
                <View className='gap-1 flex-row items-center'>
                  <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4' />
                  <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
                </View>
                <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                <View className='flex-row items-center'>
                  <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                    {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleDateString([], {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'} -
                  </Text>
                  <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                    {tripDetails?.timestamp ? new Date(tripDetails.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A'}
                  </Text>
                </View>
                <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                <View>
                  <Text className="text-sm font-medium text-[#787A80]">₦{tripDetails?.pricePerSeat || '0'}</Text>
                </View>
              </View>
            </View>
    </View>
  );
};

export default PickupConfirmedNotification;
