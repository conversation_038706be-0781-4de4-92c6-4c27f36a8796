import { View, Text, StyleSheet } from "react-native";
import React, { useEffect, useState } from "react";
import { useUser } from "@/context/UserContext";
import MapView, {
  PROVIDER_DEFAULT,
  Circle,
  Marker,
  MarkerAnimated,
} from "react-native-maps";
import { Trip, TripRequestsResponse } from "@/utils/types";
import MapViewDirections from "react-native-maps-directions";
import { getTravelTimeAndDistance } from "@/utils/helpers";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  markers: TripRequestsResponse;
  direction: boolean;
  directionOrigin: { latitude: number; longitude: number };
  directionDestination: {
    latitude: number;
    longitude: number;
  };
  setMarkerDirection: (latitude: number, longitude: number, id: string) => void;
  activeTrip: Trip;
}

const DriverTripMap = ({
  step,
  markers,
  direction,
  directionOrigin,
  directionDestination,
  setMarkerDirection,
  activeTrip,
}: Props) => {
  const { initialRegion, displayCurrentAddress } = useUser();

  // const { travelData } = useTravelTimeAndDistance(
  //   activeTrip?.origin?.name,
  //   activeTrip?.destination.name
  // );

  return (
    <MapView
      region={initialRegion}
      userInterfaceStyle="light"
      showsUserLocation={true}
      style={styles.map}
      className="relative"
      initialRegion={initialRegion}
      provider={PROVIDER_DEFAULT}
    >
      <Circle
        center={initialRegion}
        radius={50}
        strokeWidth={1}
        strokeColor="#4639f533"
        fillColor="#4639f533"
      />

      {/* {step < 4 &&
        generatePassengers?.map((passenger) => {
          return (
            <>
              <Marker
                key={passenger?.id}
                coordinate={passenger?.coordinates}
                style={{ zIndex: 100 }}
                onPress={() => {
                  setMarkerDirection(
                    passenger?.coordinates?.latitude,
                    passenger?.coordinates?.longitude,
                    passenger?.id
                  );
                }}
              >
                <View className="relative">
                  <View className="h-[50px] w-[50px] p-1 bg-[#E5850C] rounded-full items-center justify-center">
                    <View className="items-center justify-center">
                      <Text className="text-[10px] font-semibold text-white">
                        {passenger?.distance}
                      </Text>
                    </View>
                  </View>
                  <View className="absolute w-[4px] h-[35px] bg-[#E5850C] -bottom-7 right-[45%]">
                    <View className="h-[14px] border-[3px] border-[#E5850C] w-[14px] rounded-full bg-white bottom-[0.2px] absolute -right-[5px]"></View>
                  </View>
                </View>
              </Marker>
            </>
          );
        })} */}

      {step < 4 &&
        markers?.data?.map((marker) => {
          // const distanceAway =
          return (
            <>
              <Marker
                key={marker.id}
                coordinate={{
                  latitude: marker.currentLocation.lat,
                  longitude: marker.currentLocation.lng,
                }}
                style={{ zIndex: 100 }}
                onPress={() => {
                  setMarkerDirection(
                    marker.currentLocation.lat,
                    marker.currentLocation.lng,
                    marker.id
                  );
                }}
              >
                <View className="relative">
                  <View className="h-[50px] w-[50px] p-1 bg-[#E5850C] rounded-full items-center justify-center">
                    <View className="items-center justify-center">
                      <Text className="text-[16px] font-semibold text-white">
                        2
                      </Text>
                      <Text className="text-white text-[10px]">mins</Text>
                    </View>
                  </View>
                  <View className="absolute w-[4px] h-[35px] bg-[#E5850C] -bottom-7 right-[45%]">
                    <View className="h-[14px] border-[3px] border-[#E5850C] w-[14px] rounded-full bg-white bottom-[0.2px] absolute -right-[5px]"></View>
                  </View>
                </View>
              </Marker>
            </>
          );
        })}

      {step === 4 && (
        <Marker
          key={`marker-${activeTrip?.driver?.id}`}
          coordinate={{
            latitude: activeTrip?.destination?.lat,
            longitude: activeTrip?.destination?.lng,
          }}
          // image={require("../assets/images/user-marker.png")}
          opacity={1}
        >
          <View className="relative">
            <View className="h-[50px] w-[50px] p-1 bg-[#151B2D] rounded-full items-center justify-center">
              <View className="items-center justify-center">
                <Text className="text-[10px] text-center font-semibold text-white">
                  {/* {travelData?.duration} */}
                </Text>
              </View>
            </View>
            <View className="absolute w-[4px] h-[35px] bg-[#151B2D] -bottom-7 right-[45%]"></View>
          </View>
        </Marker>
      )}

      {step === 4 && (
        <MapViewDirections
          origin={initialRegion}
          destination={{
            latitude: activeTrip?.destination?.lat,
            longitude: activeTrip?.destination?.lng,
          }}
          apikey={process.env.EXPO_PUBLIC_GOOGLE_API_KEY as string}
          strokeWidth={4}
          strokeColor="#473BF0"
        />
      )}

      {step === 5 && (
        <MarkerAnimated
          key={`marker-${activeTrip?.driver?.id}`}
          coordinate={{
            latitude: activeTrip?.destination?.lat,
            longitude: activeTrip?.destination?.lng,
          }}
          image={require("../assets/images/trip-ended.png")}
          opacity={1}
        />
      )}

      {direction === true && (
        <MapViewDirections
          origin={directionOrigin}
          destination={directionDestination}
          apikey={process.env.EXPO_PUBLIC_GOOGLE_API_KEY as string}
          strokeWidth={4}
          strokeColor="#473BF0"
        />
      )}
    </MapView>
  );
};

export default DriverTripMap;

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});
