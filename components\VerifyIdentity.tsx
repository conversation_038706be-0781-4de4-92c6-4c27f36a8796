import { View, Text, TextInput } from "react-native";
import React, { Dispatch, SetStateAction, useState } from "react";
import CustomPickerDropdown from "./CustomPickerDropdown"; // Import your custom component
import Button from "./Button";
import { Href, router } from "expo-router";
import { useMutation, useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

interface Props {
  step: number;
  setStep: Dispatch<SetStateAction<number>>;
}

const VerifyIdentity: React.FC<Props> = ({ step, setStep }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["verification"],
    queryFn: () => services.getCompleteVerification(),
  });

  console.log("data", data.data);

  const [selected, setSelected] = useState<string | number | null>(null);
  const [formValue, setFormValue] = useState("");

  const options = [
    { label: "Bank Verification Number", value: "BVN" },
    {
      label: "National Identification Number",
      value: "NIN",
    },
  ];

  console.log(selected);

  const { mutate: uploadIdentity, isPending } = useMutation({
    mutationFn: services.userIdentity,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Identity updated",
      });

      setTimeout(() => {
        router.replace("/Status");
      }, 500);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <View className="relative h-[90%] w-full">
      <Text className="text-[#151B2D] font-semibold text-[20px] my-4">
        Verify Identity
      </Text>

      <View>
        <CustomPickerDropdown
          label="Select identification type"
          items={options}
          onValueChange={(value) => setSelected(value)}
          value={selected}
          placeholder={{ label: "Please select", value: null }}
        />
      </View>

      {selected && (
        <View className="mt-3">
          <Text className="text-[15px] text-[#4A4C50] font-medium mb-1">
            {selected}
          </Text>
          <TextInput
            className="w-full h-[48px] rounded-[6px] bg-[#F6F6F6] px-3"
            placeholder="Ex. 34565678900"
            placeholderTextColor="#A9A9A9"
            value={formValue}
            onChangeText={(text) => setFormValue(text)}
          />
        </View>
      )}
      <View className="absolute bottom-[10%] w-full">
        <Button
          buttonDisabled={isPending}
          isLoading={isPending}
          text="Next"
          buttonClassName="bg-[#473BF0]"
          textClassName="text-white"
          onClick={() =>
            selected && uploadIdentity({ dataBody: { type: selected as string, value: formValue } })
          }
        />
      </View>
    </View>
  );
};

export default VerifyIdentity;