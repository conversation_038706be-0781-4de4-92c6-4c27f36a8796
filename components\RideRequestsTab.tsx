import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, Image, ActivityIndicator, ScrollView } from 'react-native';
import { useQuery, useMutation } from '@tanstack/react-query';
import { services } from '@/services';
import useCurrentUser from '@/hooks/useCurrentUser';
import useActiveTrip from '@/hooks/useActiveTrip';
import Toast from 'react-native-toast-message';
import { Realtime } from 'ably';
import { queryClient } from '@/providers';
import { useTripStore } from '@/app/store/tripStore';
import { realtimeNotificationService, realtimeNotificationHelpers } from '@/utils/realtimeNotifications';



interface LoadingState {
  id: string | null;
  type: 'accept' | 'reject' | null;
}

const RideRequestsTab: React.FC = () => {
  const { data: currentUser } = useCurrentUser();
  const { data: activeTrip } = useActiveTrip();
  const ablyRef = useRef<Realtime | null>(null);
  const [loadingRequestId, setLoadingRequestId] = useState<LoadingState>({ id: null, type: null });

  // Zustand store for request statuses
  const { requestStatuses, updateRequestStatus } = useTripStore();

  // Fetch ride requests for the active trip (if any)
  const { data: rideRequests, isLoading, refetch } = useQuery({
    queryKey: ["allRequests", activeTrip?.id],
    queryFn: () => services.getCarpoolRequests("carpool", activeTrip?.id),
    enabled: !!currentUser?.id && !!activeTrip?.id,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });



  // Set up real-time listening for new ride requests
  useEffect(() => {
    if (!currentUser?.id || !activeTrip?.id) return;

    ablyRef.current = new Realtime({
      key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
      clientId: currentUser.id,
      autoConnect: true,
    });

    // Initialize realtime notification service
    realtimeNotificationService.initialize(
      ablyRef.current,
      activeTrip.id,
      currentUser.id,
      'driver' // RideRequestsTab is for drivers
    );

    const channel = ablyRef.current.channels.get("drivers");
    const tripChannel = ablyRef.current.channels.get(activeTrip.id);

    channel.subscribe("ride-request", (message) => {
      console.log("New ride request received:", message.data);
      refetch(); // Refetch requests when new one comes in
    });

    tripChannel.subscribe("ride-request", (message) => {
      console.log("Trip-specific ride request received:", message.data);
      refetch();
    });

    return () => {
      channel.unsubscribe();
      tripChannel.unsubscribe();
      realtimeNotificationService.cleanup();
      ablyRef.current?.close();
    };
  }, [currentUser?.id, activeTrip?.id, refetch]);

  // Accept ride request mutation
  const { mutate: acceptRideRequest } = useMutation({
    mutationFn: services.acceptRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message || "Ride request accepted",
      });
      setLoadingRequestId({ id: null, type: null });

      // Update Zustand store with accepted status
      updateRequestStatus(loadingRequestId.id!, {
        status: 'accepted',
        pickupConfirmed: false
      });

      // Send real-time notification to passenger about booking acceptance
      if (activeTrip?.id && currentUser?.firstName) {
        realtimeNotificationHelpers.notifyBookingAccepted(
          activeTrip.id,
          `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
          {
            id: activeTrip.id,
            driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
            origin: activeTrip.origin,
            destination: activeTrip.destination,
            timestamp: activeTrip.timestamp,
            pricePerSeat: activeTrip.pricePerSeat,
            // Include full trip data for complete navigation
            fullTripData: activeTrip
          }
        );
      }

      // Publish to Ably channels for real-time updates
      if (ablyRef.current) {
        const channel = ablyRef.current.channels.get("drivers");
        channel.publish("ride-update", {
          type: "request-accepted",
          tripId: activeTrip?.id,
          requestId: loadingRequestId.id,
          timestamp: new Date().toISOString()
        });

        // Also publish to trip channel for passenger notifications
        const tripChannelName = `trip-${activeTrip?.id}`;
        const tripChannel = ablyRef.current.channels.get(tripChannelName);

        const bookingData = {
          tripDetails: {
            id: activeTrip?.id,
            driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
            origin: activeTrip?.origin,
            destination: activeTrip?.destination,
            timestamp: activeTrip?.timestamp,
            pricePerSeat: activeTrip?.pricePerSeat,
          },
          fullTripData: activeTrip, // Include complete trip data for navigation
          timestamp: new Date().toISOString()
        };

        tripChannel.publish("booking-accepted", bookingData);

        // Also publish to passenger's user channel
        const acceptedRequest = rideRequests?.data?.find((req: any) => req.requestId === loadingRequestId.id);
        if (acceptedRequest?.id) {
          const userChannelName = `user-${acceptedRequest.id}`;
          const userChannel = ablyRef.current.channels.get(userChannelName);
          userChannel.publish("booking-accepted", bookingData);
        }
      }

      refetch();
      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Failed to accept ride request",
      });
      setLoadingRequestId({ id: null, type: null });
    },
  });

  // Reject ride request mutation
  const { mutate: rejectRideRequest } = useMutation({
    mutationFn: services.rejectRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message || "Ride request declined",
      });
      setLoadingRequestId({ id: null, type: null });

      // Update Zustand store with rejected status
      updateRequestStatus(loadingRequestId.id!, {
        status: 'rejected',
        pickupConfirmed: false
      });

      // Send real-time notification to passenger about booking decline
      if (activeTrip?.id && currentUser?.firstName) {
        realtimeNotificationHelpers.notifyBookingDeclined(
          activeTrip.id,
          `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
          {
            id: activeTrip.id,
            driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
            origin: activeTrip.origin,
            destination: activeTrip.destination,
            timestamp: activeTrip.timestamp,
            pricePerSeat: activeTrip.pricePerSeat,
          }
        );
      }

      // Also publish to trip channel for passenger notifications
      if (ablyRef.current) {
        const tripChannelName = `trip-${activeTrip?.id}`;
        const tripChannel = ablyRef.current.channels.get(tripChannelName);

        const bookingData = {
          tripDetails: {
            id: activeTrip?.id,
            driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
            origin: activeTrip?.origin,
            destination: activeTrip?.destination,
            timestamp: activeTrip?.timestamp,
            pricePerSeat: activeTrip?.pricePerSeat,
          },
          fullTripData: activeTrip, // Include complete trip data for navigation
          timestamp: new Date().toISOString()
        };

        tripChannel.publish("booking-declined", bookingData);

        // Also publish to passenger's user channel
        const rejectedRequest = rideRequests?.data?.find((req: any) => req.requestId === loadingRequestId.id);
        if (rejectedRequest?.id) {
          const userChannelName = `user-${rejectedRequest.id}`;
          const userChannel = ablyRef.current.channels.get(userChannelName);
          userChannel.publish("booking-declined", bookingData);
        }
      }

      refetch();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Failed to decline ride request",
      });
      setLoadingRequestId({ id: null, type: null });
    },
  });

  const handleAcceptRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "accept" });
    acceptRideRequest({ dataBody: { requestId } });
  };

  const handleRejectRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "reject" });
    rejectRideRequest({ dataBody: { requestId } });
  };

  const handleConfirmPickup = (requestId: string) => {
    // Update Zustand store to mark pickup as confirmed
    updateRequestStatus(requestId, {
      status: 'accepted',
      pickupConfirmed: true
    });

    // Find the passenger name for the notification
    const confirmedRequest = rideRequests?.data?.find((req: any) => (req.requestId || req.id) === requestId);
    const passengerName = confirmedRequest ? confirmedRequest.firstName : 'Passenger';

    // Send realtime notification to passengers
    if (activeTrip?.id) {
      // Pass complete trip data for proper navigation
      const completeTrip = {
        ...activeTrip,
        status: 'ongoing'
      };

      realtimeNotificationHelpers.notifyPickupConfirmed(activeTrip.id, passengerName, completeTrip);
    }

    // Publish to Ably channels for real-time updates
    if (ablyRef.current && activeTrip?.id) {
      const tripChannel = ablyRef.current.channels.get(`trip-${activeTrip.id}`);
      console.log('📤 Publishing pickup-confirmed to channel:', `trip-${activeTrip.id}`);

      const pickupData = {
        tripDetails: {
          id: activeTrip.id,
          driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
          origin: activeTrip.origin,
          destination: activeTrip.destination,
          timestamp: activeTrip.timestamp,
          pricePerSeat: activeTrip.pricePerSeat,
        },
        passengerName: passengerName,
        requestId: requestId,
        timestamp: new Date().toISOString()
      };

      tripChannel.publish("pickup-confirmed", pickupData);

      // Also publish to passenger's user channel
      const passengerRequest = rideRequests?.data?.find((req: any) => (req.requestId || req.id) === requestId);
      console.log('🔍 Publishing pickup confirmed to user channel:', {
        requestId,
        passengerRequest,
        passengerId: passengerRequest?.id,
        allRequests: rideRequests?.data?.map((req: any) => ({ id: req.id, requestId: req.requestId }))
      });
      if (passengerRequest?.id) {
        const userChannelName = `user-${passengerRequest.id}`;
        console.log('📤 Publishing pickup-confirmed to user channel:', userChannelName);
        const userChannel = ablyRef.current.channels.get(userChannelName);
        userChannel.publish("pickup-confirmed", pickupData);
      } else {
        console.log('❌ Could not find passenger request for pickup confirmation');
      }
    }

    Toast.show({
      type: "success",
      text1: "Pickup confirmed",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const getAvailableSeats = (trip: any) => {
    const totalSeats = trip?.noOfPassengers || 0;
    const occupiedSeats = trip?.passengers?.length || 0;
    return Math.max(0, totalSeats - occupiedSeats);
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="small" color="#473BF0" />
        <Text className="mt-2 text-gray-500">Loading ride requests...</Text>
      </View>
    );
  }

  // Show different messages based on state
  if (!activeTrip?.id) {
    return (
      <View className="flex-1 items-center justify-center rounded-lg p-6 bg-white">
        <Text className="text-lg font-medium mb-2">No active trip</Text>
        <Text className="text-gray-500 mb-4 text-center">
          Post a trip to start receiving ride requests
        </Text>
        <Image
          source={require('../assets/images/homme.png')}
          className="w-24 h-24"
        />
      </View>
    );
  }

  if (!rideRequests?.data || rideRequests.data.length === 0) {
    return (
      <View className="flex-1 items-center justify-center rounded-lg p-6 bg-white">
        <Text className="text-lg font-medium mb-2">No ride requests</Text>
        <Text className="text-gray-500 mb-4 text-center">
          When passengers request to join your active trip, they'll appear here
        </Text>
        <Image
          source={require('../assets/images/homme.png')}
          className="w-24 h-24"
        />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="p-4">
        {/* Trip Info Header - Show once for the active trip */}
        {activeTrip && (
          <View className="mb-3">
            <Text className="text-[#473BF0] text-xs font-semibold mb-1">
              {getAvailableSeats(activeTrip)} seat{getAvailableSeats(activeTrip) !== 1 ? 's' : ''} left
            </Text>
            <View className="flex-row items-center justify-between">
              <Text className="text-[16px] font-semibold flex-1" numberOfLines={1}>
                {activeTrip.origin?.name?.split(",")[0] || 'Unknown'} to {activeTrip.destination?.name?.split(",")[0] || 'Unknown'}
              </Text>
              <View className='flex-row items-center'>
                <Text className="text-[#4A4C50] text-[13px] font-medium">
                  {activeTrip.timestamp ? formatTime(activeTrip.timestamp) : 'N/A'}
                </Text>
                <Image source={require("../assets/images/goNew.png")} className="ml-[3px] w-3 h-4" />
              </View>
              
            </View>
            <View className="flex-row items-center mt-1">
              <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4 mr-1' />
              <Text className="text-sm text-gray-600 mr-2">Coride</Text>
              <View className='h-1 w-1 bg-gray-400 rounded-full mr-2' />
              <Text className="text-sm text-gray-600 mr-2">{activeTrip.noOfPassengers || 0} seats</Text>
              <View className='h-1 w-1 bg-gray-400 rounded-full mr-2' />
              <Text className="text-sm text-gray-600">₦{activeTrip.pricePerSeat || 0}</Text>
            </View>
          </View>
        )}

        {rideRequests.data.map((request: any) => {
          return (
            <View key={request.requestId || request.id} className="mb-4">

              {/* Passenger Request */}
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1">
                  <View className="h-10 w-10 bg-[#EDEDED] rounded-full mr-3 justify-center items-center">
                    <Text className="text-lg font-bold text-gray-700">
                      {request.firstName?.charAt(0)?.toUpperCase() || '?'}
                    </Text>
                  </View>
                  <Text className="text-[15px] font-medium flex-1">
                    {request.firstName} wants to join your trip
                  </Text>
                </View>
              </View>

              {/* Action Buttons */}
              <View className="flex-row items-center mt-2 gap-x-2">
                <View className="flex-row">
                  {requestStatuses[request.requestId]?.status === "accepted" ? (
                    requestStatuses[request.requestId]?.pickupConfirmed ? (
                      <View className='rounded-full p-2'>
                        <Text className='text-green-500 text-[10px]'>Pickup Confirmed</Text>
                      </View>
                    ) : (
                      <TouchableOpacity
                        onPress={() => handleConfirmPickup(request.requestId || request.id)}
                        className="bg-[#473BF0] rounded-full w-[116px] h-[34px] flex items-center justify-center"
                      >
                        <Text className="text-white font-semibold text-xs py-[6px] px-[5px] ">Confirm Pickup</Text>
                      </TouchableOpacity>
                    )
                  ) : requestStatuses[request.requestId]?.status === "rejected" ? (
                    <View className='rounded-full p-2'>
                      <Text className='text-red-500 text-[10px]'>Request Declined</Text>
                    </View>
                  ) : (
                    <>
                      <TouchableOpacity
                        onPress={() => handleRejectRideRequest(request.requestId || request.id)}
                        disabled={loadingRequestId.id === (request.requestId || request.id)}
                        className="bg-[#EDEDED] rounded-full w-[116px] h-[34px] flex items-center justify-center mr-2"
                      >
                        {loadingRequestId.id === (request.requestId || request.id) && loadingRequestId.type === "reject" ? (
                          <ActivityIndicator size="small" color="#151B2D" />
                        ) : (
                          <Text className="text-[#151B2D] font-semibold text-xs">Decline</Text>
                        )}
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => handleAcceptRideRequest(request.requestId || request.id)}
                        disabled={loadingRequestId.id === (request.requestId || request.id)}
                        className="bg-[#34A853] rounded-full w-[116px] h-[34px] flex items-center justify-center"
                      >
                        {loadingRequestId.id === (request.requestId || request.id) && loadingRequestId.type === "accept" ? (
                          <ActivityIndicator size="small" color="white" />
                        ) : (
                          <Text className="text-white font-semibold text-xs">Accept</Text>
                        )}
                      </TouchableOpacity>
                    </>
                  )}
                </View>

                {/* Contact Icons */}
                <View className="flex-row">
                  <TouchableOpacity className="mr-2">
                    <Image source={require("../assets/images/Chat.png")} className="w-[34px] h-[34px]" />
                  </TouchableOpacity>
                  <TouchableOpacity className="">
                    <Image source={require("../assets/images/Call.png")} className="w-[34px] h-[34px]" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

export default RideRequestsTab;
