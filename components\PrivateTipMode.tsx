import React, { useState } from 'react';
import { View, Text, Switch } from 'react-native';
import { styled } from "nativewind";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledSwitch = styled(Switch);

const PrivateTipMode = () => {
    const [isPrivateTipMode, setIsPrivateTipMode] = useState(false);

    const toggleSwitch = () => setIsPrivateTipMode(previousState => !previousState);

    return (
        <StyledView className="flex-1 flex-row items-center justify-between bg-[#F9F9F9] p-3 my-3">
            <StyledView className="flex-1 flex-col">
                <StyledText className="text-xl font-bold">
                    {isPrivateTipMode ? 'Private Tip Mode' : 'Private Mode'}
                </StyledText>
                <StyledText>
                    If turned on, you will only be booked by a single
                </StyledText>
                <StyledText>
                    passenger for the entire ride.
                </StyledText>
            </StyledView>

            <StyledSwitch
                style={{ flexShrink: 0 }}
                trackColor={{ false: "#767577", true: "#34C759" }}
                thumbColor={isPrivateTipMode ? "#f4f3f4" : "#f4f3f4"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={toggleSwitch}
                value={isPrivateTipMode}
            />
        </StyledView>
    );
};

export default PrivateTipMode;
