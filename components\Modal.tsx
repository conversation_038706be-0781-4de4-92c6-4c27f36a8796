import BottomSheet, { BottomSheetBackdrop, BottomSheetTextInput } from "@gorhom/bottom-sheet";
import { forwardRef, useCallback, useMemo } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  ViewStyle,
  TextStyle,
} from "react-native";
import Animated, { useAnimatedStyle, interpolate } from "react-native-reanimated";

export type Ref = BottomSheet;

interface Props {
  children: React.ReactNode;
  index: number;
  customSnapPoints: string[] | number[];
  goBack?: boolean;
  handleGoBack?: () => void;
  showTextInput?: boolean;
  textInputProps?: {
    value?: string;
    onChangeText?: (text: string) => void;
    placeholder?: string;
    multiline?: boolean;
    numberOfLines?: number;
    style?: ViewStyle;
    textStyle?: TextStyle;
  };
}

const CustomBottomSheet = forwardRef<Ref, Props>(
  ({ 
    children, 
    index, 
    customSnapPoints, 
    goBack, 
    handleGoBack,
    showTextInput = false,
    textInputProps = {}
  }, ref) => {
    const snapPoints = useMemo(() => customSnapPoints, [customSnapPoints]);

    const renderBackdrop = useCallback(
      (props: any) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
        />
      ),
      []
    );

    const defaultTextInputStyle = {
      backgroundColor: "#F4F4F4",
      padding: 12,
      borderRadius: 6,
      fontSize: 14,
      color: "#151B2D",
    } as const;

    return (
      <View style={styles.container}>
        {goBack && (
          <TouchableOpacity
            onPress={handleGoBack}
            activeOpacity={0.9}
            className="p-2 bg-white rounded-full absolute top-[48%] h-fit ml-4 z-50"
          >
            <Image
              source={require("../assets/images/Back.png")}
              className="h-[24px] w-[24px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        )}

        <BottomSheet
          ref={ref}
          index={index}
          snapPoints={snapPoints}
          enablePanDownToClose={false}
          handleIndicatorStyle={{ backgroundColor: "#FFFFFF" }}
          backgroundStyle={{ backgroundColor: "#FFFFFF" }}
          style={styles.bottomSheet}
          backdropComponent={renderBackdrop}
        >
          {showTextInput && (
            <BottomSheetTextInput
              {...textInputProps}
              style={[
                defaultTextInputStyle,
                textInputProps.style as TextStyle
              ]}
            />
          )}
          {children}
        </BottomSheet>
      </View>
    );
  }
);

export default CustomBottomSheet;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  bottomSheet: {
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
  },
});
