export type Driver = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  isVerified: boolean;
  carDetails: CarDetails;
}

interface CarDetails {
  make: string;
  model: string;
  colour: string;
  yearOfManufacture: string;
  chassisNumber: string;
  passengerCapacity: number;
  plateNumber: string;
}

interface Location {
  name: string;
  lng: number;
  lat: number;
}

export type Passenger = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  isVerified: boolean;
  modeOfPayment: string;
  currentLocation: Location;
  pickup: Location;
  dropoff: Location;
  requestId: string;
  requestedAt: string;
};

interface Preference {
  desc: string;
  value: boolean;
}

// Interface for the location coordinates
interface Coordinates {
  coordinates: [number, number]; // [longitude, latitude]
  type: string;
}

// Interface for a stop along the route
interface Stop {
  location: Coordinates;
  name: string;
}

// Interface for a ride
// export type Ride = {
//   id: string; // Unique identifier for the ride
//   driver: Driver;
//   passengers: any[]; // You can specify a more detailed type if needed
//   type: string; // e.g., "one-time"
//   origin: Location;
//   destination: Location;
//   stops: Stop[];
//   timestamp: string; // ISO 8601 date string
//   noOfPassengers: number;
//   pricePerSeat: number;
//   preferences: Preference[];
//   __v: number; // Version key, often used in databases
// };

// // Example type for the rides array
// export type Rides = Ride[];

export type RideShare = {
  id: string;
  status: "pending" | string; // Add other possible status values if known
  type: "one-time" | string; // Add other possible type values if known
  driver: Driver;
  passengers: string[]; // Array of passenger IDs
  origin: Location;
  destination: Location;
  stops: Stop[];
  timestamp: string;
  noOfPassengers: number;
  pricePerSeat: number;
  preferences: Preference[];
};

// interface TripData {
//   tripId: string;
//   modeOfPayment: string;
//   currentLocation: Location;
//   pickup: Location;
//   dropoff: Location;
// }

interface TripRequest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  isVerified: boolean;
  modeOfPayment: string;
  currentLocation: Location;
  pickup: Location;
  dropoff: Location;
  requestId: string;
  requestedAt: string;
}

export type TripRequestsResponse = {
  status: boolean;
  message: string;
  type: string;
  data: TripRequest[];
};

export type Trip = {
  id: string;
  status: string;
  type: string;
  driver: Driver;
  passengers: Passenger[];
  origin: Location;
  destination: Location;
  stops: Stop[];
  timestamp: string;
  noOfPassengers: number;
  pricePerSeat: number;
  mode: string;
  preferences: Preference[];
  userId: string;
  driverNote?: string;
};

export type TripData = {
  id: string;
  status: string;
  type: string;
  driver: Driver;
  origin: Location;
  destination: Location;
  stops: Location[];
  timestamp: string;
  noOfPassengers: number;
  pricePerSeat: number;
  mode: string;
  preferences: Preference[];
};

export type PrivateRide = {
  estimatedPrice: number;
  drivers: Driver[];
};
