import React, { useRef } from "react";
import { View, Text, Image } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";

import { router } from "expo-router";
import { useRide } from "@/context/RideProvider";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { queryClient } from "@/providers";

interface Props {
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  tripId?: string;
}

const DriverCancelRide: React.FC<Props> = ({ setCancelRide, tripId }) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { toggleSwitch, setIsEnabled, deactivatePrivateRide } = useRide();

  const customSnapPoints = ["34%"];

  const { mutate: handleCancelTrip, isPending } = useMutation({
    mutationFn: services.cancelTrip,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      deactivatePrivateRide();
      queryClient.invalidateQueries({ queryKey: ["trip"] });
      setTimeout(() => {
        router.replace("/(tabs)/Home");
      }, 1000);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 justify-center w-full bg-[#FFFFFF]">
        <View className="w-[90%] mx-auto items-center gap-y-3 py-4">
          <Image
            source={require("../assets/images/delete.png")}
            className="h-[46px] w-[46px]"
            resizeMode="contain"
          />

          <Text className="text-[#151B2D] text-[20px] font-semibold">
            Cancel trip?
          </Text>
          <Text className="text-[#151B2D] text-[15px]">
            Are you sure you want to cancel
          </Text>
          <View className="w-full">
            <Button
              isLoading={isPending}
              text="Cancel"
              textClassName="text-[#E05859]"
              buttonClassName="bg-[#********] w-full mb-2"
              onClick={() => {
                handleCancelTrip(tripId || null);
              }}
            />
            <Button
              text="Back"
              textClassName="text-[#151B2D]"
              buttonClassName="bg-[#EDEDED] w-full mb-2"
              onClick={() => setCancelRide(false)}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DriverCancelRide;
