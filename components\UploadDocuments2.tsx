import {
  View,
  Text,
  Safe<PERSON>reaView,
  <PERSON><PERSON><PERSON>iew,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import React, { Dispatch, SetStateAction, useState } from "react";
import * as ImagePicker from "expo-image-picker";
import PickImage from "./PickImage";
import Button from "./Button";

import * as SecureStore from "expo-secure-store";
import axios from "axios";
import Toast from "react-native-toast-message";
import useCurrentUser from "@/hooks/useCurrentUser";
interface Props {
  step: number;
  setStep: Dispatch<SetStateAction<number>>;
}

const UploadDocuments2: React.FC<Props> = ({ step, setStep }) => {
  const [loading, setLoading] = useState(false);
  const { data } = useCurrentUser();
  const [images, setImages] = useState({
    motorInsurance: data?.verification?.motorInsurance || "",
    roadWorthiness: data?.verification?.roadWorthiness || "",
  });

  const pickImageAsync = async (imageType: keyof typeof images) => {
    let result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled) {
      setImages({ ...images, [imageType]: result.assets[0].uri });
    } else {
      alert("You did not select any image.");
    }
  };

  const uploadImage = async (type: string, imageUri: string) => {
    try {
      const tokenData = await SecureStore.getItemAsync("authToken");

      if (!tokenData) {
        throw new Error("No authentication token found");
      }

      const parsedToken = JSON.parse(tokenData);

      const formData = new FormData();
      formData.append("type", type);
      formData.append("image", {
        uri: imageUri,
        type: "image/jpeg",
        name: `${type}.jpg`,
      } as any);

      const response = await axios.post(
        `${process.env.EXPO_PUBLIC_BASE_URL}/user/verification/image`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${parsedToken.token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);

      throw error;
    }
  };

  const uploadAllImages = async () => {
    setLoading(true);
    try {
      const uploads = [
        uploadImage("motorInsurance", images.motorInsurance),
        uploadImage("roadWorthiness", images.roadWorthiness),
      ];

      const results = await Promise.all(uploads);
      console.log("Upload results:", results);
      Toast.show({
        type: "success",
        text1: "All images uploaded successfully",
      });
      setTimeout(() => {
        setStep(step + 1);
      }, 400);
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to upload one or more images",
      });
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="mt-4">
          <View>
            <Text className="text-[#151B2D] font-semibold text-[20px]">
              Motor Insurance
            </Text>

            {/* Motor Insurance */}
            <PickImage
              image={images.motorInsurance}
              pickImage={() => pickImageAsync("motorInsurance")}
              text="Motor Insurance"
            />

            {/* Road Worthiness */}
            <PickImage
              image={images.roadWorthiness}
              pickImage={() => pickImageAsync("roadWorthiness")}
              text="Road Worthiness"
            />
          </View>
        </View>
      </ScrollView>
      <View style={{ marginTop: "auto", width: "100%", paddingBottom: 10 }}>
        <Button
          buttonDisabled={
            loading || !images.motorInsurance || !images.roadWorthiness
          }
          isLoading={loading}
          text="Next"
          buttonClassName="bg-[#473BF0]"
          textClassName="text-white"
          onClick={() => uploadAllImages()}
        />
      </View>
    </SafeAreaView>
  );
};

export default UploadDocuments2;
