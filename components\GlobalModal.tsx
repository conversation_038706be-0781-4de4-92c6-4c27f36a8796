import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import Modal from 'react-native-modal';
import { useNotificationStore, ModalData } from '@/app/store/notificationStore';
import { router } from 'expo-router';
import Button from './Button';

interface GlobalModalProps {
  modal: ModalData;
}

const GlobalModal: React.FC<GlobalModalProps> = ({ modal }) => {
  const { closeModal } = useNotificationStore();

  const handleButtonPress = (button: ModalData['buttons'][0]) => {
    if (!button) return;

    switch (button.action) {
      case 'close':
        closeModal(modal.id);
        break;
      
      case 'navigate':
        closeModal(modal.id);
        if (button.actionData?.route) {
          router.push(button.actionData.route);
        }
        break;
      
      case 'custom':
        if (button.actionData?.callback) {
          button.actionData.callback();
        }
        closeModal(modal.id);
        break;
      
      default:
        closeModal(modal.id);
    }
  };

  const getButtonStyle = (style?: string) => {
    switch (style) {
      case 'destructive':
        return 'bg-red-500';
      case 'primary':
        return 'bg-[#473BF0]';
      default:
        return 'bg-gray-200';
    }
  };

  const getTextStyle = (style?: string) => {
    switch (style) {
      case 'destructive':
      case 'primary':
        return 'text-white';
      default:
        return 'text-gray-800';
    }
  };

  return (
    <Modal
      isVisible={true}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.5}
      onBackdropPress={() => closeModal(modal.id)}
      style={{ margin: 0, justifyContent: 'center', alignItems: 'center' }}
    >
      <View className="bg-white rounded-lg p-6 mx-4 max-w-sm w-full">
        {/* Title */}
        <Text className="text-lg font-semibold text-center mb-3 text-[#151B2D]">
          {modal.title}
        </Text>
        
        {/* Content */}
        <Text className="text-sm text-center mb-6 text-[#787A80] leading-5">
          {modal.content}
        </Text>
        
        {/* Buttons */}
        <View className="space-y-3">
          {modal.buttons?.map((button, index) => (
            <Button
              key={index}
              text={button.text}
              buttonClassName={`${getButtonStyle(button.style)} w-full`}
              textClassName={getTextStyle(button.style)}
              onClick={() => handleButtonPress(button)}
            />
          )) || (
            <Button
              text="OK"
              buttonClassName="bg-[#473BF0] w-full"
              textClassName="text-white"
              onClick={() => closeModal(modal.id)}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

export default GlobalModal;
