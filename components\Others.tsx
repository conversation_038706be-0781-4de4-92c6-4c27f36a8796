import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import { router, Href } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";

const Others = () => {
  const { data } = useCurrentUser();

  return (
    <View className="bg-white rounded-[9px] p-3 mt-5">
      <View className="mt-1">
        <Text className="text-[#4A4C50] pb-3 text-[14px] font-medium opacity-50">
          Others
        </Text>

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Promo" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/refernew.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          Refer a Friend
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

                    

          <View className="w-full h-[1px] my-2 bg-white-100" />

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Promo" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/promonew.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          Promo Codes
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

                    <View className="w-full h-[1px] my-2 bg-white-100" />

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Security" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/faqnew.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          FAQs
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

                    <View className="w-full h-[1px] my-2 bg-white-100" />

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Security" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/aboutnew.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          About Us
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

          
        </View>
      </View>
    
  );
};

export default Others;
