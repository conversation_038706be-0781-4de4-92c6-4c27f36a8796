import {
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  SafeAreaView,
  StatusBar,
} from "react-native";
import React from "react";
import Button from "@/components/Button";
import Input from "@/components/Input";
import { Href, router } from "expo-router";
import { useRide } from "@/context/RideProvider";

const Pricing = () => {
  const { ride, updateRide, preferences } = useRide();

  const handleNavigateToSummary = () => {
    const tripData = {
      type: ride.type,
      origin: ride.origin,
      destination: ride.destination,
      stops: ride.stops,
      noOfPassengers: ride.noOfPassengers,
      pricePerSeat: Number(ride.pricePerSeat),
      preferences: preferences,
      timestamp: ride.timestamp,
      mode: "carpool",
      isPreview: true,
    };

    router.push({
      pathname: "/TripSummary",
      params: {
        trip: encodeURIComponent(JSON.stringify(tripData)),
      },
    } as Href);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="w-full h-full bg-white">
          <StatusBar barStyle="dark-content" backgroundColor="transparent" />
          <View className="w-[90%] h-full mx-auto mt-4 relative">
            <View className="mt-2">
              <Input
                text="Price per seat"
                placeHolder="N0.00"
                value={ride.pricePerSeat}
                setValue={(value) => updateRide("pricePerSeat", value)}
                type="number"
              />

              <Text className="text-[#4A4C50] text-[15px] font-medium mt-3">
                Note: this can be changed anytime
              </Text>
            </View>

            <Button
              onClick={handleNavigateToSummary}
              buttonDisabled={
                ride.pricePerSeat === "0" ||
                ride.pricePerSeat.length < 1
              }
              text="Review Trip"
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] absolute bottom-[3%]"
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default Pricing;