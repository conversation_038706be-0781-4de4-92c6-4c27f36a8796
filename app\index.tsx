import './gesture-handler';
import 'react-native-gesture-handler';
import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import * as SecureStore from "expo-secure-store";
import { View, ActivityIndicator } from "react-native";
import 'react-native-get-random-values'

const Page = () => {
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const checkIfUserIsSignedIn = async () => {
    try {
      const tokenData = await SecureStore.getItemAsync("authToken");
      if (tokenData) {
        const { expiration } = JSON.parse(tokenData);
        const currentTime = new Date().getTime();
        if (currentTime < expiration) {
          setIsSignedIn(true);
        } else {
          // Token is expired, clear it if necessary
          await SecureStore.deleteItemAsync("authToken");
        }
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkIfUserIsSignedIn();
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        {/* <ActivityIndicator size="large" /> */}
      </View>
    );
  }

  if (isSignedIn) return <Redirect href="/Home" />;

  return <Redirect href="/Onboarding" />;
};

export default Page;