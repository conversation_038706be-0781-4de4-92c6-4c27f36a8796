import GoBack from "@/components/GoBack";
import { router, Stack } from "expo-router";
import { Image, View, Text, TouchableOpacity } from "react-native";

export default function RideLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="FindARide"
        options={{
          title: "Find a ride",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

<Stack.Screen
        name="FindRideScreen"
        options={{
          title: "Need Help?",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="BookRide"
        options={{ headerShown: false, title: "Book a ride" }}
      />
      <Stack.Screen
        name="OfferRide"
        options={{
          title: "Trip schedule",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="TripPreference"
        options={{
          title: "Trip Preference",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="Pricing"
        options={{
          title: "Pricing",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="PrivatePassenger"
        options={{
          title: "Find a private driver",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="PostRide"
        options={{ headerShown: false, title: "Post Ride" }}
      />
      <Stack.Screen
        name="PrivatePassengerMap"
        options={{
          headerShown: false,
          headerShadowVisible: true,

          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
        }}
      />
      <Stack.Screen
        name="PrivateDriver"
        options={{
          title: "Find a private driver",
          headerShown: false,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="[chat]"
        options={{
          presentation: "modal",
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => router.dismiss()}
            >
              <Image
                source={require("../../assets/images/close_line.png")}
                className="w-[24px] h-[24px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          ),
          headerTitle: () => (
            <View className="flex-row space-x-2">
              <Image
                source={require("../../assets/images/ProfilePic.png")}
                className="h-[37px] w-[37px]"
                resizeMode="contain"
              />
              <View>
                <Text className="text-[15px] text-[#151B2D] font-medium">
                  Jojo
                </Text>
                <Text className="text-xs text-[#8F8F8F]">
                  Mercedes benz - ABJ680RU
                </Text>
              </View>
            </View>
          ),
          headerRight: () => (
            <Image
              source={require("../../assets/images/phone_fill.png")}
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          ),
          headerShadowVisible: true,
          headerBackTitle: "",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="TripSummary"
        options={{
          title: "Trip Summary",
          headerShown: true,
          headerLeft: () => <GoBack color="#FFFFFF" />,
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#F4F4F4" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
    </Stack>
  );
}
