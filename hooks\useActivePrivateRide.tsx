import { services } from "@/services";
import { useQuery } from "@tanstack/react-query";

const useActivePrivateRide = () => {
  const {
    data,
    isLoading,
    error,
    refetch: refetchActive,
  } = useQuery({
    queryKey: ["trip"],
    queryFn: () => services.getActivePrivateRide(),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });

  return { data, isLoading, error, refetchActive };
};

export default useActivePrivateRide;
