import React, { useRef } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import { Trip } from "@/utils/types";
import { useRide } from "@/context/RideProvider";

interface Props {
  setShowPassengers: React.Dispatch<React.SetStateAction<boolean>>;
  setShowPassenger: React.Dispatch<React.SetStateAction<boolean>>;
  trip: Trip;
}
const PassengersDetails = ({
  setShowPassengers,
  setShowPassenger,
  trip,
}: Props) => {
  const { updateRide } = useRide();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const passengers = trip.passengers?.map((passenger) => {
    return {
      id: passenger.id,
      name: passenger.firstName,
      destination: passenger.currentLocation.name,
    };
  });

  const customSnapPoints = ["36%"];
  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto space-y-1">
          <TouchableOpacity
            onPress={() => setShowPassengers(false)}
            activeOpacity={0.7}
            className="flex-row justify-between items-center my-3"
          >
            <Text className="text-[20px] text-[#151B2D] font-semibold">
              Passenger details
            </Text>
            <Image
              source={require("../assets/images/close_line.png")}
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
          {passengers.map((passenger) => (
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                updateRide("selectedPassenger", passenger.id);
                setShowPassenger(true);
              }}
              key={passenger.id}
              className="flex-row space-x-4 items-center border-[0.3px] border-[#2525253a] p-2 drop-shadow-sm rounded-[6px]"
            >
              <Image
                source={require("../assets/images/ProfilePic.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <View className="flex-1 space-y-1 justify-center">
                {/* Use flex-1 here */}
                <Text className="text-[15px] text-[#151B2D] font-medium">
                  {passenger.name}
                </Text>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[#8F8F8F] text-[13px] flex-wrap"
                >
                  {passenger.destination}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
};

export default PassengersDetails;
