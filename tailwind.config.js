/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "custom-gradient":
          "linear-gradient(0deg, #000000 0%, rgba(0, 0, 0, 0) 100%)",
      },
      fontFamily: {
        pbold: ["BodoniModa", "sans-serif"],
        Ibold: ["BoldItalic", "sans-serif"],
        tensor: ["TensorRegular", "sans-serif"],
        tenorRegular: ["TenorSans", "sans-serif"],
        pbold: ["BodoniModa", "sans-serif"],
        Ibold: ["BoldItalic", "sans-serif"],
        tensor: ["TensorRegular", "sans-serif"],
      },
    },
  },
  plugins: [],
};
