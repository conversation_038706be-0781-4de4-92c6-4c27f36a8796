import React, { useRef } from "react";
import { View, Text, Image, TouchableOpacity, ScrollView } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import { router } from "expo-router";
import { Trip } from "@/utils/types";
import useActiveTrip from "@/hooks/useActiveTrip";
import { makePhoneCall } from "@/utils/helpers";

interface Props {
  setShowPassenger: React.Dispatch<React.SetStateAction<boolean>>;
  trip: Trip;
}
const DriverInformation = ({ setShowPassenger, trip }: Props) => {
  const bottomSheetRef = useRef<BottomSheet>(null);

  const customSnapPoints = ["40%"];

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        className="flex-1 w-full relative bg-[#FFFFFF]"
      >
        <View className="flex-1 w-[90%] mx-auto">
          <View className="flex-row justify-between items-center mt-2">
            <View className="gap-y-[6px] flex-row justify-between w-full">
              <View className="flex-row space-x-2 items-center">
                <Image
                  source={require("../assets/images/ProfilePic.png")}
                  className="h-[44px] w-[44px]"
                  resizeMode="contain"
                />
                <View className="space-y-1">
                  <Text className="text-[20px] text-[#151B2D] font-semibold">
                    {trip?.driver?.firstName}
                  </Text>
                  <View className="flex-row items-center space-x-4">
                    <View className="flex-row items-center space-x-1">
                      <Image
                        source={require("../assets/images/star_fill.png")}
                        className="w-[20px] h-[20px]"
                        resizeMode="contain"
                      />
                      <Text className="text-[14px] mt-[2px] text-[#151B2D] font-semibold">
                        4.8
                      </Text>
                    </View>
                    <Text className="text-[#151B2D] text-[14px]">
                      1,234 trips
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => setShowPassenger(false)}
              >
                <Image
                  source={require("../assets/images/close_line.png")}
                  className="w-[24px] h-[24px]"
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>

          <View className="flex-row justify-between w-full px-4 mt-6">
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => router.push(`/${"chat"}`)}
              className="items-center justify-center gap-y-1"
            >
              <Image
                source={require("../assets/images/Chat.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">Chat</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => makePhoneCall(trip?.driver?.phoneNumber)}
              activeOpacity={0.8}
              className="items-center justify-center gap-y-1"
            >
              <Image
                source={require("../assets/images/Call.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />

              <Text className="text-[#151B2D] font-medium text-xs">Call</Text>
            </TouchableOpacity>

            <View className="items-center justify-center gap-y-1">
              <Image
                source={require("../assets/images/Call.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />

              <Text className="text-[#151B2D] font-medium text-xs">
                Internet Call
              </Text>
            </View>
          </View>

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View className="space-y-3">
            <View>
              <Text className="text-[#747272] text-xs">Vehicle</Text>
              <Text
                ellipsizeMode="middle"
                numberOfLines={1}
                className="text-[#151B2D] text-[15px]"
              >
                {trip.driver.carDetails.make} {trip?.driver?.carDetails.model},{" "}
                {trip?.driver?.carDetails?.colour}
              </Text>
            </View>
            <View>
              <Text className="text-[#747272] text-xs">Plate Number</Text>
              <Text
                ellipsizeMode="middle"
                numberOfLines={1}
                className="text-[#151B2D] text-[15px]"
              >
                {trip?.driver.carDetails.plateNumber}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </Modal>
  );
};

export default DriverInformation;
