import React, { useEffect } from 'react';
import { useNotificationStore } from '@/app/store/notificationStore';
import GlobalModal from './GlobalModal';
import useCurrentUser from '@/hooks/useCurrentUser';
import useActiveTrip from '@/hooks/useActiveTrip';

const GlobalModalManager: React.FC = () => {
  const { data: currentUser } = useCurrentUser();
  const { data: activeTrip } = useActiveTrip();
  
  const { 
    getModalsForUser, 
    setUserContext,
    currentUserRole,
    currentTripId,
    currentUserId 
  } = useNotificationStore();

  // Update user context when user or trip data changes
  useEffect(() => {
    console.log('GlobalModalManager - User context update:', {
      currentUser: currentUser?.id,
      activeTrip: activeTrip?.id,
      activeTripDriver: activeTrip?.driver?.id,
      activeTripDataDriver: activeTrip?.data?.driver?.id,
    });

    if (currentUser && activeTrip) {
      // Handle both data structures - activeTrip.driver and activeTrip.data.driver
      const driverId = activeTrip.driver?.id || activeTrip.data?.driver?.id;
      const tripId = activeTrip.id || activeTrip.data?.id;

      const isDriver = currentUser.id === driverId;
      const role = isDriver ? 'driver' : 'passenger';

      console.log('GlobalModalManager - Setting user context:', {
        role,
        tripId,
        userId: currentUser.id,
        isDriver,
        driverId
      });

      setUserContext(role, tripId, currentUser.id);
    } else if (currentUser) {
      console.log('GlobalModalManager - No active trip, clearing context');
      setUserContext(null, null, currentUser.id);
    }
  }, [currentUser, activeTrip, setUserContext]);

  const modalsToShow = getModalsForUser();

  console.log('GlobalModalManager - Modals to show:', {
    modalsCount: modalsToShow.length,
    modals: modalsToShow.map(m => ({
      id: m.id,
      type: m.type,
      title: m.title,
      targetRole: m.targetRole,
      tripId: m.tripId
    }))
  });

  if (modalsToShow.length === 0) {
    return null;
  }

  // Show only the first modal (you can modify this to show multiple if needed)
  return <GlobalModal modal={modalsToShow[0]} />;
};

export default GlobalModalManager;
