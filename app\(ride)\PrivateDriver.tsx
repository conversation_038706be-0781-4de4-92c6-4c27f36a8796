import { View, StyleSheet, StatusBar, Text, Image, Switch } from "react-native";
import React, { useState } from "react";
import { useUser } from "../../context/UserContext";

import { useLocalSearchParams, useNavigation } from "expo-router";

import { DrawerActions } from "@react-navigation/native";
import { useRide } from "@/context/RideProvider";
import AvailablePrivatePassengers from "@/components/AvailablePrivatePassengers";
import NoPassengerAvailable from "@/components/NoPassengerAvailable";
import DriverCancelRide from "@/components/DriverCancelRide";
import DriverPrivateTripStarted from "@/components/DriverPrivateTripStarted";
import PassengerInfo from "@/components/PassengerInfo";
import PrivateRide from "@/components/PrivateRide";
import DriverTripEnded from "@/components/DriverTripEnded";
import PrivateLookingForPassengers from "@/components/PrivateLookingForPassengers";
import { useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import { Trip, TripRequestsResponse } from "@/utils/types";

import DriverTripMap from "@/components/DriverTripMap";
import useActiveTrip from "@/hooks/useActiveTrip";
import PassengersDetails from "@/components/PassengersDetails";

const PrivateDriver = () => {
  //This is for everything related to directions
  const [direction, setDirection] = useState(false);
  const [directionOrigin, setDirectionOrigin] = useState({
    latitude: 0,
    longitude: 0,
  });
  const [directionDestination, setDirectionDestination] = useState({
    latitude: 0,
    longitude: 0,
  });

  const { initialRegion, updateRide } = useUser();
  const { isEnabled } = useRide();

  const [step, setStep] = useState(1);
  const [showPassengers, setShowPassengers] = useState(false);

  const [cancelRide, setCancelRide] = useState(false);
  const [showPassenger, setShowPassenger] = useState(false);
  const [noPassengerAvailable, setNoPassengerAvailable] = useState(false);

  const { trip: tripParams } = useLocalSearchParams();
  const trip: Trip = tripParams
    ? JSON.parse(decodeURIComponent(tripParams as string))
    : [];

  //Get Carpool requests
  const { data, refetch } = useQuery({
    queryKey: ["requests"],
    queryFn: () => services.getCarpoolRequests("private", trip?.id),
  });

  // Store carpool requests in a variable and add type
  const rideRequests: TripRequestsResponse = data;

  //Origin for direction is the users current location, destination would be where they're going to, also update rideId
  const setMarkerDirection = (
    latitude: number,
    longitude: number,
    id: string
  ) => {
    setDirectionOrigin({
      latitude: initialRegion.latitude,
      longitude: initialRegion.longitude,
    });
    setDirectionDestination({
      latitude: latitude,
      longitude: longitude,
    });
    setDirection(true);
    updateRide("rideId", id);
  };

  const { data: activeTrip, refetchActive } = useActiveTrip();

  const ActiveTrip = activeTrip.data;

  return (
    <View className="h-full w-full bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="transparent" />

      <View className="bg-white px-2 py-0.5 rounded-[100px] absolute top-14 right-1 flex-row space-x-1 items-center justify-between z-20">
        <Image
          source={require("../../assets/images/PrivateCar.png")}
          className="w-[18px] h-[18px]"
          resizeMode="contain"
        />
        <Text
          style={{
            color: "#151B2D",
            fontSize: 12,
            fontWeight: "500",
          }}
        >
          Private trip mode
        </Text>
        <Switch
          trackColor={{ false: "#767577", true: "#34C759" }}
          thumbColor={isEnabled ? "#FFFFFF" : "#f4f3f4"}
          ios_backgroundColor="#3e3e3e"
          //   onValueChange={toggleSwitch}
          value={isEnabled}
          style={{ transform: [{ scaleX: 0.6 }, { scaleY: 0.5 }] }}
        />
      </View>

      <DriverTripMap
        markers={rideRequests}
        direction={direction}
        directionOrigin={directionOrigin}
        directionDestination={directionDestination}
        setMarkerDirection={setMarkerDirection}
        activeTrip={ActiveTrip}
        step={step}
      />

      {/* <MapView
        region={initialRegion}
        userInterfaceStyle="light"
        showsUserLocation={true}
        style={styles.map}
        className="relative"
        initialRegion={initialRegion}
        provider={PROVIDER_DEFAULT}
      >
        <Circle
          center={initialRegion}
          radius={50}
          strokeWidth={1}
          strokeColor="#4639f533"
          fillColor="#4639f533"
        />
      </MapView> */}

      {step === 1 &&
        (!noPassengerAvailable ? (
          <PrivateLookingForPassengers
            step={step}
            setStep={setStep}
            rideRequests={rideRequests}
            noPassengerAvailable={noPassengerAvailable}
            setNoPassengerAvailable={setNoPassengerAvailable}
            refetch={refetch}
            refetchActive={refetchActive}
            tripId={trip.id}
            activeTrip={ActiveTrip || trip}
          />
        ) : (
          <NoPassengerAvailable
            step={step}
            setStep={setStep}
            noPassengerAvailable={noPassengerAvailable}
            setNoPassengerAvailable={setNoPassengerAvailable}
          />
        ))}

      {step === 2 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} tripId={trip.id} />
        ) : (
          <AvailablePrivatePassengers
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            rideRequests={rideRequests}
            trip={trip}
            refetch={refetch}
            refetchActive={refetchActive}
            activeTrip={ActiveTrip}
          />
        ))}

      {step === 3 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} tripId={trip.id} />
        ) : showPassengers ? (
          showPassenger ? (
            <PassengerInfo
              setShowPassenger={setShowPassenger}
              trip={ActiveTrip}
            />
          ) : (
            <PassengersDetails
              setShowPassengers={setShowPassengers}
              setShowPassenger={setShowPassenger}
              trip={ActiveTrip}
            />
          )
        ) : (
          <DriverPrivateTripStarted
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            setShowPassengers={setShowPassengers}
            trip={ActiveTrip}
          />
        ))}

      {step === 4 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} tripId={trip.id} />
        ) : (
          <PrivateRide
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            trip={ActiveTrip}
          />
        ))}

      {step === 5 && (
        <DriverTripEnded step={step} setStep={setStep} trip={ActiveTrip} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

export default PrivateDriver;
