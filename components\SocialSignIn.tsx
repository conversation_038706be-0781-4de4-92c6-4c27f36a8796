import { View, Text, TouchableOpacity, Image } from "react-native";
import React, { useEffect, useState } from "react";
import * as WebBrowser from "expo-web-browser";
import * as Google from "expo-auth-session/providers/google";
import * as Facebook from "expo-auth-session/providers/facebook";
import {
  IOS_CLIENT_ID,
  WEB_CLIENT_ID,
  FACEBOOK_APP_ID,
  ANDROID_CLIENT_ID,
} from "@/keys";
// devemmah.
const SocialSignIn = () => {
  const [request, response, promptAsync] = Google.useAuthRequest({
    androidClientId: ANDROID_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID,
    // webClientId: WEB_CLIENT_ID,
    // clientId: ANDROID_CLIENT_ID,
    scopes: ["profile", "email"],
  });

  const [facebookRequest, facebookResponse, promptFacebookAsync] =
    Facebook.useAuthRequest({
      clientId: FACEBOOK_APP_ID,
      scopes: ["public_profile", "email"],
    });

  useEffect(() => {
    handleSignInResponse();
  }, [response, facebookResponse]);

  const handleSignInResponse = async () => {
    if (response?.type === "success") {
      const { authentication } = response;
      await handleGoogleSignIn(authentication?.accessToken);
    } else if (facebookResponse?.type === "success") {
      const { authentication } = facebookResponse;
      await handleFacebookSignIn(authentication?.accessToken);
    }
  };

  const handleGoogleSignIn = async (accessToken: string | undefined) => {
    try {
      const backendResponse = await fetch(
        `${process.env.EXPO_PUBLIC_BASE_URL}/auth/signin-google`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            accessToken: accessToken,
          }),
        }
      );

      const data = await backendResponse.json();
      if (backendResponse.ok) {
        console.log("Google authentication successful", data);
      } else {
        console.error("Google backend authentication failed", data);
      }
    } catch (error) {
      console.error("Error during Google authentication:", error);
    }
  };

  const handleFacebookSignIn = async (accessToken: string | undefined) => {
    try {
      const backendResponse = await fetch(
        `${process.env.EXPO_PUBLIC_BASE_URL}/auth/signin-facebook`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            accessToken: accessToken,
          }),
        }
      );

      const data = await backendResponse.json();
      if (backendResponse.ok) {
        console.log("Facebook authentication successful", data);
      } else {
        console.error("Facebook backend authentication failed", data);
      }
    } catch (error) {
      console.error("Error during Facebook authentication:", error);
    }
  };

  return (
    <View className="gap-y-5">
      <TouchableOpacity
        onPress={() => {
          console.log("Attempting to sign in with Google...");
          promptAsync();
        }}
        className="relative border border-[#151B2D33] flex flex-row justify-center items-center py-[15px] rounded-[100px]"
      >
        <Image
          source={require("../assets/images/Google.png")}
          className="w-[16px] h-[16px] absolute left-3"
          resizeMode="contain"
        />
        <Text className="text-center text-[#151B2D] font-medium text-[15px]">
          Continue with Google
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => {
          console.log("Attempting to sign in with Facebook...");
          promptFacebookAsync();
        }}
        className="relative border border-[#151B2D33] flex flex-row justify-center items-center py-[15px] rounded-[100px]"
      >
        <Image
          source={require("../assets/images/Facebook.png")}
          className="w-[16px] h-[16px] absolute left-3"
          resizeMode="contain"
        />
        <Text className="text-center text-[#151B2D] font-medium text-[15px]">
          Continue with Facebook
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default SocialSignIn;
