import { View, Text } from "react-native";
import React, { useRef } from "react";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "./Modal";
import { router } from "expo-router";
import Button from "./Button";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  noPassengerAvailable: boolean;
  setNoPassengerAvailable: React.Dispatch<React.SetStateAction<boolean>>;
}

const NoPassengerAvailable: React.FC<Props> = ({
  step,
  setStep,
  noPassengerAvailable,
  setNoPassengerAvailable,
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["25%"];

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };
  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF] items-center justify-center">
        <View className="flex-1 w-[90%] mx-auto pt-5">
          <Text className="text-[#151B2D] font-semibold text-center text-[18px]">
            No passengers found
          </Text>

          <Text className="w-full text-[#4A4C50] text-[14px] mt-3 text-center">
            We could no find any passenger to join your ride at the moment
            please try again in some minutes
          </Text>
        </View>
        <View className="absolute bottom-3 left-0 right-0 px-5 bg-white pb-2">
          <Button
            text="Retry"
            buttonClassName="bg-[#EDEDED] w-full mb-2"
            textClassName="text-[#151B2D]"
            onClick={() => setNoPassengerAvailable(!noPassengerAvailable)}
          />
        </View>
      </View>
    </Modal>
  );
};

export default NoPassengerAvailable;
