import * as Notifications from 'expo-notifications';
import { pushNotificationHelpers } from './pushNotificationService';

interface TripReminderData {
  tripId: string;
  userRole: 'driver' | 'passenger';
  tripDetails: {
    origin: string;
    destination: string;
    time: string;
    driverName?: string;
    passengerCount?: number;
  };
  tripStartTime: Date;
}

class TripReminderScheduler {
  private scheduledNotifications: Map<string, string[]> = new Map();

  /**
   * Schedule all trip reminder notifications for a trip
   * @param tripData - Trip information including start time and user role
   */
  async scheduleAllReminders(tripData: TripReminderData): Promise<void> {
    try {
      console.log('📅 Scheduling trip reminders for trip:', tripData.tripId);
      
      const now = new Date();
      const tripStartTime = new Date(tripData.tripStartTime);
      
      // Calculate reminder times
      const reminder30Min = new Date(tripStartTime.getTime() - 30 * 60 * 1000); // 30 minutes before
      const reminder5Min = new Date(tripStartTime.getTime() - 5 * 60 * 1000);   // 5 minutes before
      const reminderNow = tripStartTime; // At trip start time
      
      const notificationIds: string[] = [];
      
      // Schedule 30-minute reminder if it's in the future
      if (reminder30Min > now) {
        const id30 = await this.scheduleNotification(
          reminder30Min,
          'trip_reminder_30min',
          tripData,
          this.get30MinReminderContent(tripData)
        );
        if (id30) notificationIds.push(id30);
        console.log('⏰ Scheduled 30-min reminder for:', reminder30Min.toLocaleString());
      }
      
      // Schedule 5-minute reminder if it's in the future
      if (reminder5Min > now) {
        const id5 = await this.scheduleNotification(
          reminder5Min,
          'trip_reminder_5min',
          tripData,
          this.get5MinReminderContent(tripData)
        );
        if (id5) notificationIds.push(id5);
        console.log('⏰ Scheduled 5-min reminder for:', reminder5Min.toLocaleString());
      }
      
      // Schedule "start now" reminder if it's in the future
      if (reminderNow > now) {
        const idNow = await this.scheduleNotification(
          reminderNow,
          'trip_start_now',
          tripData,
          this.getStartNowContent(tripData)
        );
        if (idNow) notificationIds.push(idNow);
        console.log('🚗 Scheduled start-now reminder for:', reminderNow.toLocaleString());
      }
      
      // Store notification IDs for potential cancellation
      this.scheduledNotifications.set(tripData.tripId, notificationIds);
      
      console.log(`✅ Scheduled ${notificationIds.length} reminders for trip ${tripData.tripId}`);
    } catch (error) {
      console.error('❌ Failed to schedule trip reminders:', error);
    }
  }

  /**
   * Cancel all scheduled reminders for a trip
   * @param tripId - The trip ID to cancel reminders for
   */
  async cancelTripReminders(tripId: string): Promise<void> {
    try {
      const notificationIds = this.scheduledNotifications.get(tripId);
      if (!notificationIds || notificationIds.length === 0) {
        console.log('📅 No reminders to cancel for trip:', tripId);
        return;
      }

      // Cancel all scheduled notifications for this trip
      await Promise.all(
        notificationIds.map(id => Notifications.cancelScheduledNotificationAsync(id))
      );
      
      // Remove from tracking
      this.scheduledNotifications.delete(tripId);
      
      console.log(`✅ Cancelled ${notificationIds.length} reminders for trip ${tripId}`);
    } catch (error) {
      console.error('❌ Failed to cancel trip reminders:', error);
    }
  }

  /**
   * Schedule a single notification
   */
  private async scheduleNotification(
    triggerTime: Date,
    type: string,
    tripData: TripReminderData,
    content: { title: string; body: string }
  ): Promise<string | null> {
    try {
      const identifier = `${type}_${tripData.tripId}_${triggerTime.getTime()}`;
      
      await Notifications.scheduleNotificationAsync({
        content: {
          title: content.title,
          body: content.body,
          data: {
            type,
            tripId: tripData.tripId,
            userRole: tripData.userRole,
            ...tripData.tripDetails,
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: triggerTime,
        },
        identifier,
      });
      
      return identifier;
    } catch (error) {
      console.error('❌ Failed to schedule notification:', error);
      return null;
    }
  }

  /**
   * Get content for 30-minute reminder
   */
  private get30MinReminderContent(tripData: TripReminderData): { title: string; body: string } {
    const isDriver = tripData.userRole === 'driver';
    return {
      title: isDriver ? '🚗 Trip Starting Soon' : '🚗 Your Ride is Coming',
      body: isDriver 
        ? `Your trip from ${tripData.tripDetails.origin} to ${tripData.tripDetails.destination} starts in 30 minutes at ${tripData.tripDetails.time}. Get ready!`
        : `Your ride from ${tripData.tripDetails.origin} to ${tripData.tripDetails.destination} starts in 30 minutes at ${tripData.tripDetails.time}. Be ready!`,
    };
  }

  /**
   * Get content for 5-minute reminder
   */
  private get5MinReminderContent(tripData: TripReminderData): { title: string; body: string } {
    const isDriver = tripData.userRole === 'driver';
    return {
      title: isDriver ? '🚗 Trip Starting Very Soon!' : '🚗 Your Ride is Almost Here!',
      body: isDriver 
        ? `Your trip starts in 5 minutes! Time to head to ${tripData.tripDetails.origin} and pick up your passengers.`
        : `Your ride starts in 5 minutes! Be ready at ${tripData.tripDetails.origin} for pickup.`,
    };
  }

  /**
   * Get content for "start now" reminder
   */
  private getStartNowContent(tripData: TripReminderData): { title: string; body: string } {
    const isDriver = tripData.userRole === 'driver';
    return {
      title: isDriver ? '🚗 Time to Start Your Trip!' : '🚗 Your Ride Should Start Now!',
      body: isDriver 
        ? `It's time to start your trip! Head to ${tripData.tripDetails.origin} to pick up your passengers.`
        : `Your ride should be starting now. Your driver should be arriving at ${tripData.tripDetails.origin}.`,
    };
  }

  /**
   * Get all scheduled reminders for debugging
   */
  getScheduledReminders(): Map<string, string[]> {
    return this.scheduledNotifications;
  }

  /**
   * Clear all scheduled reminders (useful for cleanup)
   */
  async clearAllReminders(): Promise<void> {
    try {
      const allIds = Array.from(this.scheduledNotifications.values()).flat();
      await Promise.all(
        allIds.map(id => Notifications.cancelScheduledNotificationAsync(id))
      );
      this.scheduledNotifications.clear();
      console.log(`✅ Cleared ${allIds.length} scheduled reminders`);
    } catch (error) {
      console.error('❌ Failed to clear all reminders:', error);
    }
  }
}

// Export singleton instance
export const tripReminderScheduler = new TripReminderScheduler();

// Helper functions for easy access
export const tripReminderHelpers = {
  scheduleReminders: (tripData: TripReminderData) => 
    tripReminderScheduler.scheduleAllReminders(tripData),
  cancelReminders: (tripId: string) => 
    tripReminderScheduler.cancelTripReminders(tripId),
  clearAll: () => 
    tripReminderScheduler.clearAllReminders(),
  getScheduled: () => 
    tripReminderScheduler.getScheduledReminders(),
};

export type { TripReminderData };
