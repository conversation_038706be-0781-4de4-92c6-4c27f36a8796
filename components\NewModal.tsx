import { Text, TouchableOpacity, View } from 'react-native';

interface CustomModalProps {
    isVisible: boolean,
    onClose: () => void,
    imageUrl: string,
    imageAlt: string,
    title: string,
    description: string,
    buttonText: string,
    onButtonPress: () => void,
    showCloseButton: boolean,
    modalWidth: string,
}

export default function NewModal({ 
  isVisible, 
  onClose, 
  imageUrl, 
  imageAlt = "Modal image",
  title, 
  description, 
  buttonText, 
  onButtonPress,
  showCloseButton = true,
  modalWidth = "w-80"
}: CustomModalProps) {
  if (!isVisible) return null;

  return (
    <View className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <View className={`bg-white rounded-2xl p-6 mx-4 ${modalWidth} shadow-xl relative animate-in slide-in-from-bottom-4 duration-300`}>
        <View className="flex flex-col items-center space-y-4">
          {imageUrl && (
            <img
              src={imageUrl}
              alt={imageAlt}
              className="w-24 h-24 rounded-full object-cover"
            />
          )}

          {title && (
            <Text className="text-xl font-bold text-gray-800 text-center">
              {title}
            </Text>
          )}
          {description && (
            <Text className="text-gray-600 text-center leading-5">
              {description}
            </Text>
          )}
          {buttonText && (
            <TouchableOpacity
              className="bg-blue-500 px-8 py-3 rounded-lg mt-4 w-full text-white font-semibold text-center text-lg hover:bg-blue-600 transition-colors"
              onPress={onButtonPress || onClose}
            >
              {buttonText}
            </TouchableOpacity>
          )}
        </View>
        {showCloseButton && (
          <TouchableOpacity
            className="absolute top-4 right-4 w-8 h-8 flex justify-center items-center text-gray-400 text-xl font-bold hover:text-gray-600 transition-colors"
            onPress={onClose}
          >
            <Text>x</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}