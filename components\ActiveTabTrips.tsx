import { View, Text, ActivityIndicator, Image, TouchableOpacity } from 'react-native'
import React, { useRef, useEffect, useState } from 'react'
import useActiveTrip from '@/hooks/useActiveTrip';
import { useRide } from '@/context/RideProvider';
import { router } from 'expo-router';
import { services } from '@/services';
import { useMutation } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';
import { format } from 'date-fns';

const ActiveTabTrips: React.FC = () => {
    // ===== HOOKS & STATE =====
    const { data: tripData, isLoading, refetchActive } = useActiveTrip();
    const { setIsEnabled } = useRide();
    const mountTimeRef = useRef<string>('');
    const [, forceUpdate] = useState({});

    // Time-based status state
    const [isTimeToStart, setIsTimeToStart] = useState<boolean>(false);

    // ===== EFFECTS =====
    useEffect(() => {
        if (!mountTimeRef.current) {
            const now = new Date();
            mountTimeRef.current = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true});
            forceUpdate({});
        }
    }, []);

    // ===== HELPER FUNCTIONS =====
    // Helper function to format trip time display
    const formatTripTime = (timestamp: string | undefined): string => {
        if (!timestamp) return 'N/A';

        if (isTimeToStart) return 'Now';

        return new Date(timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Helper function to get trip status
    const getTripStatus = () => ({
        text: isTimeToStart ? 'Ongoing' : 'Upcoming',
        color: isTimeToStart ? 'text-[#34A853]' : 'text-[#F59E0B]'
    });

    // Helper function to format location names
    const formatLocationName = (location: any, fallback: string): string => {
        return location?.name?.split(",")[0] || fallback;
    };

    // Helper function to format trip metadata
    const getTripMetadata = () => ({
        seats: trip?.noOfPassengers || '0',
        price: trip?.pricePerSeat || '0'
    });

    // Helper function to format today's date
    const formatTodaysDate = (): string => {
        return format(new Date(), 'EEE d, MMM');
    };

    // Time comparison logic (similar to HomeTripCard)
    useEffect(() => {
        if (!tripData?.timestamp) {
            setIsTimeToStart(false);
            return;
        }

        const updateTimeStatus = () => {
            const now = new Date();
            const tripTime = new Date(tripData.timestamp);
            const timeDiff = tripTime.getTime() - now.getTime();

            // If time difference is 0 or negative, it's time to start
            setIsTimeToStart(timeDiff <= 0);
        };

        // Update immediately
        updateTimeStatus();

        // Update every second
        const interval = setInterval(updateTimeStatus, 1000);

        return () => clearInterval(interval);
    }, [tripData?.timestamp]);

    // ===== MUTATIONS =====
    const { mutate: handleCancelTrip, isPending: isCancelingTrip } = useMutation({
        mutationFn: services.cancelTrip,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message || "Trip cancelled successfully",
            });
            
            setIsEnabled(false);
            
            refetchActive();
        },
        onError: (error: any) => {
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    if (isLoading) {
        return (
            <View className="flex-1 justify-center items-center">
                <ActivityIndicator size="small" color="#473BF0" />
                <Text className="mt-2 text-gray-500">Loading active trips...</Text>
            </View>
        );
    }

    // Check if we have a valid trip with required data
         if (!tripData || !tripData.origin || !tripData.destination) {
                return (
                    <View className="flex-1 items-center justify-center rounded-lg p-6 bg-white">
                                    <Text className="text-lg font-medium mb-2">No active trips</Text>
                                    <Text className="text-gray-500 mb-4">
                                      Post or request a ride to get started
                                    </Text>
                                    <Image
                                      source={require('../assets/images/homme.png')}
                                      className="w-24 h-24"
                                    />
                                  </View>
                );
            }


    // ===== RENDER =====
    const trip = tripData;
    return (
        <View>
            <Text className="text-[13px] font-medium text-[#5B5B5B] mb-3">
                {formatTodaysDate()}
            </Text>

            <TouchableOpacity onPress={() => {
            router.push({
                pathname: "/TripSummary",
                params: {
                    trip: encodeURIComponent(JSON.stringify(trip)),
                },
            });
        }} className="flex flex-col bg-[#FFF] rounded-md p-4 mb-4 max-w-full">
            <View className="flex-row items-center justify-between mb-2">
                <Text className={`text-xs font-semibold ${getTripStatus().color}`}>
                    {getTripStatus().text}
                </Text>
                
            </View>

            <View className='flex-row items-center justify-between mb-2'>
                <View className='flex-1 mr-3'>
                    <View className='flex-row items-center'>
                        <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-[16px] font-semibold min-w-0">
                            {formatLocationName(trip.origin, 'Unknown origin')} to {formatLocationName(trip.destination, 'Unknown destination')}
                        </Text>
                    </View>
                </View>
                <Text className="text-[#787A80] text-[13px] font-semibold flex-shrink-0">
                    {formatTripTime(trip.timestamp)}
                </Text>
            </View>

            <View className='flex-row items-center justify-between'>
                <View className="flex-row items-center flex-1">
                    <View className='flex-row items-center'>
                        <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4 mr-1' />
                        <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
                    </View>
                    <View className='h-1 w-1 bg-[#787A80] rounded-full mx-2' />
                    <Text className="text-sm font-medium text-[#787A80]">
                        {getTripMetadata().seats} seats
                    </Text>
                    <View className='h-1 w-1 bg-[#787A80] rounded-full mx-2' />
                    <Text className="text-sm font-medium text-[#787A80]">
                        ₦{getTripMetadata().price}
                    </Text>
                </View>
                <Image source={require("../assets/images/goNew.png")} className='w-4 h-4' />
            </View>

            
            
            
        </TouchableOpacity>
        </View>
        
    );
};

export default ActiveTabTrips;