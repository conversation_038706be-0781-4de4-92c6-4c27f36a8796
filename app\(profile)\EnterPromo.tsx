import React, { useState } from 'react';
import { View, Text, Switch, TouchableOpacity } from 'react-native';
import { styled } from "nativewind";
import useCurrentUser from "@/hooks/useCurrentUser";
import GoBack from '@/components/GoBack';
import { router, Href } from 'expo-router';
import { TextInput } from 'react-native-gesture-handler';

const StyledView = styled(View);
const StyledText = styled(Text);

const EnterPromo = () => {
    const [promoCode, setPromoCode] = useState('');
    const isButtonDisabled = promoCode.trim() === '';


    return (
        <StyledView className="flex-1 p-5 bg-[#F4F4F4]">


            <StyledText className="text-lg font-semibold">Promo Code</StyledText>


            <View className="flex-1 justify-end">


                <TextInput
                    placeholder="CO-XXXX-1234"
                    className="border border-gray-300 p-3 mb-2 rounded mt-2 bg-[#F4F4F4]"
                    value={promoCode}
                    onChangeText={setPromoCode}
                />
                <Text className='text-lg'>This code will apply to your next ride</Text>

                <View className="flex-1 justify-end">
                    <TouchableOpacity
                        className={`p-4 rounded-full items-center ${isButtonDisabled ? 'bg-gray-400' : 'bg-blue-500'}`}
                        onPress={() => router.push("/(profile)/EditProfileScreen" as Href)}
                        disabled={isButtonDisabled}
                    >
                        <Text className="text-white font-bold">Apply</Text>
                    </TouchableOpacity>
                </View>
            </View>

        </StyledView>
    );
};

export default EnterPromo;
