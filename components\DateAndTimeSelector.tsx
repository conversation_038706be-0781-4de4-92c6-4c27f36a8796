import { View, Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import DateTimePickerModal from "react-native-modal-datetime-picker";

interface Props {
  showDatePicker: () => void;
  showTimePicker: () => void;
  date: Date | undefined;
  isDatePickerVisible: boolean;
  isTimePickerVisible: boolean;
  handleTimeConfirm: (selectedTime: Date) => void;
  handleDateConfirm: (selectedDate: Date) => void;
  hideDatePicker: () => void;
  hideTimePicker: () => void;
  dateSelected: boolean;
  timeSelected: boolean;
  showTitle?: boolean;
  enableTimePicker?: boolean;
  textToBeShown: string;
}

const DateTimeSelector = ({
  showDatePicker,
  showTimePicker,
  date,
  isDatePickerVisible,
  isTimePickerVisible,
  handleTimeConfirm,
  handleDateConfirm,
  hideDatePicker,
  hideTimePicker,
  dateSelected,
  timeSelected,
  showTitle = true,
  enableTimePicker = true,
  textToBeShown,
}: Props) => (
  <View className="mt-4">
    {showTitle && (
      <Text className="text-[#151B2D] text-[15px] font-medium mb-2">
        Select Date and Time
      </Text>
    )}
    <View className="flex-row justify-between w-full">
      {/* Date Picker */}
      <View className={`p-3 bg-[#F6F6F6] rounded-md h-[48px] justify-center ${enableTimePicker ? 'flex-1 mr-4' : 'w-full'}`}>
        <TouchableOpacity
          onPress={showDatePicker}
          className="flex-row justify-between items-center"
        >
          <Text>
            {dateSelected ? date?.toLocaleDateString() : textToBeShown}
          </Text>
          <Image
            source={require("../assets/images/Date.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>

      {/* Time Picker */}
      {enableTimePicker && (
        <View className="flex-1 p-3 bg-[#F6F6F6] rounded-md ml-4 h-[48px] justify-center">
          <TouchableOpacity
            onPress={showTimePicker}
            className="flex-row justify-between items-center"
          >
            <Text>
              {timeSelected ? date?.toLocaleTimeString() : "Select Time"}
            </Text>
            <Image
              source={require("../assets/images/Time.png")}
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      )}
    </View>

    {/* Date Picker Modal */}
    <DateTimePickerModal
      isVisible={isDatePickerVisible}
      mode="date"
      onConfirm={handleDateConfirm}
      onCancel={hideDatePicker}
      date={date}
    />

    {/* Time Picker Modal */}
    {enableTimePicker && (
      <DateTimePickerModal
        isVisible={isTimePickerVisible}
        mode="time"
        onConfirm={handleTimeConfirm}
        onCancel={hideTimePicker}
        date={date}
      />
    )}
  </View>
);

export default DateTimeSelector;
