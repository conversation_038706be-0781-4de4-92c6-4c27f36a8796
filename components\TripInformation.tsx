import { View, Text, Image } from "react-native";
import React, { Dispatch, SetStateAction } from "react";
import Button from "./Button";
import { Trip } from "@/utils/types";
import { format } from "date-fns";

interface Props {
  trip: Trip;
  setCancelRide?: Dispatch<SetStateAction<boolean>>;
}

const TripInformation = ({ trip, setCancelRide }: Props) => {
  return (
    <View className="mt-2 mb-2">
      <Text className="text-[#151B2D] font-semibold text-[20px] mb-2">
        Trip Information
      </Text>
      <View className="bg-[#FFFFFF] p-4 rounded-[6px]">
        <View className="flex-row w-full justify-between">
          <Text className="text-[#151B2D] text-[17px] font-semibold">
            My route
          </Text>
          <Text className="text-[#473BF0] text-[14px] font-medium">Edit</Text>
        </View>

        <View className="w-full flex-row items-center mt-3">
          <View className="items-center">
            <Image
              source={require("../assets/images/from2.png")}
              className="w-[15px] h-[15px]"
              resizeMode="contain"
            />
            <View className="h-[12px] w-[1px] bg-[#D9D9D9]" />
            <Image
              source={require("../assets/images/to2.png")}
              className="w-[15px] h-[15px]"
              resizeMode="contain"
            />
          </View>
          <View className="flex-1 gap-y-[12px] ml-3">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-[12px] text-[#151B2D] font-semibold"
            >
              {trip?.origin?.name}
            </Text>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-[12px] text-[#151B2D] font-semibold"
            >
              {trip?.destination?.name}
            </Text>
          </View>
        </View>

        <View className="w-full h-[1px] bg-[#E3E3E3] my-3"></View>

        <View>
          <View className="flex-row w-full justify-between mb-2">
            <Text className="text-[#151B2D] text-[17px] font-semibold">
              Ride details
            </Text>
            <Text className="text-[#473BF0] text-[14px] font-medium">Edit</Text>
          </View>

          <View className="flex-row flex-wrap justify-between gap-y-2 flex-1">
            <View className="gap-y-1">
              <Text className="text-[12px] text-[#787A80] font-medium">
                Date and time:
              </Text>
              <Text className="text-[12px] text-[#151B2D] font-medium">
                {format(trip?.timestamp, "MMMM dd, yyyy hh:mm a")}
              </Text>
            </View>
            <View className="gap-y-1">
              <Text className="text-[12px] text-[#787A80] font-medium">
                No. of passengers
              </Text>
              <Text className="text-[12px] text-[#151B2D] font-medium">
                {trip?.passengers?.length}/{trip?.noOfPassengers}
              </Text>
            </View>
            <View className="gap-y-1">
              <Text className="text-[12px] text-[#787A80] font-medium">
                Price per seat
              </Text>
              <Text className="text-[12px] text-[#151B2D] font-medium">
                {trip?.pricePerSeat}
              </Text>
            </View>
            <View className="gap-y-1">
              <Text className="text-[12px] text-[#787A80] font-medium">
                No. of passengers
              </Text>
              <Text className="text-[12px] text-[#151B2D] font-medium">
                {trip?.passengers?.length}/{trip?.noOfPassengers}
              </Text>
            </View>
          </View>

          <View className="mt-2">
            <Text className="text-[#787A80] text-[12px] font-medium">
              Preferences
            </Text>
            <View className="flex-row flex-wrap justify-between">
              {trip.preferences.map((preference) => (
                <View
                  // key={preference.value}
                  className="py-1 justify-between mb-2 flex-row items-center gap-x-[4px]"
                >
                  {preference?.value === true ? (
                    <Image
                      source={require("../assets/images/accepted.png")}
                      className="h-[12px] w-[12px]"
                      resizeMode="contain"
                    />
                  ) : (
                    <Image
                      source={require("../assets/images/cancel.png")}
                      className="h-[12px] w-[12px]"
                      resizeMode="contain"
                    />
                  )}

                  <Text className="text-[#4A4C50] text-[11px] font-medium">
                    {preference.desc}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </View>

      {setCancelRide && (
        <View className="my-4">
          <Button
            text="Cancel trip"
            textClassName="text-[#E05859]"
            buttonClassName="bg-[#E0585926] w-full mb-2"
            onClick={() => setCancelRide(true)}
          />
        </View>
      )}
    </View>
  );
};

export default TripInformation;
