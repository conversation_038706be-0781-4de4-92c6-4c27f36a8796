import React, { useEffect, useRef } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { router } from "expo-router";
import BottomSheet, { BottomSheetFlatList } from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";

import { PrivateRide, Trip } from "@/utils/types";
import { useUser } from "@/context/UserContext";
import useCurrentUser from "@/hooks/useCurrentUser";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  availableCoride: PrivateRide;
  setMarkerDirection: (
    latitude: number,
    longitude: number,
    id: string,
    price: number
  ) => void;
  trip: Trip;
}

const AvailablePrivateDrivers: React.FC<Props> = ({
  step,
  setStep,
  availableCoride,
  setMarkerDirection,
  trip,
}) => {
  const { ride: selectedRide, initialRegion } = useUser();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const customSnapPoints = ["45%", "60%"];

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const { data: user } = useCurrentUser();

  useEffect(() => {
    const passengerExists = trip?.passengers?.some(
      (passenger) => passenger?.id === user?.id
    );

    if (trip?.status === "pending" && passengerExists) {
      setStep(4);
    } else if (trip?.status === "pending" && !passengerExists) {
      setStep(3);
    } else if (trip?.status === "ongoing") {
      setStep(5);
    }
  }, [trip, user]);

  // Check if availableCoride and its drivers exist
  const drivers = availableCoride?.drivers || [];

  return (
    <Modal
      handleGoBack={handleGoBack}
      goBack
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative">
        <View className="flex-1 w-[90%] mx-auto">
          <View className="gap-y-1 py-2">
            <Text className="text-[#151B2D] font-semibold text-xl">
              Available private drivers
            </Text>
          </View>
          <BottomSheetFlatList
            data={drivers}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{
              paddingBottom: 100,
            }}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View className="flex-1 items-center justify-center h-[20vh]">
                <Text>No Rides available at the moment</Text>
              </View>
            }
            renderItem={(item) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setMarkerDirection(
                      1111,
                      1111,
                      item.item.id,
                      availableCoride.estimatedPrice
                    );
                  }}
                  activeOpacity={0.9}
                  className={`p-4 mb-3 rounded-[6px] border shadow-sm ${
                    selectedRide.driverId === item.item.id
                      ? "bg-[#473BF014] border-[#473BF0]"
                      : "bg-white border-[#FFFFFF]"
                  }`}
                >
                  <View>
                    <View className="flex-row justify-between items-center">
                      <View className="flex-row items-center">
                        <Image
                          source={require("../assets/images/ProfilePic.png")}
                          className="h-[50px] w-[50px] rounded-full mr-3"
                          resizeMode="cover"
                        />
                        <View>
                          <Text className="text-[#151B2D] font-semibold text-[16px]">
                            {`${item?.item?.firstName} ${item?.item?.lastName}`}
                          </Text>
                          <Text className="text-[#787A80] text-[13px]">
                            {`${item?.item?.carDetails?.make} ${item?.item?.carDetails?.model}, ${item?.item?.carDetails?.colour}`}
                          </Text>
                        </View>
                      </View>
                      <View className="items-end">
                        <Text className="text-[#473BF0] font-semibold text-[18px]">
                          {availableCoride?.estimatedPrice}
                        </Text>
                        <Text className="text-xs text-[#4A4C50]">Per seat</Text>
                      </View>
                    </View>
                    <View className="mt-3 flex-row justify-between items-center">
                      <View>
                        {/* <Text className="text-[#151B2D] text-[13px] font-semibold">
                        Ride starts: {extractTimeFromISO(item.item.timestamp)}
                      </Text> */}
                        {/* <Text className="text-[#151B2D] text-[13px]">
                        {haversineDistance(
                          {
                            latitude: initialRegion.latitude,
                            longitude: initialRegion.longitude,
                          },
                          {
                            latitude: item.item.origin.lat,
                            longitude: item.item.origin.lng,
                          }
                        ).toFixed(2)}{" "}
                        m away
                      </Text> */}
                      </View>
                      <View className="flex-row items-center">
                        <Image
                          source={require("../assets/images/group.png")}
                          className={`w-[18px] h-[18px] mr-1`}
                          resizeMode="contain"
                        />
                        <Text className="text-xs text-[#4A4C50]">
                          {item?.item?.carDetails?.passengerCapacity}
                        </Text>
                      </View>
                    </View>
                    {/* <View className="my-3 w-full h-[1px] bg-[#afafaf]"></View>
                  {selectedRide.rideId === item.item.id && (
                    <>
                      <View className="flex-row flex-wrap">
                        {item.item.preferences.map((preference, index) => (
                          <View
                            key={index}
                            className="px-2 py-1 mr-2 mb-2 flex-row items-center gap-x-[4px]"
                          >
                            {preference.value === true ? (
                              <Image
                                source={require("../assets/images/accepted.png")}
                                className="h-[12px] w-[12px]"
                                resizeMode="contain"
                              />
                            ) : (
                              <Image
                                source={require("../assets/images/cancel.png")}
                                className="h-[12px] w-[12px]"
                                resizeMode="contain"
                              />
                            )}

                            <Text className="text-[#4A4C50] text-[12px] font-medium">
                              {preference.desc}
                            </Text>
                          </View>
                        ))}
                      </View>
                      <View className="my-3 w-full h-[1px] bg-[#afafaf]"></View>
                    </>
                  )}

                  <View className="flex-row items-center gap-x-1">
                    <Image
                      source={require("../assets/images/arrow.png")}
                      className={`w-[16px] h-[16px] transition-all duration-300 ${
                        selectedRide.rideId === item.item.id
                          ? "rotate-180"
                          : "rotate-0"
                      }`}
                      resizeMode="contain"
                    />
                    <Text className="text-xs text-[#787A80] font-medium">
                      View details
                    </Text>
                  </View> */}
                  </View>
                </TouchableOpacity>
              );
            }}
          />
        </View>
        <View className="absolute bottom-0 left-0 right-0 px-5 pb-5 bg-white">
          <Button
            buttonDisabled={!selectedRide.driverId}
            text="Continue"
            textClassName="text-white"
            buttonClassName="bg-[#473BF0] w-full mb-2"
            onClick={() => setStep(step + 1)}
          />
        </View>
      </View>
    </Modal>
  );
};

export default AvailablePrivateDrivers;
