import { View, Text, Image, Switch } from "react-native";
import React from "react";
import { Drawer } from "expo-router/drawer";
import { DrawerContentScrollView, DrawerItem } from "@react-navigation/drawer";

import { Href, router, Stack } from "expo-router";
import { useRide } from "@/context/RideProvider";
import useCurrentUser from "@/hooks/useCurrentUser";

const CustomDrawerContent = (props: any) => {
  const { data: user } = useCurrentUser();

  const { isEnabled, toggleSwitch, deactivatePrivateRidePending, isPending } =
    useRide();

  return (
    <DrawerContentScrollView
      style={{ backgroundColor: "white", paddingTop: 20 }}
      {...props}
    >
      <View className="flex-row items-center w-[85%] mx-auto">
        <Image
          source={require("../../assets/images/Profile.png")}
          className="w-[44px] h-[44px]"
          resizeMode="contain"
        />
        <View className="ml-2 justify-center">
          <Text className="text-[#151B2D] text-[16px] font-semibold">
            {user ? user.firstName : "<PERSON>"} {user ? user.lastName : "Doe"}
          </Text>
          <View
            className={`flex-row self-start items-center justify-center rounded-full px-[4px] py-[2px] mt-[2px] ${
              user?.isVerified ? "bg-[#34A8531A]" : "bg-[#EBEBEB]"
            }`}
          >
            <Text
              className={`text-[10px] font-semibold mr-1 ${
                user?.isVerified ? "text-[#34A853]" : "text-[#8F8F8F]"
              }`}
            >
              {user?.isVerified ? "Verified" : "Not verified"}
            </Text>
            <Image
              source={
                user?.isVerified
                  ? require("../../assets/images/check_circle.png")
                  : require("../../assets/images/close_circle_fill.png")
              }
              className="w-[10px] h-[10px]"
              resizeMode="contain"
            />
          </View>
        </View>
      </View>

      <View className="w-[90%] mx-auto my-5 h-[1px] bg-[#D9D9D9]"></View>

      {user?.isVerified && (
        <DrawerItem
          style={{ backgroundColor: "#F4F4F4" }}
          icon={({ size }) => (
            <Image
              source={require("../../assets/images/PrivateCar.png")}
              className="w-[24px] h-[24px]"
              resizeMode="contain"
            />
          )}
          label={() => (
            <View className="flex-row items-center justify-between h-8">
              <Text
                style={{
                  color: "#151B2D",
                  fontSize: 13,
                  fontWeight: "500",
                }}
              >
                Private trip mode
              </Text>
              <Switch
                trackColor={{ false: "#767577", true: "#34C759" }}
                thumbColor={isEnabled ? "#FFFFFF" : "#f4f3f4"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={toggleSwitch}
                value={isEnabled}
                disabled={deactivatePrivateRidePending || isPending}
                style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.7 }] }}
              />
            </View>
          )}
          labelStyle={{
            color: "#151B2D",
            fontSize: 13,
            fontWeight: "500",
          }}
          onPress={() => {
            toggleSwitch();
            // router.push("/Wallet" as Href);
          }}
        />
      )}

      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/wallet_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"Wallet"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {
          router.push("/Wallet" as Href);
        }}
      />
      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/sale_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"Promo codes"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {
          router.push("/Promo" as Href);
        }}
      />
      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/calendar_time_add_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"My trips"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {
          router.push("/(drawer)/(tabs)/Trips");
        }}
      />
      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/headphone_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"Support"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {}}
      />

      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/question_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"FAQs"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {}}
      />

      <DrawerItem
        icon={({ size }) => (
          <Image
            source={require("../../assets/images/information_line.png")}
            className="w-[24px] h-[24px]"
            resizeMode="contain"
          />
        )}
        label={"About us"}
        labelStyle={{
          color: "#151B2D",
          fontSize: 13,
          fontWeight: "500",
        }}
        onPress={() => {}}
      />
    </DrawerContentScrollView>
  );
};

export default function Layout() {
  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
    </Drawer>
  );
}
