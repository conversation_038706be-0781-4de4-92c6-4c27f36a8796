import useCurrentUser from "@/hooks/useCurrentUser";
import { services } from "@/services";
import { useMutation } from "@tanstack/react-query";
import {
  createContext,
  Dispatch,
  ReactElement,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";
import Toast from "react-native-toast-message";

type CarpoolType = "one-time" | "recurring";

export type Location = {
  name: string;
  lat: number;
  lng: number;
};

interface CarpoolRide {
  type: CarpoolType;
  origin: Location;
  destination: Location;
  stops: Location[];
  noOfPassengers: number;
  pricePerSeat: string;
  timestamp: string;
  selectedRide: string;
  selectedPassenger: string;
  driverNote: string;
}

interface PrivateRide {
  origin: Location;
  destination: Location;
  stops: Location[];
  rideId: string;
  driverId: string;
}

export type Preferences = {
  desc: string;
  value: boolean | null;
}[];

type rideContextType = {
  ride: CarpoolRide;
  setRide: Dispatch<SetStateAction<CarpoolRide>>;
  setDetails: (value: string, key: string) => void;
  isEnabled: boolean;
  setIsEnabled: Dispatch<SetStateAction<boolean>>;
  toggleSwitch: () => void;
  preferences: Preferences;
  updateLocation: (key: "origin" | "destination", location: Location) => void;
  addStop: (stop: Location) => void;
  removeStop: (index: number) => void;
  updatePreference: (desc: string, newValue: boolean) => void;
  updateRide: (key: keyof CarpoolRide, value: any) => void;
  updateStop: (index: number, updatedStop: Location) => void;
  privateRide: PrivateRide;
  addPrivateRideStop: (stop: Location) => void;
  removePrivateRideStop: (index: number) => void;
  updatePrivateRideStop: (index: number, updatedStop: Location) => void;
  updatePrivateRideLocation: (
    key: "origin" | "destination",
    location: Location
  ) => void;
  deactivatePrivateRidePending: boolean;
  isPending: boolean;
  deactivatePrivateRide: () => void;
  resetRideState: () => void;
};

const RideContext = createContext<rideContextType | undefined>(undefined);

function useRide(): rideContextType {
  const context = useContext(RideContext);
  if (!context) {
    throw new Error("useRide must be used within an RideProvider");
  }
  return context;
}

const RideProvider = (props: { children: ReactNode }): ReactElement => {
  const [ride, setRide] = useState<CarpoolRide>({
    type: "one-time",
    origin: { name: "", lat: 0, lng: 0 },
    destination: { name: "", lat: 0, lng: 0 },
    stops: [],
    noOfPassengers: 1,
    pricePerSeat: String(0),
    timestamp: "",
    selectedRide: "",
    selectedPassenger: "",
    driverNote: "Please keep to time",
  });

  const [privateRide, setPrivateRide] = useState<PrivateRide>({
    origin: { name: "", lat: 0, lng: 0 },
    destination: { name: "", lat: 0, lng: 0 },
    stops: [],
    rideId: "",
    driverId: "",
  });

  const addPrivateRideStop = (stop: Location) => {
    setPrivateRide((prev) => ({ ...prev, stops: [...prev.stops, stop] }));
  };

  // Remove a stop by index
  const removePrivateRideStop = (index: number) => {
    setPrivateRide((prev) => ({
      ...prev,
      stops: prev.stops.filter((_, i) => i !== index),
    }));
  };

  const updatePrivateRideStop = (index: number, updatedStop: Location) => {
    setPrivateRide((prev) => ({
      ...prev,
      stops: prev.stops.map((stop, i) => (i === index ? updatedStop : stop)),
    }));
  };

  const updatePrivateRideLocation = (
    key: "origin" | "destination",
    location: Location
  ) => {
    setPrivateRide((prev) => ({ ...prev, [key]: location }));
  };

  const addStop = (stop: Location) => {
    setRide((prev) => ({ ...prev, stops: [...prev.stops, stop] }));
  };

  // Remove a stop by index
  const removeStop = (index: number) => {
    setRide((prev) => ({
      ...prev,
      stops: prev.stops.filter((_, i) => i !== index),
    }));
  };

  const updateStop = (index: number, updatedStop: Location) => {
    setRide((prev) => ({
      ...prev,
      stops: prev.stops.map((stop, i) => (i === index ? updatedStop : stop)),
    }));
  };

  const [preferences, setPreferences] = useState<Preferences>([
    {
      desc: "Allow luggage",
      value: null,
    },
    {
      desc: "Allow Smoking/Drinking",
      value: null,
    },
    {
      desc: "Allow Pets",
      value: null,
    },
    {
      desc: "Allow Bikes",
      value: null,
    },
  ]);

  // console.log(preferences);

  const updatePreference = (desc: string, newValue: boolean) => {
    setPreferences((prevPreferences) =>
      prevPreferences.map((preference) =>
        preference.desc === desc
          ? { ...preference, value: newValue }
          : preference
      )
    );
  };

  const updateRide = (key: keyof CarpoolRide, value: any) => {
    setRide((prev) => ({ ...prev, [key]: value }));
  };

  const updateLocation = (
    key: "origin" | "destination",
    location: Location
  ) => {
    setRide((prev) => ({ ...prev, [key]: location }));
  };

  const setDetails = (value: string, key: string) => {
    setRide((prev) => {
      if (key === "origin" || key === "destination") {
        return {
          ...prev,
          [key]: { ...prev[key], name: value },
        };
      }
      return { ...prev, [key]: value };
    });
  };

  const { data } = useCurrentUser();

  useEffect(() => {
    setIsEnabled(data?.privateRideActive);
  }, [data]);

  const [isEnabled, setIsEnabled] = useState(data?.privateRideActive);

  const { mutate: activePrivateRide, isPending } = useMutation({
    mutationFn: services.activatePrivateRide,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message,
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const {
    mutate: deactivatePrivateRide,
    isPending: deactivatePrivateRidePending,
  } = useMutation({
    mutationFn: services.deactivatePrivateRide,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message,
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const toggleSwitch = async () => {
    try {
      if (!isEnabled) {
        // If switch is off and we're turning it on, activate private ride
        await new Promise((resolve, reject) => {
          activePrivateRide(undefined, {
            onSuccess: (data) => {
              setIsEnabled(true);
              resolve(data);
            },
            onError: (error) => {
              reject(error);
            },
          });
        });
      } else {
        // If switch is on and we're turning it off, deactivate private ride
        await new Promise((resolve, reject) => {
          deactivatePrivateRide(undefined, {
            onSuccess: (data) => {
              setIsEnabled(false);
              resolve(data);
            },
            onError: (error) => {
              reject(error);
            },
          });
        });
      }
    } catch (error) {
      // throw new Error(error);
    }
  };

  const resetRideState = () => {
    // Reset ride data to initial state
    setRide({
      type: "one-time",
      origin: { name: "", lat: 0, lng: 0 },
      destination: { name: "", lat: 0, lng: 0 },
      stops: [],
      noOfPassengers: 1,
      pricePerSeat: String(0),
      timestamp: "",
      selectedRide: "",
      selectedPassenger: "",
      driverNote: "Please keep to time",
    });

    // Reset preferences to initial state
    setPreferences([
      {
        desc: "Allow luggage",
        value: null,
      },
      {
        desc: "Allow Smoking/Drinking",
        value: null,
      },
      {
        desc: "Allow Pets",
        value: null,
      },
      {
        desc: "Allow Bikes",
        value: null,
      },
    ]);

    // Reset private ride data
    setPrivateRide({
      origin: { name: "", lat: 0, lng: 0 },
      destination: { name: "", lat: 0, lng: 0 },
      stops: [],
      rideId: "",
      driverId: "",
    });
  };

  return (
    <RideContext.Provider
      value={{
        ride,
        setRide,
        setDetails,
        preferences,
        updateLocation,
        addStop,
        updatePreference,
        updateRide,
        isEnabled,
        setIsEnabled,
        toggleSwitch,
        removeStop,
        updateStop,
        addPrivateRideStop,
        removePrivateRideStop,
        updatePrivateRideStop,
        updatePrivateRideLocation,
        privateRide,
        isPending,
        deactivatePrivateRidePending,
        deactivatePrivateRide,
        resetRideState,
      }}
    >
      {props.children}
    </RideContext.Provider>
  );
};

export { RideProvider, useRide };
