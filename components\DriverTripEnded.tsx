import React, { useEffect, useRef, useState } from "react";
import { View, Text, Image, TouchableOpacity, Platform } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { extras } from "@/utils/data";
import { router } from "expo-router";
import { Trip } from "@/utils/types";
import { format } from "date-fns";
import { getTravelTimeAndDistance } from "@/utils/helpers";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  trip: Trip;
  preferences?: boolean;
}

const DriverTripEnded: React.FC<Props> = ({
  step,
  setStep,
  trip,
  preferences,
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);

  const iosCustomSnapPoints = ["40%", "58%"];
  const androidCustomSnapPoints = ["40%", "62%"];

  const { travelData } = useTravelTimeAndDistance(
    trip?.origin?.name,
    trip?.destination?.name
  );

  return (
    <Modal
      customSnapPoints={
        Platform.OS === "ios" ? iosCustomSnapPoints : androidCustomSnapPoints
      }
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => router.replace("/(tabs)/Home")}
          className="absolute top-2 right-4 z-30"
        >
          <Image
            source={require("../assets/images/close_line.png")}
            className="w-[30px] h-[30px]"
          />
        </TouchableOpacity>
        <View className="flex-1 w-[90%] mx-auto">
          <View className="items-center mt-8 mb-4">
            <Image
              source={require("../assets/images/TripEnded.png")}
              className="w-[44px] h-[44px]"
              resizeMode="contain"
            />
            <Text className="text-[#151B2D] text-[20px] font-semibold my-2">
              Trip ended
            </Text>
            <Text className="text-[#151B2D]">
              <Text className="text-[#787A80]">{travelData?.distance}</Text>
            </Text>
          </View>

          <View className="mb-2 flex-1">
            <View className="bg-[#FFFFFF] p-4 rounded-[6px]">
              <View className="flex-row w-full justify-between">
                <Text className="text-[#151B2D] text-[17px] font-semibold">
                  My route
                </Text>
              </View>

              <View className="w-full flex-row items-center mt-3">
                <View className="items-center">
                  <Image
                    source={require("../assets/images/from2.png")}
                    className="w-[12px] h-[12px]"
                    resizeMode="contain"
                  />
                  <View className="h-[25px] w-[1px] bg-[#D9D9D9]" />
                  <Image
                    source={require("../assets/images/to2.png")}
                    className="w-[13px] h-[13px]"
                    resizeMode="contain"
                  />
                </View>
                <View className="flex-1 gap-y-[18px] ml-3">
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.origin?.name}
                  </Text>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.destination?.name}
                  </Text>
                </View>
              </View>

              <View className="w-full h-[1px] bg-[#E3E3E3] my-3"></View>

              <View className="">
                <View className="flex-row w-full justify-between mb-2">
                  <Text className="text-[#151B2D] text-[17px] font-semibold">
                    Ride details
                  </Text>
                </View>

                <View className="flex-row flex-wrap justify-between gap-y-2">
                  {preferences && (
                    <View className="gap-y-1">
                      <Text className="text-[14px] text-[#787A80] font-medium">
                        Date and time:
                      </Text>
                      <Text className="text-[14px] text-[#151B2D] font-medium">
                        {format(trip?.timestamp, "MMMM dd, yyyy hh:mm a")}
                      </Text>
                    </View>
                  )}

                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Price per seat
                    </Text>
                    <Text className="text-[14px] text-[#151B2D] font-medium">
                      {trip?.pricePerSeat}
                    </Text>
                  </View>
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      No. of passengers
                    </Text>
                    <Text className="text-[14px] text-[#151B2D] font-medium">
                      {trip?.passengers?.length}/{trip?.noOfPassengers}
                    </Text>
                  </View>
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Trip Type
                    </Text>
                    <Text className="text-[14px] text-[#151B2D] font-medium">
                      {`${trip?.mode.charAt(0).toUpperCase()}${trip?.mode.slice(
                        1
                      )}`}
                    </Text>
                  </View>
                </View>

                {preferences && (
                  <View className="mt-2">
                    <Text className="text-[#787A80] text-[14px] font-medium">
                      Preferences
                    </Text>
                    <View className="flex-row flex-wrap justify-between">
                      {trip?.preferences.map((preference) => (
                        <View
                          key={preference.desc}
                          className="py-1 justify-between mb-2 flex-row items-center gap-x-[4px]"
                        >
                          {preference?.value === true ? (
                            <Image
                              source={require("../assets/images/accepted.png")}
                              className="h-[12px] w-[12px]"
                              resizeMode="contain"
                            />
                          ) : (
                            <Image
                              source={require("../assets/images/cancel.png")}
                              className="h-[12px] w-[12px]"
                              resizeMode="contain"
                            />
                          )}

                          <Text className="text-[#4A4C50] text-[11px] font-medium">
                            {preference.desc}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DriverTripEnded;
