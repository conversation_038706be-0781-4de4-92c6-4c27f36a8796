/**
 * PASSENGER TRIP ENDED MODAL - USAGE EXAMPLES
 * 
 * This demonstrates how the pure Ably-based passenger trip ended modal system works
 */

import { driverTripEndedPublisherHelpers } from '../utils/driverTripEndedPublisher';
import { passengerTripEndedListenerHelpers } from '../utils/passengerTripEndedListener';
import { passengerTripEndedHelpers } from '../app/store/passengerTripEndedStore';

// Example trip and user data
const exampleTrip = {
  id: "trip_123",
  origin: { name: "Downtown Mall, City Center" },
  destination: { name: "Airport Terminal, International Airport" },
  passengers: [
    { id: "passenger_789", firstName: "Jane", lastName: "Smith" },
    { id: "passenger_101", firstName: "Bob", lastName: "Johnson" }
  ],
  price: "$25",
  timestamp: "2024-01-15T14:30:00Z"
};

const driverUser = { 
  id: "driver_456", 
  firstName: "<PERSON>", 
  lastName: "Doe",
  profilePicture: "https://example.com/profile.jpg"
};

const passengerUser = { 
  id: "passenger_789", 
  firstName: "Jane", 
  lastName: "Smith" 
};

/**
 * 1. DRIVER SIDE - Publishing Trip Ended Events
 */

// When driver ends trip normally
export const driverEndsTripNormally = async () => {
  try {
    await driverTripEndedPublisherHelpers.publishTripEnded(
      exampleTrip, 
      driverUser, 
      'completed'
    );
    console.log('✅ Published normal trip completion');
  } catch (error) {
    console.error('❌ Failed to publish trip ended event:', error);
  }
};

// When driver ends trip early
export const driverEndsTripEarly = async () => {
  try {
    await driverTripEndedPublisherHelpers.publishTripEndedEarly(
      exampleTrip, 
      driverUser, 
      'Traffic was lighter than expected'
    );
    console.log('✅ Published early trip completion');
  } catch (error) {
    console.error('❌ Failed to publish early trip ended event:', error);
  }
};

// When driver cancels trip
export const driverCancelsTrip = async () => {
  try {
    await driverTripEndedPublisherHelpers.publishTripCancelled(
      exampleTrip, 
      driverUser, 
      'Car breakdown'
    );
    console.log('✅ Published trip cancellation');
  } catch (error) {
    console.error('❌ Failed to publish trip cancelled event:', error);
  }
};

// When driver ends trip due to issue
export const driverEndsTripWithIssue = async () => {
  try {
    await driverTripEndedPublisherHelpers.publishTripEndedWithIssue(
      exampleTrip, 
      driverUser, 
      'Passenger no-show'
    );
    console.log('✅ Published trip ended with issue');
  } catch (error) {
    console.error('❌ Failed to publish trip issue event:', error);
  }
};

/**
 * 2. PASSENGER SIDE - Listening for Trip Ended Events
 */

// Initialize listener (done automatically in Home component)
export const initializePassengerListener = (ably: any) => {
  passengerTripEndedListenerHelpers.initialize(ably, passengerUser.id);
  console.log('🎧 Passenger listener initialized');
};

// Start listening for specific trip (done automatically when trip is active)
export const startListeningForTrip = () => {
  passengerTripEndedListenerHelpers.startListening(exampleTrip.id);
  console.log('🎧 Started listening for trip:', exampleTrip.id);
};

// Stop listening when trip ends or user leaves
export const stopListeningForTrip = () => {
  passengerTripEndedListenerHelpers.stopListening(exampleTrip.id);
  console.log('🔇 Stopped listening for trip:', exampleTrip.id);
};

/**
 * 3. MANUAL MODAL CONTROL (for testing or special cases)
 */

// Manually show the modal
export const manuallyShowModal = () => {
  const tripEndedData = {
    tripId: exampleTrip.id,
    driverName: `${driverUser.firstName} ${driverUser.lastName}`,
    driverPhoto: driverUser.profilePicture,
    origin: exampleTrip.origin.name.split(',')[0],
    destination: exampleTrip.destination.name.split(',')[0],
    cost: exampleTrip.price,
    endReason: 'completed' as const,
    endTime: new Date().toISOString(),
  };

  passengerTripEndedHelpers.showModal(tripEndedData);
  console.log('📱 Manually showed passenger trip ended modal');
};

// Manually hide the modal
export const manuallyHideModal = () => {
  passengerTripEndedHelpers.hideModal();
  console.log('❌ Manually hid passenger trip ended modal');
};

// Check if modal is visible
export const checkModalVisibility = () => {
  const isVisible = passengerTripEndedHelpers.isModalVisible();
  console.log('👁️ Modal visible:', isVisible);
  return isVisible;
};

/**
 * 4. INTEGRATION WITH EXISTING COMPONENTS
 */

// In your EndTripModal component (already implemented)
export const integrateWithEndTripModal = async (trip: any, driver: any, endReason: string) => {
  // This is already done in the EndTripModal component
  // When driver ends trip, it automatically publishes the Ably event
  
  try {
    await driverTripEndedPublisherHelpers.publishTripEnded(trip, driver, endReason as any);
    console.log('📡 EndTripModal published trip ended event');
  } catch (error) {
    console.error('❌ EndTripModal failed to publish event:', error);
  }
};

// In your Home component (already implemented)
export const integrateWithHomeComponent = (ably: any, userId: string, tripId: string) => {
  // This is already done in the Home component
  // Automatically initializes listener when Ably connects
  
  passengerTripEndedListenerHelpers.initialize(ably, userId);
  passengerTripEndedListenerHelpers.startListening(tripId);
  console.log('🏠 Home component integrated with passenger listener');
};

/**
 * 5. DEBUGGING AND MONITORING
 */

// Check listener status
export const checkListenerStatus = () => {
  const isReady = passengerTripEndedListenerHelpers.isReady();
  const listeningTrips = passengerTripEndedListenerHelpers.getListening();
  
  console.log('🔍 Listener ready:', isReady);
  console.log('🔍 Listening for trips:', listeningTrips);
  
  return { isReady, listeningTrips };
};

// Check publisher status
export const checkPublisherStatus = () => {
  const isReady = driverTripEndedPublisherHelpers.isReady();
  
  console.log('🔍 Publisher ready:', isReady);
  
  return { isReady };
};

// Get current modal state
export const getCurrentModalState = () => {
  const tripData = passengerTripEndedHelpers.getTripData();
  const isVisible = passengerTripEndedHelpers.isModalVisible();
  
  console.log('🔍 Current modal state:', { isVisible, tripData });
  
  return { isVisible, tripData };
};

/**
 * FLOW SUMMARY:
 * 
 * 1. Driver ends trip → EndTripModal calls driverTripEndedPublisherHelpers.publishTripEnded()
 * 2. Ably publishes "trip-ended" event on channel "trip-{tripId}"
 * 3. Passenger app (Home component) listens via passengerTripEndedListenerHelpers
 * 4. Event received → passengerTripEndedHelpers.showModal() called
 * 5. PassengerTripEndedModal appears over current screen (anywhere in app)
 * 6. User interacts with modal (rate trip, close, etc.)
 * 7. Modal closes → navigate to appropriate screen
 * 
 * The system is fully automatic and works regardless of which screen the passenger is on!
 */
