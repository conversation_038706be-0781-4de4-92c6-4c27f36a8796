import {
  View,
  Text,
  Image,
  StyleSheet,
  StatusBar,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from "react-native";

import PersonalInfo from "@/components/PersonalInfo";
import Payment from "@/components/Payment";
import Account from "@/components/Account";
import { SafeAreaView } from "react-native-safe-area-context";

import {} from "react-native";
import React, { useCallback, useMemo, useRef, useState } from "react";

import Button from "@/components/Button";
import BottomSheet, { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import { makePhoneCall } from "@/utils/helpers";
import useGetVerification from "@/hooks/useGetVerification";
import Others from "@/components/Others";

const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

const Profile = () => {
  const { data } = useGetVerification();
  console.log('verification', data);
  const bottomSheetRef = useRef<BottomSheet>(null);
  const screenHeight = Dimensions.get("window").height;

  // Calculate a dynamic snap point based on screen height
  const dynamicSnapPoint = useMemo(() => {
    const minHeight = 300; // Minimum height in pixels
    const percentHeight = screenHeight * 0.33; // 33% of screen height
    return Math.max(minHeight, percentHeight);
  }, [screenHeight]);

  const customSnapPoints = useMemo(
    () => [dynamicSnapPoint],
    [dynamicSnapPoint]
  );
  const [showModal, setShowModal] = useState(false);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        style={[StyleSheet.absoluteFillObject, styles.backdrop]}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        {...props}
      />
    ),
    []
  );

  const handleClosePress = () => bottomSheetRef.current?.close();
  const handleOpenPress = () => bottomSheetRef.current?.expand();
  return (
    <SafeAreaView className="h-full w-full mb-10 bg-slate-50">
      <ScrollView
        className="w-[90%] mx-auto py-4 h-full"
        showsVerticalScrollIndicator={false}
      >
        <View className="flex-row justify-between items-center mt-4">
          <Text className="text-[22px] text-[#151B2D] font-semibold">
            Account
          </Text>
          <Image
            source={require("../../../assets/images/ear.png")}
            className="w-[100px] h-[40px] rounded-full"
            resizeMode="contain"
          />
        </View>
        <View className="mt-4">

          <PersonalInfo />

          <Payment />

          <Others />
          <Account
            setShowModal={setShowModal}
            handleOpenPress={handleOpenPress}
          />
        </View>
      </ScrollView>
      {showModal && (
        <BottomSheet
          ref={bottomSheetRef}
          index={0}
          snapPoints={customSnapPoints}
          enablePanDownToClose={true}
          containerStyle={styles.bottomSheetContainer}
          // handleIndicatorStyle={styles.bottomSheetIndicator}
          backgroundStyle={styles.bottomSheetBackground}
          backdropComponent={renderBackdrop}
        >
          <View className="w-full h-full flex-1 relative">
            <View className="w-[90%] mt-2 space-y-3 mx-auto h-full flex-1 items-center">
              <Image
                source={require("../../../assets/images/delete.png")}
                className="w-[50px] h-[50px]"
                resizeMode="contain"
              />
              <Text className=" text-[#151B2D] text-[20px] font-semibold">
                Delete account?
              </Text>
              <Text className=" text-[#151B2D] text-[15px] font-medium">
                We do not want to see you go
              </Text>
              <View className="w-full mt-4">
                <Button
                  text="Yes, delete"
                  buttonClassName="bg-[#********]"
                  textClassName="text-[#E05859]"
                  onClick={() => {
                    handleClosePress();
                  }}
                />
                <View className="w-full h-2" />
                <Button
                  text="Cancel"
                  buttonClassName="bg-[#EDEDED]"
                  textClassName="text-[#151B2D]"
                  onClick={() => {
                    handleClosePress();
                  }}
                />
              </View>
            </View>
          </View>
        </BottomSheet>
      )}
    </SafeAreaView>
  );
};

export default Profile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: "white",
    width: "100%",
  },
  backdrop: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    top: -STATUSBAR_HEIGHT,
  },
  bottomSheetContainer: {
    zIndex: 1000,
  },
  bottomSheetIndicator: {
    backgroundColor: "#fff",
  },
  bottomSheetBackground: {
    backgroundColor: "#fff",
  },
});
