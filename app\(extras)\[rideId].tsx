import { View, Text, Image, ActivityIndicator } from "react-native";
import React, { useEffect } from "react";
import { Href, router, useLocalSearchParams } from "expo-router";
import { trips } from "@/libs/data";
import Button from "@/components/Button";
import { useMutation, useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import { TripData } from "../(drawer)/(tabs)/Trips";
import { calculateTravelTime, haversineDistance } from "@/utils/helpers";
import { format } from "date-fns/format";
import { isValid, parseISO } from "date-fns";
import Toast from "react-native-toast-message";

const RideId = () => {
  const { rideId } = useLocalSearchParams();

  const { mutate: scheduleTrip, isPending } = useMutation({
    mutationFn: services.scheduleTrip,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      router.replace({
        pathname: "/(ride)/PostRide",
        params: {
          trip: encodeURIComponent(JSON.stringify(data.data)),
        },
      } as Href);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const {
    data: tripsResponse,
    refetch: refetchTrips,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["trips"],
    queryFn: () => services.getTrips("given"),
  });

  useEffect(() => {
    refetchTrips();
  }, []);

  // Check if tripsResponse is defined before accessing it
  const trips: TripData[] = tripsResponse?.data || [];

  // Find the single trip based on rideId
  const singleTrip: TripData | undefined = trips.find(
    (trip) => trip.id === rideId
  );

  // Calculate distance and travel time
  const point1 = {
    latitude: singleTrip?.origin.lat,
    longitude: singleTrip?.origin.lng,
  };
  const point2 = {
    latitude: singleTrip?.destination.lat,
    longitude: singleTrip?.destination.lng,
  };

  const distance = haversineDistance(point1, point2);
  const averageSpeed = 60; // Average speed in km/h
  const travelTime = calculateTravelTime(distance, averageSpeed);

  const formattedDate =
    singleTrip?.timestamp && isValid(parseISO(singleTrip.timestamp))
      ? format(parseISO(singleTrip.timestamp), "EEE, dd MMM yyyy")
      : "-";

  const formatTravelTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    let timeString = "";

    if (hours > 0) {
      timeString += `${hours} hour${hours > 1 ? "s" : ""}`;
    }

    if (mins > 0) {
      timeString += `${timeString ? " " : ""}${mins} minute${
        mins > 1 ? "s" : ""
      }`;
    }

    return timeString || "0 minutes";
  };

  // Show loading indicator while data is being fetched
  if (isLoading) {
    return (
      <View className="bg-white h-full w-full items-center justify-center">
        <ActivityIndicator color="#000" size="small" />
      </View>
    );
  }

  // Show error message if there is an error
  if (isError) {
    return (
      <View className="bg-white h-full w-full items-center justify-center">
        <Text>Error: {error.message}</Text>
      </View>
    );
  }

  return (
    <View className="h-full w-full bg-white">
      <View className="w-[90%] h-full mx-auto mt-4 relative">
        <View className="flex-row justify-between items-center">
          <View className="space-y-1">
            {singleTrip?.mode === "private" && (
              <Text className="text-[20px] text-[#151B2D] font-semibold">
                Private Ride
              </Text>
            )}

            <Text className="text-[20px] text-[#151B2D] font-semibold">
              {formattedDate}
            </Text>
            {/* <Text className="text-[14px] text-[#151B2D]">Fri, 27 Nov 2024</Text> */}
            <View className="bg-[#F4F4F4] rounded-[100px] py-[3px] px-[6px] self-start">
              <Text className="text-[#151B2D] text-[12px]">
                {distance.toFixed(2)}m, {formatTravelTime(travelTime)}
              </Text>
            </View>
          </View>

          <Image
            source={require("../../assets/images/ProfilePic.png")}
            className="w-[60px] h-[60px]"
            resizeMode="contain"
          />
        </View>

        <View className="mt-6">
          <Image
            source={require("../../assets/images/MapView.png")}
            className="w-full h-[145px]"
            resizeMode="cover"
          />
        </View>

        <View className="mt-6">
          <Text className="text-[18px] text-[#151B2D] font-semibold">
            Route
          </Text>
          <View className="w-full flex-row items-center mt-3">
            <View className="items-center">
              <Image
                source={require("../../assets/images/from2.png")}
                className="w-[12px] h-[12px]"
                resizeMode="contain"
              />
              <View className="h-[23px] w-[1px] bg-[#D9D9D9]" />
              <Image
                source={require("../../assets/images/to2.png")}
                className="w-[13px] h-[13px]"
                resizeMode="contain"
              />
            </View>
            <View className="gap-y-[18px] ml-3">
              <View>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[15px] text-[#151B2D]"
                >
                  {singleTrip?.origin.name}
                </Text>
                {/* <Text className="text-[12px] text-[#787A80]">10:00</Text> */}
              </View>
              <View>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[15px] text-[#151B2D]"
                >
                  {singleTrip?.destination.name}
                </Text>
                {/* <Text className="text-[12px] text-[#787A80]">10:00</Text> */}
              </View>
            </View>
          </View>
        </View>

        <View className="mt-6">
          <Text className="text-[18px] text-[#151B2D] font-semibold">
            Payment
          </Text>

          <View className="space-y-4">
            <View className="flex-row justify-between mt-2">
              <Text className="text-[#151B2D] text-[14px]">Fare</Text>
              <Text className="text-[#151B2D] text-[14px]">
                {singleTrip?.pricePerSeat} x3
              </Text>
            </View>
            <View className="flex-row justify-between mt-2">
              <Text className="text-[#151B2D] text-[14px]">Booking fee</Text>
              <Text className="text-[#151B2D] text-[14px]">N120</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-[#473BF0] text-[14px]">Discount</Text>
              <Text>N0</Text>
            </View>
          </View>

          <View className="mt-4">
            <View className="flex-row justify-between">
              <Text className="text-[#151B2D] text-[16px] font-semibold">
                Total
              </Text>
              <Text className="text-[#151B2D] text-[16px] font-semibold">
                {singleTrip?.pricePerSeat}
              </Text>
            </View>

            {/* <View className="flex-row justify-between mt-4 items-center">
              <View className="flex-row items-center space-x-2">
                <Image
                  source={require("../../assets/images/Card.png")}
                  className="w-[28px] h-[28px]"
                  resizeMode="contain"
                />
                <Text className="text-[#151B2D] text-[14px]">
                  {singleTrip.p}
                </Text>
              </View>
              <Text className="text-[#151B2D] text-[14px] font-semibold">
                N1,300
              </Text>
            </View> */}
          </View>
        </View>

        <View className="absolute bottom-10 w-full">
          <Button
            isLoading={isPending}
            buttonDisabled={isPending}
            onClick={() =>
              singleTrip?.mode === "carpool"
                ? scheduleTrip({
                    dataBody: {
                      type: singleTrip.type,
                      origin: singleTrip.origin,
                      destination: singleTrip.destination,
                      stops: singleTrip.stops,
                      noOfPassengers: singleTrip.noOfPassengers,
                      pricePerSeat: Number(singleTrip.pricePerSeat),
                      preferences: singleTrip.preferences,
                      timestamp: singleTrip.timestamp,
                      mode: "carpool",
                    },
                  })
                : Toast.show({
                    type: "success",
                    text1: "Enable private mode to book a trip like this",
                  })
            }
            text="Repost"
            buttonClassName="bg-[#EDEDED]"
            textClassName="text-[#151B2D]"
          />
        </View>
      </View>
    </View>
  );
};

export default RideId;
