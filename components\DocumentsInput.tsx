import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Image,
} from "react-native";
import React, { Dispatch, SetStateAction } from "react";

interface Props {
  placeHolder: string;
  text: string;
  value: string;
  setValue: (value: string) => void;
  onFocus: (event: any) => void;
}

const DocumentsInput = ({
  placeHolder,
  text,
  value,
  setValue,
  onFocus,
}: Props) => {
  return (
    <View className="w-full mt-2">
      <Text className="text-[#4A4C50] font-medium text-[15px]">{text}</Text>
      <View className="w-full mt-1">
        <TextInput
          className="w-full h-[48px] rounded-[6px] bg-[#F6F6F6] px-3"
          placeholder={placeHolder}
          placeholderTextColor="#A9A9A9"
          value={value}
          onChangeText={(text) => setValue(text)}
          onFocus={onFocus}
        />
      </View>
    </View>
  );
};

export default DocumentsInput;
