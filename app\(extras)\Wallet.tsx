import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import React, { useState } from "react";
import GoBack from "@/components/GoBack";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { router } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";
import { Swipeable } from "react-native-gesture-handler";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const Wallet = () => {
  const [mode, setMode] = useState("");
  const { data, refetch } = useCurrentUser();

  const { mutate: deleteCard, isPending } = useMutation({
    mutationFn: services.detachCard,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });

      refetch();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
      console.log(
        error.response
          ? error.response.data.description || error.response.data.message
          : error.message
      );
    },
  });

  const answersData = [
    {
      id: "0",
      text: "Cash",
      image: require("../../assets/images/cash.png"),
    },
    // Add dynamic cards with updated state
    ...data?.cards?.map((card: { id: string[] }, index: number) => ({
      id: card.id,
      text: `Debit card ending with XX${index + 99}`,
      image: require("../../assets/images/bank.png"),
    })),
  ];

  // swipeable delete button
  const renderRightActions = (id: string) => (
    <TouchableOpacity
      disabled={isPending}
      activeOpacity={0.7}
      className="bg-[#E05859] items-center justify-center w-[90px] border border-[#F9F9F9] mb-2 p-4 rounded-r-[6px]"
      onPress={() => deleteCard(id)}
    >
      {isPending ? (
        <ActivityIndicator color="#fff" size="small" />
      ) : (
        <Text className="text-[#FFFFFF] text-[15px] font-medium">Delete</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <View className="w-[90%] h-full pt-5 mx-auto">
        <View className="mt-2 bg-[#473BF008] p-3 w-full">
          <View>
            <Text className="text-[#787A80] text-[12px] font-semibold">
              Wallet balance
            </Text>
            <Text className="text-[#B3B3B3] text-[26px] font-bold">N0.00</Text>
          </View>
          <View className="mt-3">
            <View className="w-[45%]">
              <Button
                text="Withdraw"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => router.push("/(extras)/Withdraw")}
              />
            </View>
          </View>

          <View className="w-full h-[1px] my-4 bg-[#E7E7E7]"></View>

          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => router.push("/TransactionHistory")}
            className="flex-row justify-between items-center"
          >
            <View className="items-center flex-row">
              <Image
                source={require("../../assets/images/clock.png")}
                className="w-[18px] h-[18px]"
                resizeMode="contain"
              />
              <Text className="text-[13px] font-medium text-[#4A4C50] ml-2">
                See transaction history
              </Text>
            </View>
            <Image
              source={require("../../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>

        <View className="mt-5">
          <Text className="text-[#151B2D] text-[20px] font-semibold">
            Payment methods
          </Text>
          <Text className="text-[13px] text-[#4A4C50] font-medium mt-1">
            Select your default payment method
          </Text>

          <View className="flex-wrap justify-between mt-4">
            {answersData.map((datum) => {
              const CardComponent = datum.id !== "0" ? Swipeable : View;

              return (
                <CardComponent
                  key={datum.id}
                  renderRightActions={() =>
                    datum.id !== "0" ? renderRightActions(datum.id) : null
                  }
                >
                  <TouchableOpacity
                    activeOpacity={0.9}
                    className={`w-full h-[60px] bg-white border drop-shadow-md border-[#F9F9F9] mb-2 p-4 rounded-[6px] items-center flex-row justify-between
                      
                    } `}
                    onPress={() => setMode(datum.id)}
                  >
                    <View className="flex-row items-center gap-x-2">
                      <Image
                        source={datum.image}
                        className="w-[22px] h-[22px]"
                        resizeMode="contain"
                      />
                      <Text className="text-[#151B2D] text-[14px] font-medium">
                        {datum.text}
                      </Text>
                    </View>
                    <View
                      className={`w-[20px] h-[20px] rounded-[15px] border justify-center items-center ${
                        mode === datum.id ? "border-[#473BF0]" : "border-black"
                      } `}
                    >
                      {mode === datum.id && (
                        <View className="w-[12px] h-[12px] rounded-[11px] bg-[#473BF0]" />
                      )}
                    </View>
                  </TouchableOpacity>
                </CardComponent>
              );
            })}
          </View>

          <TouchableOpacity
            onPress={() => router.push("/(extras)/NewCard")}
            className="w-full h-[60px] border border-[#F9F9F9] mb-2 p-4 rounded-[6px] items-center flex-row justify-between"
          >
            <View className="flex-row space-x-3 items-center">
              <Image
                source={require("../../assets/images/add.png")}
                className="h-[30px] w-[30px]"
              />
              <Text className="text-[#151B2D] text-[14px] font-medium">
                Add new credit/debit card
              </Text>
            </View>
            <Image
              source={require("../../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Wallet;
