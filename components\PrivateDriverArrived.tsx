import React, { useEffect, useMemo, useRef } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { router } from "expo-router";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import { Passenger, Trip } from "@/utils/types";
import { Realtime } from "ably";
import useActiveTrip from "@/hooks/useActiveTrip";
import useCurrentUser from "@/hooks/useCurrentUser";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setShowPassenger: React.Dispatch<React.SetStateAction<boolean>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  // userTripInformation: Passenger | undefined;
  trip: Trip;
  isLoading: boolean;
  refetchActive: any;
}

const PrivateDriverArrived: React.FC<Props> = ({
  step,
  setStep,
  setShowPassenger,
  setCancelRide,
  // userTripInformation,
  trip,
  refetchActive,
  isLoading,
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const screenHeight = Dimensions.get("window").height;

  const { data: user } = useCurrentUser();
  // const { data, isLoading, refetchActive } = useActiveTrip();

  // const trip: Trip = data?.data;

  // console.log("driver arrived", data);

  const userTripInformation: Passenger | undefined = trip?.passengers?.find(
    (passenger) => passenger.id === user?.id
  );

  // Calculate dynamic snap points based on screen height
  const customSnapPoints = useMemo(() => {
    const percentages = [23, 70];
    const minHeight = 250;

    return percentages.map((percentage) => {
      const calculatedHeight = (percentage / 100) * screenHeight;
      return Math.max(minHeight, calculatedHeight);
    });
  }, [screenHeight]);

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const ably = new Realtime({
    key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
    autoConnect: true,
  });

  useEffect(() => {
    // Subscribe to ride channel
    const channel = ably.channels.get(trip?.driver.id || trip?.id);

    // Handle ride acceptance
    channel.subscribe("pickup-confirmed", (message) => {
      console.log("Pickup confirmed:", message.data);
      if (message.data) {
        refetchActive();
        setStep((prevStep) => (prevStep < 5 ? 5 : prevStep));
      }
    });

    // Cleanup on unmount
    return () => {
      channel.unsubscribe();
      channel.detach();
    };
  }, [ably, trip?.id, trip?.driver?.id, refetchActive, setStep]);

  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto">
          {isLoading ? (
            <ActivityIndicator color="#000" />
          ) : (
            <View className="flex-row justify-between items-center mt-2">
              <View className="gap-y-[6px]">
                <Text className="text-[#151B2D] text-[20px] font-semibold">
                  {trip?.driver?.firstName} will arrive in 2 mins
                </Text>
                <View className="flex-row gap-x-3 items-center">
                  <Text className="text-[#151B2D] text-[15px]">
                    {trip?.driver?.carDetails?.make}{" "}
                    {trip?.driver?.carDetails?.model},{" "}
                    {trip?.driver?.carDetails?.colour}
                  </Text>
                  <View className="bg-[#EAEAEA] rounded-[3px] p-[3px]">
                    <Text className="text-[#151B2D] text-[13px] font-medium">
                      {trip?.driver?.carDetails?.plateNumber}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => setShowPassenger(true)}
              >
                <Image
                  source={require("../assets/images/ProfilePic.png")}
                  className="h-[44px] w-[44px]"
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          )}

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View className="flex-row justify-between w-full px-4">
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => router.push(`/${"chat"}`)}
              className="items-center justify-center gap-y-1"
            >
              <Image
                source={require("../assets/images/Chat.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">Chat</Text>
            </TouchableOpacity>
            <View className="items-center justify-center gap-y-1">
              <Image
                source={require("../assets/images/Call.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">Call</Text>
            </View>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => setCancelRide(true)}
              className="items-center justify-center gap-y-1"
            >
              <Image
                source={require("../assets/images/CancelRide.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">
                Cancel ride
              </Text>
            </TouchableOpacity>
          </View>

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View>
            <Text className="text-[#151B2D] text-[17px] font-semibold">
              My route
            </Text>

            <View className="mt-2">
              <View className="w-full flex-row items-center">
                <View className="items-center">
                  <Image
                    source={require("../assets/images/from2.png")}
                    className="w-[15px] h-[15px]"
                    resizeMode="contain"
                  />
                  <View className="h-[19px] w-[1px] bg-[#D9D9D9]" />
                  <Image
                    source={require("../assets/images/to2.png")}
                    className="w-[15px] h-[15px]"
                    resizeMode="contain"
                  />
                </View>
                <View className="flex-1 gap-y-[12px] ml-2">
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[14px] text-[#151B2D]"
                  >
                    {userTripInformation?.pickup.name}
                  </Text>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[14px] text-[#151B2D]"
                  >
                    {userTripInformation?.dropoff.name}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View>
            <Text className="text-[#151B2D] text-[17px] font-semibold">
              Payment method
            </Text>

            <Text className="text-[#8F8F8F] text-[14px] font-medium my-2">
              {userTripInformation?.modeOfPayment
                ? userTripInformation.modeOfPayment.charAt(0).toUpperCase() +
                  userTripInformation.modeOfPayment.slice(1)
                : ""}
            </Text>

            <View className="flex-row justify-between items-center">
              <View className="flex-row gap-x-2 items-center">
                <Image
                  source={require("../assets/images/wallet.png")}
                  className="w-[18px] h-[18px]"
                  resizeMode="contain"
                />
                <Text className="text-[#151B2D] text-[15px] font-medium">
                  XXXX 1499
                </Text>
              </View>
              <View>
                <Text className="text-[#151B2D] text-[15px] font-medium">
                  N{trip?.pricePerSeat?.toLocaleString()}
                </Text>
              </View>
            </View>

            <View className="p-3 border border-[#473bf014] shadow-sm mt-5 flex-row items-center h-[60px] rounded-[6px]">
              <Image
                source={require("../assets/images/share.png")}
                className="w-[24px] h-[24px]"
                resizeMode="contain"
              />
              <Text className="ml-4 text-[#151B2D] text-[15px] font-medium">
                Share trip details
              </Text>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PrivateDriverArrived;
