import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import React, { useState } from "react";
import AuthHeader from "@/components/AuthHeader";
import Input from "@/components/Input";
import { Href, router } from "expo-router";
import Button from "@/components/Button";
import { useUser } from "@/context/UserContext";

const ProfileSetUp = () => {
  const { userInfo, setUserDetails } = useUser();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [ownPersonalVehicle, setOwnPersonalVehicle] = useState("");

  const answersData = [
    {
      value: true,
      text: "Yes",
    },
    {
      value: false,
      text: "No",
    },
  ];

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] mx-auto h-full">
            <AuthHeader
              header="Set up your profile"
              subHeader="Please enter your details to continue"
            />

            <View className="mt-10">
              <Input
                text="Email"
                type="email"
                placeHolder="<EMAIL>"
                value={userInfo.email}
                setValue={setUserDetails}
                ObjKey="email"
              />
              <View className="h-3" />
              <Input
                text="Password"
                type="password"
                placeHolder="Enter your password"
                value={userInfo.password}
                setValue={setUserDetails}
                ObjKey="password"
                showPassword={showPassword}
                setShowPassword={setShowPassword}
              />
              <View className="h-3" />
              <Input
                text="Confirm password"
                type="password"
                placeHolder="Enter your password"
                value={userInfo.confirmPassword}
                setValue={setUserDetails}
                ObjKey="confirmPassword"
                showPassword={showConfirmPassword}
                setShowPassword={setShowConfirmPassword}
              />
              <View className="h-3" />
              <View>
                <Text className="text-[#4A4C50] font-medium text-[15px]">
                  Do you have a personal vehicle?
                </Text>
                <View className="flex-row flex-wrap justify-between mt-2">
                  {answersData.map((datum, i) => {
                    return (
                      <TouchableOpacity
                        activeOpacity={0.9}
                        key={i}
                        className={`w-[48%] mb-4 p-4  rounded-[6px] items-center flex-row justify-between ${
                          ownPersonalVehicle === datum.text
                            ? "bg-[#473BF01A] border border-[#473BF0]"
                            : "bg-[#F6F6F6]"
                        } `}
                        onPress={() => {
                          setOwnPersonalVehicle(datum.text);
                          setUserDetails(datum.value, "ownPersonalVehicle");
                        }}
                      >
                        <Text>{datum.text}</Text>
                        <View
                          className={`w-[20px] h-[20px] rounded-[15px] border justify-center items-center ${
                            ownPersonalVehicle === datum.text
                              ? "border-[#473BF0]"
                              : "border-black"
                          } `}
                        >
                          {ownPersonalVehicle === datum.text ? (
                            <View className="w-[12px] h-[12px] rounded-[11px] bg-[#473BF0]" />
                          ) : null}
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                buttonDisabled={
                  !userInfo.email ||
                  !userInfo.password ||
                  !userInfo.confirmPassword ||
                  userInfo.ownPersonalVehicle === null
                }
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => router.push("/PersonalInfo" as Href)}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default ProfileSetUp;
