import React, { useRef, useState } from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from "react-native";
import Picker from "react-native-picker-select";
import CountryPicker, { CountryCode } from "react-native-country-picker-modal";
import PhoneInput from "react-native-phone-input";
import { useUser } from "@/context/UserContext";

const EditProfileScreen = () => {
  const { userInfo, setUserDetails } = useUser();
  const [firstName, setFirstName] = useState(userInfo.firstName || "");
  const [lastName, setLastName] = useState(userInfo.lastName || "");
  const [email, setEmail] = useState(userInfo.email || "");
  const [phoneNumber, setPhoneNumber] = useState(userInfo.phoneNumber || "");
  const [gender, setGender] = useState(userInfo.gender || "");
  const [hasVehicle, setHasVehicle] = useState<string>(userInfo.hasVehicle ? "Yes" : "No");
  const [vehicleDetails, setVehicleDetails] = useState({
    make: userInfo.vehicle?.make || "",
    model: userInfo.vehicle?.model || "",
    color: userInfo.vehicle?.color || "",
    year: userInfo.vehicle?.year || "",
    chassisNumber: userInfo.vehicle?.chassisNumber || "",
    passengerCapacity: userInfo.vehicle?.passengerCapacity || "",
    plateNumber: userInfo.vehicle?.plateNumber || "",
  });

  const [phoneCountryCode, setPhoneCountryCode] = useState<CountryCode>("CA");
  const [showPhoneCountryPicker, setShowPhoneCountryPicker] = useState(false);
  const phoneInput = useRef<PhoneInput>(null);

  const handleCountrySelect = (country: any) => {
    setPhoneCountryCode(country.cca2);
    const newPhoneNumber = `+${country.callingCode[0]}`;
    setPhoneNumber(newPhoneNumber);
    if (phoneInput.current) {
      phoneInput.current.selectCountry(country.cca2.toLowerCase());
      phoneInput.current.setValue(newPhoneNumber);
    }
    setShowPhoneCountryPicker(false);
  };


  const handleSave = () => {
    const updatedUserDetails = {
      firstName,
      lastName,
      email,
      phoneNumber,
      gender,
      hasVehicle: hasVehicle === "Yes",
      vehicle: vehicleDetails,
    };
    setUserDetails("userDetailsKey", updatedUserDetails);
    Alert.alert("Profile Updated", "Your profile has been successfully updated!");
  };

  return (
    <ScrollView className="flex-1 bg-gray-50 px-4 pt-6 mb-10">
      {/* Section Title */}
      <Text className="text-lg font-semibold text-black mb-4">Personal details</Text>

      {/* Form Fields */}
      <Text>First Name</Text>
      <TextInput
        className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
        placeholder="First name"
        value={firstName}
        onChangeText={setFirstName}
      />

<Text>Last Name</Text>
      <TextInput
        className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
        placeholder="Last name"
        value={lastName}
        onChangeText={setLastName}
      />

<Text>Email Address</Text>
      <TextInput
        className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
        placeholder="Email address"
        value={email}
        onChangeText={setEmail}
      />

      {/* Phone Number Input */}
      <Text>Phone Number</Text>
      <View className="mt-5">
        <PhoneInput
          ref={phoneInput}
          initialValue={phoneNumber}
          initialCountry={phoneCountryCode.toLowerCase()}
          onPressFlag={() => setShowPhoneCountryPicker(true)}
          onChangePhoneNumber={setPhoneNumber}
          style={{
            backgroundColor: "#F6F6F6",
            height: 48,
            borderRadius: 6,
            paddingHorizontal: 10,
          }}
        />

        <CountryPicker
          countryCode={phoneCountryCode}
          visible={showPhoneCountryPicker}
          onSelect={handleCountrySelect}
          onClose={() => setShowPhoneCountryPicker(false)}
          withFlagButton={false}
          withFilter
        />
        <Text className="text-red-500 text-xs mt-1">
          {phoneInput.current?.isValidNumber()
            ? ""
            : "Please enter a valid phone number."}
        </Text>
      </View>

      {/* Gender Input */}
      <Text>Gender</Text>
      <TextInput
        className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
        placeholder="Gender"
        value={gender}
        onChangeText={setGender}
      />
  

      {/* Personal Vehicle Selection */}
      <Text className="text-lg font-semibold text-black mb-2">Do you have a personal vehicle?</Text>
      <View className="flex-row mb-4">
        <TouchableOpacity
          onPress={() => setHasVehicle("Yes")}
          className={`px-6 py-3 rounded-lg mr-2 ${
            hasVehicle === "Yes" ? "bg-blue-100 border-2 border-blue-500" : "bg-gray-200"
          }`}>
          <Text className="text-black">Yes</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setHasVehicle("No")}
          className={`px-6 py-3 rounded-lg ${
            hasVehicle === "No" ? "bg-blue-100 border-2 border-blue-500" : "bg-gray-200"
          }`}>
          <Text className="text-black">No</Text>
        </TouchableOpacity>
      </View>
      
      {/* Vehicle Details */}
      <Text  className="text-lg font-semibold text-black mb-2">Vehicle Details</Text>
      {/* Vehicle Details */}
      {hasVehicle === "Yes" && (
        <>
        <Text>Make</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Make"
            value={vehicleDetails.make}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, make: value })}
          />

<Text>Model</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Model"
            value={vehicleDetails.model}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, model: value })}
          />

<Text>Color</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Color"
            value={vehicleDetails.color}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, color: value })}
          />

<Text>Year of Manufacturing</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Year of manufacturing"
            value={vehicleDetails.year}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, year: value })}
          />

<Text>Chassis Number</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Chassis Number"
            value={vehicleDetails.chassisNumber}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, chassisNumber: value })}
          />

          <Text>Passenger capacity</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Passenger capacity"
            value={vehicleDetails.passengerCapacity}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, passengerCapacity: value })}
          />

          <Text>Plate number</Text>
          <TextInput
            className="bg-gray-200 px-4 py-3 rounded-lg mb-4"
            placeholder="Plate number"
            value={vehicleDetails.plateNumber}
            onChangeText={(value) => setVehicleDetails({ ...vehicleDetails, plateNumber: value })}
          />
        </>
      )}

      {/* Save Button */}
      <TouchableOpacity className="bg-indigo-600 py-4 rounded-lg mt-6 mb-10" onPress={handleSave}>
        <Text className="text-white text-center font-semibold text-lg">Save</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default EditProfileScreen;
