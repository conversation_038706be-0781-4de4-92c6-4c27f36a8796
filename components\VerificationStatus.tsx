import {
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ImageProps,
  SafeAreaView,
} from "react-native";
import React from "react";
import { Href, router } from "expo-router";
import Button from "@/components/Button";
import GoBack from "./GoBack";

interface Props {
  image: ImageProps;
  text: string;
  description: string;
  button?: boolean;
}

const VerificationStatus = ({ image, text, description, button }: Props) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="bg-white h-full w-full">
          <View className="bg-white w-full h-[80vh]">
            <View className="relative flex-1 items-center justify-center">
              <View className="w-[70%] mx-auto items-center gap-y-4">
                <Image
                  source={image}
                  className="h-[61px] w-[61px]"
                  resizeMode="contain"
                />
                <Text className="text-center text-[#151B2D] font-semibold text-[24px]">
                  {text}
                </Text>
                <Text className="text-center text-[#787A80]">
                  {description}
                </Text>
              </View>

              {button && (
                <View className="absolute bottom-10 w-[90%] mx-auto">
                  <Button
                    text="Continue"
                    buttonClassName="bg-[#473BF0]"
                    textClassName="text-[#FFFFFF]"
                    onClick={() => router.push("/Verification" as Href)}
                  />
                </View>
              )}
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default VerificationStatus;
