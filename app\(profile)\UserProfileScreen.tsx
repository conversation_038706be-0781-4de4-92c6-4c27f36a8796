import React from 'react';
import { View, Text, Image, FlatList, TouchableOpacity, ScrollView } from 'react-native';
import { styled } from 'nativewind';
import useCurrentUser from '@/hooks/useCurrentUser';
import { useNavigation } from '@react-navigation/native';
import { FontAwesome } from '@expo/vector-icons';
import { useState, useRef } from 'react';
import BottomSheet from '@gorhom/bottom-sheet';
import Button from '@/components/Button';
import Account from '@/components/Account';
import ProfilePicturePicker from '@/components/ProfilePicture';


const StyledView = styled(View);
const StyledText = styled(Text);
const StyledImage = styled(Image);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);

const customSnapPoints = ['25%', '50%', '75%'];

const renderBackdrop = (props: any) => (
    <View {...props} className="absolute inset-0 bg-black bg-opacity-50" />
);

const UserProfileScreen: React.FC = () => {
    const { data: user } = useCurrentUser();
     const [showModal, setShowModal] = useState(false);
    const navigation = useNavigation();
    const bottomSheetRef = useRef<BottomSheet>(null);

    if (!user) {
        return (
            <StyledView className="flex-1 items-center justify-center p-5 bg-white">
                <StyledText>Loading...</StyledText>
            </StyledView>
        );
    }

    return (
        <StyledScrollView className="flex-1 p-5 bg-[#E9E9E9]">
            <StyledView className="items-center mb-5 bg-white p-3 rounded-lg">
                <ProfilePicturePicker user={user} onProfilePictureChange={() => {}} />
                {/* <StyledImage source={{ uri: user.avatar }} className="w-24 h-24 rounded-full mb-3" /> */}
                <StyledText className="text-2xl font-bold mb-1">{`${user.firstName} ${user.lastName}`}</StyledText>
                <StyledTouchableOpacity>
                    <StyledText className="text-blue-500 mb-2">Edit personal details</StyledText>
                </StyledTouchableOpacity>

                <StyledView className="flex-row items-center justify-center bg-green-100 p-1 rounded-lg mt-2">
                    <StyledText className="text-green-500 mb-2">Verified</StyledText>
                    <FontAwesome name="check-circle" size={14} color="green" style={{ marginLeft: 5 }} />
                </StyledView>

                <StyledView className="flex-row justify-between w-full mt-3 mb-5">
                    <StyledView className="flex-1 items-center">
                        <StyledText className="text-gray-500">Rides given</StyledText>
                        <StyledText className="text-lg font-bold">{user.ridesGiven || 0}</StyledText>

                    </StyledView>

                    <StyledView className="flex-1 items-center">
                        <StyledText className="text-gray-500">Rides taken</StyledText>
                        <StyledText className="text-lg font-bold">{user.ridesTaken || 0}</StyledText>

                    </StyledView>

                    <StyledView className="flex-1 items-center">
                        <StyledText className="text-gray-500">Ratings</StyledText>
                        <StyledView className="flex-row items-center">
                            <FontAwesome name="star" size={14} color="gold" style={{ marginLeft: 5 }} />
                            <StyledText className="text-lg font-bold">N/A</StyledText>
                        </StyledView>

                    </StyledView>
                </StyledView>
            </StyledView>

            <StyledView className="mb-4 p-3 border bg-white border-gray-200 rounded-lg flex-row justify-between items-center">
                <StyledView>
                    <StyledText className="text-gray-500">Vehicle(s):</StyledText>
                    <StyledText className="text-lg">{user?.verification.carDetails.make} {user?.verification.carDetails.model}</StyledText>
                </StyledView>

                <StyledTouchableOpacity>
                    <StyledText className="text-blue-500">Add</StyledText>
                </StyledTouchableOpacity>
            </StyledView>

            <StyledView className="mb-4 p-3 border bg-white border-gray-200 rounded-lg flex-row justify-between items-center">
                <StyledView>
                    <StyledText className="text-gray-500">Email address:</StyledText>
                    <StyledText className="text-lg">{user?.email}</StyledText>
                </StyledView>

                <StyledTouchableOpacity>
                    <StyledText className="text-blue-500">Edit</StyledText>
                </StyledTouchableOpacity>
            </StyledView>

            <StyledView className="mb-4 p-3 border bg-white border-gray-200 rounded-lg flex-row justify-between items-center">
                <StyledView>
                    <StyledText className="text-gray-500">Phone number(s):</StyledText>
                    <StyledText className="text-lg">{user?.phoneNumber}</StyledText>
                </StyledView>

                <StyledTouchableOpacity>
                    <StyledText className="text-blue-500">Add</StyledText>
                </StyledTouchableOpacity>
            </StyledView>
            <StyledText className="text-lg font-bold mb-2 bg-white p-3 rounded-lg">0 Upcoming trip</StyledText>

            <StyledView className="flex-1 justify-end">
                <StyledTouchableOpacity 
                    className="bg-white py-3 px-6 rounded-full mt-5 items-center opacity-50 flex-row justify-center"
                    onPress={() => setShowModal(true)}
                >
                    <FontAwesome name="trash" size={20} color="red" style={{ marginRight: 10 }} />
                    <StyledText className="text-black text-lg">Delete account</StyledText>
                </StyledTouchableOpacity>
            {showModal && (
                <StyledView className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <StyledView className="bg-white p-5 rounded-lg items-center">
                        <FontAwesome name="trash" size={30} color="red" style={{ marginBottom: 10 }} />
                        <StyledText className="text-2xl font-bold mt-4 text-center">Delete account</StyledText>
                        <StyledText className="text-lg mb-4 text-center">Are you sure you want to delete your account?</StyledText>
                        <StyledView className="flex-row justify-between w-full">
                            <StyledTouchableOpacity 
                                className="bg-red-500 opacity-70 py-2 px-14 rounded-full"
                                onPress={() => {
                                    // Add your delete account logic here
                                    setShowModal(false);
                                }}
                            >
                                <StyledText className="text-white text-center">Yes, delete</StyledText>
                            </StyledTouchableOpacity>
                            <StyledTouchableOpacity 
                                className="bg-gray-300 py-2 px-14 rounded-full"
                                onPress={() => setShowModal(false)}
                            >
                                <StyledText className="text-black text-center">No</StyledText>
                            </StyledTouchableOpacity>
                        </StyledView>
                    </StyledView>
                </StyledView>
            )}
            </StyledView>

            <StyledView className="h-20" />
            
        </StyledScrollView>
    );
};


export default UserProfileScreen;

