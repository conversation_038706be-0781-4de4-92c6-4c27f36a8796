import {
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
  StatusBar,
  Image,
  ActivityIndicator,
} from "react-native";
import React from "react";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import { useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import useCurrentUser from "@/hooks/useCurrentUser";
import Toast from "react-native-toast-message";

const Verification = () => {
  const { data: user } = useCurrentUser();

  console.log(user.verificationStatus);

  const { data, isLoading, error } = useQuery({
    queryKey: ["verification"],
    queryFn: () => services.getCompleteVerification(),
  });

  // console.log("data", data.data);

  // const documentsToUpload = [
  //   {
  //     label: "Car details",
  //     value: null,
  //   },
  //   {
  //     label: "Driver's & Vehicle license",
  //     value: null,
  //   },
  //   {
  //     label: "Motor insurance & Road worthiness",
  //     value: null,
  //   },
  //   {
  //     label: "BVN/NIN",
  //     value: null,
  //   },
  // ];

  const documentsToUpload = [
    {
      label: "Car details",
      value: data?.data?.carDetails ?? null,
    },
    {
      label: "Driver's & Vehicle license",
      value:
        data?.data?.driverLicense_front &&
        data?.data?.driverLicense_back &&
        data?.data?.vehicleLicense
          ? true
          : null,
    },
    {
      label: "Motor insurance & Road worthiness",
      value:
        data?.data?.motorInsurance && data?.data?.roadWorthiness ? true : null,
    },
    {
      label: "BVN/NIN",
      value: data?.data?.identity ?? null,
    },
  ];

  // console.log("docs", documentsToUpload);

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="small" color="#000" />
      </View>
    );
  }

  // console.log(data);
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            {/* <GoBack /> */}

            <View className="mt-8 items-center justify-center">
              <Image
                source={require("../../assets/images/Verify.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] text-[22px] font-semibold text-center mt-2">
                Please upload these documents to verify your account
              </Text>
            </View>

            <View>
              {documentsToUpload.map((document, i) => (
                <View key={i} className="flex-row justify-between mt-4">
                  <Text className="text-[#151B2D] font-medium text-[16px]">
                    {document?.label}
                  </Text>
                  {document?.value === true ? (
                    <Image
                      source={require("../../assets/images/check_circle.png")}
                      className="w-[20px] h-[20px]"
                      resizeMode="contain"
                    />
                  ) : document?.value === false ? (
                    <Image
                      source={require("../../assets/images/close_circle.png")}
                      className="w-[20px] h-[20px]"
                      resizeMode="contain"
                    />
                  ) : (
                    <View className="h-[17px] w-[17px] rounded-[100px] border border-[#B3B3B3]" />
                  )}
                </View>
              ))}
              <Text className="text-[#787A80] text-[13px] font-medium text-center mt-3">
                *We would never share your personal details to anyone
              </Text>
            </View>

            <View className="absolute bottom-5 w-full">
              <Text className="text-center text-[#787A80] text-[13px] font-medium mb-3">
                For security purposes provide documents that match your profile.
              </Text>
              <Button
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  Toast.show({
                    type: "info",
                    text1: "It works",
                  });
                  user?.isVerified === true ||
                  user?.verificationStatus === 'PENDING' ||
                  user?.verification?.verificationStatus === "IN_PROGRESS"
                    ? router.replace("/Status" as Href)
                    : router.replace("Upload" as Href)
                }}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default Verification;
