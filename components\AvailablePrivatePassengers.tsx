import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import Modal from "@/components/Modal";
import BottomSheet, { BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { Trip, TripRequestsResponse } from "@/utils/types";
import { services } from "@/services";
import {
  QueryObserverResult,
  RefetchOptions,
  useMutation,
} from "@tanstack/react-query";
import Toast from "react-native-toast-message";
import { useRide } from "@/context/RideProvider";
import { useUser } from "@/context/UserContext";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  rideRequests: TripRequestsResponse;
  trip: Trip;
  refetch: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
  activeTrip: Trip;
  refetchActive: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
}

const AvailablePrivatePassengers: React.FC<Props> = ({
  step,
  setStep,
  setCancelRide,
  rideRequests,
  trip,
  refetch,
  activeTrip,
  refetchActive,
}) => {
  const { displayCurrentAddress } = useUser();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [loadingRequestId, setLoadingRequestId] = useState<{
    id: string | null;
    type: "accept" | "reject" | null;
  }>({ id: null, type: null });

  const customSnapPoints = ["45%", "65%", "95%"];

  const { mutate: acceptRideRequest } = useMutation({
    mutationFn: services.acceptRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setLoadingRequestId({ id: null, type: null });
      refetch();
      refetchActive();
      setTimeout(() => {
        setStep(step + 1);
      }, 1000);
    },
    onError: (error: any) => {
      setLoadingRequestId({ id: null, type: null });
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const { mutate: rejectRideRequest } = useMutation({
    mutationFn: services.rejectRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setLoadingRequestId({ id: null, type: null });
      setTimeout(() => {
        refetch();
      }, 500);
    },
    onError: (error: any) => {
      setLoadingRequestId({ id: null, type: null });
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const handleAcceptRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "accept" });
    acceptRideRequest({ dataBody: { requestId } });
  };

  const handleRejectRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "reject" });
    rejectRideRequest({ dataBody: { requestId } });
  };

  const renderItem = ({ item }: { item: any }) => {
    const isAcceptLoading =
      loadingRequestId.id === item.requestId &&
      loadingRequestId.type === "accept";
    const isRejectLoading =
      loadingRequestId.id === item.requestId &&
      loadingRequestId.type === "reject";

    const { travelData } = useTravelTimeAndDistance(
      displayCurrentAddress,
      item?.pickup?.name
    );
    return (
      <View>
        <TouchableOpacity
          key={item.id}
          activeOpacity={0.9}
          className="mb-3 bg-[#FFFFFF] border border-[#FFFFFF] shadow-sm rounded-[6px] p-4"
        >
          <View className="flex-row w-full">
            <Image
              source={require("../assets/images/ProfilePic.png")}
              className="w-[40px] h-[40px]"
              resizeMode="contain"
            />
            <View className="ml-2 flex-1 justify-center">
              <Text className="text-[16px] font-medium text-[#151B2D]">
                {`${item?.firstName} ${item?.lastName}`}
              </Text>
              <View className="flex-row items-center mt-1.5">
                <View className="items-center">
                  <Image
                    source={require("../assets/images/from2.png")}
                    className="w-[12px] h-[12px]"
                    resizeMode="contain"
                  />
                  <View className="h-[12px] w-[1px] bg-[#D9D9D9]" />
                  <Image
                    source={require("../assets/images/to2.png")}
                    className="w-[13px] h-[13px]"
                    resizeMode="contain"
                  />
                </View>
                <View className="flex-1 ml-1 gap-y-[8px]">
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#787A80]"
                  >
                    {item?.pickup?.name}
                  </Text>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#787A80]"
                  >
                    {item?.dropoff?.name}
                  </Text>
                </View>
              </View>
              <Text className="text-[#151B2D] text-[13px] font-medium mt-2">
                {travelData?.duration} away
              </Text>
            </View>
          </View>

          <View className="flex-row justify-between mt-4">
            <TouchableOpacity
              onPress={() => handleRejectRideRequest(item.requestId)}
              disabled={isRejectLoading || isAcceptLoading}
              activeOpacity={0.9}
              className="bg-[#E05859] items-center justify-center w-[48%] rounded-[100px] py-[8px] px-[10px]"
            >
              {isRejectLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text className="text-[#FFFFFF] text-[12px] font-semibold">
                  Decline
                </Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => handleAcceptRideRequest(item.requestId)}
              disabled={isAcceptLoading || isRejectLoading}
              activeOpacity={0.9}
              className="bg-[#473BF0] items-center justify-center w-[48%] rounded-[100px] py-[8px] px-[10px]"
            >
              {isAcceptLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text className="text-[#FFFFFF] text-[12px] font-semibold">
                  Accept
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // useEffect(() => {
  //   if (!rideRequests?.data) {
  //     setStep(step - 1);
  //   }
  // }, []);

  useEffect(() => {
    if (
      activeTrip?.status === "pending" &&
      activeTrip?.passengers?.length === 2
    ) {
      setStep(3);
    }
  }, [activeTrip]);

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 w-[90%] mx-auto -mt-5">
        <BottomSheetFlatList
          showsVerticalScrollIndicator={false}
          data={rideRequests?.data}
          ListHeaderComponent={() => (
            <View className="gap-y-1 mb-4">
              <Text className="text-[#151B2D] font-semibold text-xl">
                Available Passengers
              </Text>
              <Text className="text-[#151B2D] text-[16px]">
                Accept or decline requests
              </Text>
            </View>
          )}
          renderItem={renderItem}
          keyExtractor={(item) => `${item.email}-${item.id}`}
          contentContainerStyle={{ flexGrow: 1 }}
          style={{ marginTop: 30 }}
          // ListFooterComponent={() => (
          //   <TripInformation trip={trip} setCancelRide={setCancelRide} />
          // )}
        />
      </View>
    </Modal>
  );
};

export default AvailablePrivatePassengers;
