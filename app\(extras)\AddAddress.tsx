import {
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import React, { useState } from "react";
import GooglePlaces from "@/components/GooglePlaces";
import { useUser } from "@/context/UserContext";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import { router } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";
import Toast from "react-native-toast-message";
import Button from "@/components/Button";

const AddAddress = () => {
  const [isOriginFocused, setIsOriginFocused] = useState(false);

  const { ride, updateLocation, updateRide } = useUser();
  const { refetch } = useCurrentUser();

  const { mutate: updateAddresses, isPending } = useMutation({
    mutationFn: services.updateAddresses,
    onSuccess: (data: any) => {
      refetch();
      Toast.show({
        type: "success",
        text1: data?.message,
      });
      router.back();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const placeholderText = isOriginFocused
    ? "Enter pick up location"
    : "Pick up location";

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="flex-1 h-full w-full bg-white">
          <View className="w-[90%] mx-auto justify-between flex-1">
            <GooglePlaces
              placeholderText={placeholderText}
              isFocused={isOriginFocused}
              onPlaceSelected={(location) => updateLocation("pickup", location)}
              setIsFocused={setIsOriginFocused}
              leftFocusedImage={require("../../assets/images/radio.png")}
              ObjKey="origin"
            />

            <Button
              buttonDisabled={isPending || !ride.pickup}
              isLoading={isPending}
              text="Continue"
              buttonClassName="bg-[#473BF0] mb-[8%]"
              textClassName="text-white"
              onClick={() => {
                updateAddresses({
                  dataBody: {
                    addresses: [
                      {
                        lat: ride.pickup.lat,
                        lng: ride.pickup.lng,
                        name: ride.pickup.name,
                      },
                    ],
                  },
                });
              }}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default AddAddress;
