import {
  View,
  Text,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
} from "react-native";
import React, { useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { router } from "expo-router";

const Withdraw = () => {
  const [amount, setAmount] = useState(0);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="w-full h-full bg-white">
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: "space-between",
            }}
            keyboardShouldPersistTaps="handled"
          >
            <View className="w-[90%] mx-auto mt-2">
              <Input
                placeHolder="N0.0"
                type="Number"
                text="Amount"
                value={amount as unknown as string}
                setValue={setAmount}
              />
              <View className="flex-row space-x-1 mt-2">
                <Text className="text-[13px] font-medium text-[#787A80]">
                  Wallet balance:
                </Text>
                <Text className="text-[13px] font-medium text-[#4A4C50]">
                  N123,473
                </Text>
              </View>
            </View>

            <View className="w-[90%] mb-5 mx-auto">
              <Button
                buttonDisabled={!amount}
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => router.push("/(extras)/Account")}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default Withdraw;
