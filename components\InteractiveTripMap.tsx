import { View, StyleSheet, Dimensions } from "react-native";
import React from "react";
import MapView, {
  PROVIDER_DEFAULT,
  Marker,
} from "react-native-maps";
import MapViewDirections from "react-native-maps-directions";

const { width: screenWidth } = Dimensions.get('window');

interface TripData {
  id: string;
  origin: {
    lat: number;
    lng: number;
    name: string;
  };
  destination: {
    lat: number;
    lng: number;
    name: string;
  };
}

interface Props {
  trip: TripData;
}

const InteractiveTripMap = ({ trip }: Props) => {
  // Calculate map region to fit both origin and destination
  const mapRegion = {
    latitude: (trip.origin.lat + trip.destination.lat) / 2,
    longitude: (trip.origin.lng + trip.destination.lng) / 2,
    latitudeDelta: Math.abs(trip.origin.lat - trip.destination.lat) * 1.3 || 0.1,
    longitudeDelta: Math.abs(trip.origin.lng - trip.destination.lng) * 1.3 || 0.1,
  };

  return (
    <View className="mt-2 rounded-xl flex-1 w-[100%] h-[150px]">
      <MapView
      className="flex-1 rounded-xl"
        region={mapRegion}
        provider={PROVIDER_DEFAULT}
        mapType="standard"
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={false}
        showsScale={false}
        showsBuildings={true}
        showsTraffic={false}
        showsIndoors={true}
        loadingEnabled={true}
      >
        {/* Origin Marker */}
        <Marker
          coordinate={{
            latitude: trip.origin.lat,
            longitude: trip.origin.lng,
          }}
          pinColor="#473BF0"
          title="Origin"
          description={trip.origin.name}
        />
        
        {/* Destination Marker */}
        <Marker
          coordinate={{
            latitude: trip.destination.lat,
            longitude: trip.destination.lng,
          }}
          pinColor="#E5850C"
          title="Destination"
          description={trip.destination.name}
        />

        {/* Route Directions */}
        <MapViewDirections
          origin={{
            latitude: trip.origin.lat,
            longitude: trip.origin.lng,
          }}
          destination={{
            latitude: trip.destination.lat,
            longitude: trip.destination.lng,
          }}
          apikey={process.env.EXPO_PUBLIC_GOOGLE_API_KEY as string}
          strokeWidth={4}
          strokeColor="#473BF0"
          optimizeWaypoints={true}
          onStart={(params) => {
            console.log(`Started routing between "${params.origin}" and "${params.destination}"`);
          }}
          onReady={(result) => {
            console.log(`Distance: ${result.distance} km`);
            console.log(`Duration: ${result.duration} min.`);
          }}
          onError={(errorMessage) => {
            console.log('GOT AN ERROR', errorMessage);
          }}
        />
      </MapView>
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     width: screenWidth,
//     height: 350,
//     backgroundColor: '#F5F5F5',
//   },
//   map: {
//     flex: 1,
//   },
// });

export default InteractiveTripMap;