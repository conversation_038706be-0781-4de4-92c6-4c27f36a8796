import {
  View,
  Text,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  Image,
  TouchableOpacity,
} from "react-native";
import React, { useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import Modal from "react-native-modal";

const Account = () => {
  const [addAccount, setAddAccount] = useState(false);
  const [accountNumber, setAccountNumber] = useState('');
  const [bankName, setBankName] = useState("");
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="w-full h-full bg-white">
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: "space-between",
            }}
            keyboardShouldPersistTaps="handled"
          >
            <View className="w-[90%] mx-auto mt-2">
              <Text className="text-[16px] text-[#151B2D]">
                Select account number
              </Text>

              <View className="bg-[#F6F6F6] rounded-[6px] p-2.5 mt-3 flex-row items-center space-x-4">
                <Image
                  source={require("../../assets/images/bank_logo.png")}
                  className="w-[36px] h-[36px]"
                  resizeMode="contain"
                />
                <View className="space-y-1">
                  <Text className="text-[15px] text-[#4A4C50]">
                    United Bank of Africa
                  </Text>
                  <Text className="text-[14px] text-[#B3B3B3]">**********</Text>
                </View>
              </View>

              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => setAddAccount(!addAccount)}
                className="mt-3 flex-row items-center space-x-2"
              >
                <Image
                  source={require("../../assets/images/add.png")}
                  className="w-[30px] h-[30px]"
                  resizeMode="contain"
                />
                <Text className="text-[#151B2D] text-[14px] font-normal">
                  Add new account number
                </Text>
              </TouchableOpacity>
            </View>

            <View className="w-[90%] mb-5 mx-auto">
              <Button
                // buttonDisabled={!amount}
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
              />
            </View>

            <Modal
              isVisible={addAccount}
              onBackdropPress={() => {
                // toggleSwitch();
                setAddAccount(!addAccount);
              }}
              style={{ marginBottom: 20 }}
            >
              <View className="bg-white rounded-[16px] space-y-5 h-[310px] p-6 items-center justify-center">
                <Text className="text-[#151B2D] text-[20px] font-medium">
                  Add account details
                </Text>
                <View className="w-full">
                  <Input
                    placeHolder="Zenith Bank"
                    type="string"
                    text="Bank name"
                    value={bankName}
                    setValue={setBankName}
                  />

                  <View className="h-5" />

                  <Input
                    placeHolder="**********"
                    type="Number"
                    text="Account number"
                    value={accountNumber as unknown as string}
                    setValue={setAccountNumber}
                  />
                </View>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => {
                    // setPrivateRideModal(false);
                    // router.push("/(ride)/PrivateDriver");
                  }}
                  className="bg-[#473BF0] w-full h-[40px] items-center justify-center rounded-[100px]"
                >
                  <Text className="text-white text-[14px] font-semibold">
                    Continue
                  </Text>
                </TouchableOpacity>
              </View>
            </Modal>
          </ScrollView>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default Account;
