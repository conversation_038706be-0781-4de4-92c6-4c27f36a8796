import { View, Text, StyleSheet } from "react-native";
import React, { useEffect, useState } from "react";
import { useUser } from "@/context/UserContext";
import MapView, {
  PROVIDER_DEFAULT,
  Circle,
  Marker,
  MarkerAnimated,
} from "react-native-maps";
import { RideShare, Trip } from "@/utils/types";
import MapViewDirections from "react-native-maps-directions";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  markers?: RideShare[];
  direction: boolean;
  directionOrigin: { latitude: number; longitude: number };
  directionDestination: {
    latitude: number;
    longitude: number;
  };
  setMarkerDirection: (
    latitude: number,
    longitude: number,
    id: string,
    price: number
  ) => void;
  step: number;
  pin: Trip;
}

const TripMap = ({
  markers,
  direction,
  directionOrigin,
  directionDestination,
  setMarkerDirection,
  step,
  pin,
}: Props) => {
  const { displayCurrentAddress } = useUser();
  const [activeMarkerId, setActiveMarkerId] = useState<string | null>(null);
  
  // Use a single hook for the active marker
  const { travelData } = useTravelTimeAndDistance(
    displayCurrentAddress,
    markers?.find(m => m.id === activeMarkerId)?.origin?.name || ""
  );

  // Update active marker when markers change
  useEffect(() => {
    if (markers && markers.length > 0 && !activeMarkerId) {
      setActiveMarkerId(markers[0].id);
    }
  }, [markers]);

  return (
    <View className="w-full h-full">
      <MapView
        region={{
          latitude: directionOrigin.latitude,
          longitude: directionOrigin.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        userInterfaceStyle="light"
        showsUserLocation={true}
        className="w-full h-full"
        provider={PROVIDER_DEFAULT}
      >
        {direction && (
          <MapViewDirections
            origin={directionOrigin}
            destination={directionDestination}
            apikey={process.env.EXPO_PUBLIC_GOOGLE_API_KEY as string}
            strokeWidth={3}
            strokeColor="#473BF0"
          />
        )}

        {markers &&
          markers?.map((marker) => (
            <MarkerAnimated
              key={`marker-${marker.id}`}
              coordinate={{
                latitude: marker?.origin?.lat,
                longitude: marker?.origin?.lng,
              }}
              image={require("../assets/images/Track.png")}
              tappable
              opacity={2}
              onPress={() => {
                setActiveMarkerId(marker.id);
                setMarkerDirection(
                  marker?.origin?.lat,
                  marker?.origin?.lng,
                  marker?.id,
                  marker?.pricePerSeat
                );
              }}
            />
          ))}
      </MapView>
    </View>
  );
};

export default TripMap;

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});
