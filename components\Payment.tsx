import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import { router, Href } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";

const Addresses = () => {
  const { data } = useCurrentUser();

  return (
    <View className="bg-white rounded-[9px] p-3 mt-5">
      <View className="mt-1">
        <Text className="text-[#4A4C50] text-[14px] font-medium opacity-50">
          Payment
        </Text>
        <View className="mt-5">
          {data?.addresses?.map((address: any, i: number) => (
            <View key={i} className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Image
                  source={require("../assets/images/address.png")}
                  className="w-[30px] h-[30px]"
                  resizeMode="contain"
                />
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="ml-3 text-[#151B2D] text-[14px] flex-shrink text-left mr-[10%] w-[70%]"
                >
                  {address?.name}
                </Text>
              </View>
              <Image
                source={require("../assets/images/right_line.png")}
                className="w-[18px] h-[18px]"
                resizeMode="contain"
              />
            </View>
          ))}

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Security" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/paynew.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          Payment Setting
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

                    

          <View className="w-full h-[1px] my-2 bg-white-100" />

          <View className="flex-row justify-between items-center">
                      <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => router.push("/(profile)/Security" as Href)}
                        className="flex-row items-center"
                      >
                        <Image
                          source={require("../assets/images/paynew2.png")}
                          className="w-[30px] h-[30px]"
                          resizeMode="contain"
                        />
                        <Text className="ml-3 text-[#151B2D] text-[14px]">
                          Driver Payout
                        </Text>
                      </TouchableOpacity>
                      <Image
                        source={require("../assets/images/right_line.png")}
                        className="w-[18px] h-[18px]"
                        resizeMode="contain"
                      />
                    </View>

          {/* <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => router.push("/(extras)/AddAddress")}
            className="flex-row justify-between items-center"
          >
            <View className="flex-row items-center">
              <Image
                source={require("../assets/images/add_address.png")}
                className="w-[30px] h-[30px]"
                resizeMode="contain"
              />
              <Text className="ml-3 text-[#151B2D] text-[14px]">
                Add new Address
              </Text>
            </View>
            <Image
              source={require("../assets/images/right_line.png")}
              className="w-[18px] h-[18px]"
              resizeMode="contain"
            />
          </TouchableOpacity> */}
        </View>
      </View>
    </View>
  );
};

export default Addresses;
