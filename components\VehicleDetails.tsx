import React, { Dispatch, SetStateAction, useState } from "react";
import {
  View,
  Text,
  Keyboard,
  TouchableWithoutFeedback,
  SafeAreaView,
} from "react-native";
import RNPickerSelect from "react-native-picker-select";
import Button from "@/components/Button";
import DocumentsInput from "./DocumentsInput";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import useGetVerification from "@/hooks/useGetVerification";
import useCurrentUser from "@/hooks/useCurrentUser";
import CustomPickerDropdown from "./CustomPickerDropdown";

interface Props {
  step: number;
  setStep: Dispatch<SetStateAction<number>>;
}

interface InputField {
  key: string;
  text: string;
  placeholder: string;
}

interface CarDetails {
  make: string;
  model: string;
  colour: string;
  year: number; // Change to number
  chasis: string;
  option: number; // Change to number
  plate: string;
}

const VehicleDetails: React.FC<Props> = ({ step, setStep }) => {
  const { data: verification } = useCurrentUser();

  const { data } = useGetVerification();

  console.log(data);

  const carDetails = verification?.verification?.carDetails;

  // console.log(verification.verification);

  const { mutate: uploadCarDetails, isPending } = useMutation({
    mutationFn: services.carDetails,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });

      setTimeout(() => {
        setStep(step + 1);
      }, 400);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });
  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const scrollToInput = (reactNode: any) => {
    if (reactNode) {
      scrollRef.current?.scrollToFocusedInput(reactNode);
    }
  };

  const [formData, setFormData] = useState<CarDetails>({
    make: carDetails?.make || "",
    model: carDetails?.model || "",
    colour: carDetails?.colour || "",
    year: carDetails?.yearOfManufacture || null,
    chasis: carDetails?.chassisNumber || "",
    plate: carDetails?.plateNumber || "",
    option: carDetails?.passengerCapacity || null,
  });

  const inputFields: InputField[] = [
    { key: "make", text: "Make", placeholder: "Ex. Mercedes Benz" },
    { key: "model", text: "Model", placeholder: "Ex. C300" },
    { key: "colour", text: "Colour", placeholder: "Ex. White" },
    { key: "year", text: "Year of Manufacturing", placeholder: "Ex. 2013" },
    { key: "chasis", text: "Chasis Number", placeholder: "Ex. 1234567890" },
    { key: "plate", text: "Plate number", placeholder: "Ex. 2013" },
  ];

  const options = [
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
  ];

  const handleInputChange = (key: string, value: string | number | null) => {
    setFormData((prevData) => ({ ...prevData, [key]: value }));
  };

  const handleSubmit = () => {
    uploadCarDetails({
      dataBody: {
        make: formData.make,
        model: formData.model,
        colour: formData.colour,
        yearOfManufacture: Number(formData.year),
        chassisNumber: formData.chasis,
        passengerCapacity: Number(formData.option),
        plateNumber: formData.plate,
      },
    });
  };

  return (
    <KeyboardAwareScrollView
      ref={scrollRef}
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="flex-1 my-5">
          <View className="pb-5">
            <Text
              style={{
                color: "#151B2D",
                fontSize: 20,
                fontWeight: "bold",
                marginBottom: 8,
              }}
            >
              Vehicle details
            </Text>
            {inputFields.map((field) => (
              <DocumentsInput
                key={field.key}
                text={field.text}
                placeHolder={field.placeholder}
                value={formData[field.key as keyof typeof formData] as string}
                setValue={(value) => handleInputChange(field.key, value)}
                onFocus={(event: any) => {
                  scrollToInput(event.target);
                }}
              />
            ))}

            <View style={{ marginTop: 16 }}>
              
              <CustomPickerDropdown
                label="Passenger capacity"
                items={options}
                onValueChange={(value) => handleInputChange("option", value)}
                value={formData.option}
              />
              
            </View>
          </View>

          <View style={{ marginTop: "auto", width: "100%" }}>
            <Button
              text="Next"
              buttonClassName="bg-[#473BF0]"
              buttonDisabled={
                isPending ||
                !formData.chasis ||
                !formData.colour ||
                !formData.make ||
                !formData.option ||
                !formData.model ||
                !formData.plate ||
                !formData.year
              }
              isLoading={isPending}
              textClassName="text-white"
              onClick={() => handleSubmit()}
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAwareScrollView>
  );
};

export default VehicleDetails;
