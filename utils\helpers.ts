import axios from "axios";
import { <PERSON><PERSON>, Linking, Platform } from "react-native";

interface Origin {
  latitude: number | undefined;
  longitude: number | undefined;
}

interface Destination {
  latitude: number | undefined;
  longitude: number | undefined;
}

export function haversineDistance(
  coords1: Origin | undefined,
  coords2: Destination | undefined
) {
  const toRadians = (degrees: number) => (degrees * Math.PI) / 180;

  const R = 6371e3; // Radius of the Earth in meters

  const lat1 = toRadians(coords1?.latitude!);
  const lat2 = toRadians(coords2?.latitude!);
  const deltaLat = toRadians(coords2?.latitude! - coords1?.latitude!);
  const deltaLon = toRadians(coords2?.longitude! - coords1?.longitude!);

  const a =
    Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1) *
      Math.cos(lat2) *
      Math.sin(deltaLon / 2) *
      Math.sin(deltaLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c; // Distance in meters

  return distance;
}

// Example usage
// const point1 = { latitude: 6.6866608, longitude: 6.2193106 }; // Example coordinates
// const point2 = { latitude: 6.7004438, longitude: 6.2138756 };

// const distance = haversineDistance(point1, point2);
// console.log(`Distance: ${distance.toFixed(2)} meters`);
export function extractTimeFromISO(isoString: string) {
  const date = new Date(isoString);

  // Get the hours, minutes, and seconds
  const hours = date.getUTCHours(); // Change to getHours() for local time
  const minutes = date.getUTCMinutes(); // Change to getMinutes() for local time

  // Format the time string
  const formattedTime = `${hours}:${minutes < 10 ? "0" : ""}${minutes}`;
  return formattedTime;
}
const averageSpeed = 50;

export function calculateTravelTime(distance: number, speed: number) {
  return (distance / speed).toFixed(0);
}

export async function getTravelTimeAndDistance(
  origin: string,
  destination: string
): Promise<any> {
  try {
    const apiKey = process.env.EXPO_PUBLIC_GOOGLE_API_KEY;

    if (!apiKey) {
      throw new Error("Google Maps API key not found");
    }

    const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${encodeURIComponent(
      origin
    )}&destinations=${encodeURIComponent(
      destination
    )}&units=metric&key=${apiKey}`;

    const response = await axios.get(url);
    console.log("Google Maps API Response:", response.data);

    if (response.status === 200) {
      if (response.data.status === "OK" && response.data.rows?.[0]?.elements?.[0]?.status === "OK") {
        const distanceInMeters = response.data.rows[0].elements[0].distance.value;
        const durationInSeconds = response.data.rows[0].elements[0].duration.value;

        const distanceInKilometers = distanceInMeters / 1000;
        const durationInSecondsString = `${Math.floor(durationInSeconds / 60)} mins`;

        return {
          origin: origin,
          destination: destination,
          distance: `${distanceInKilometers.toFixed(2)} km`,
          durationInSeconds,
          duration: durationInSecondsString,
        };
      } else {
        console.warn("Google Maps API returned non-OK status:", response.data.status);
        // Return a default value instead of throwing
        return {
          origin: origin,
          destination: destination,
          distance: "N/A",
          durationInSeconds: 0,
          duration: "N/A",
        };
      }
    } else {
      throw new Error(`API request failed with status ${response.status}`);
    }
  } catch (error) {
    console.error("Error fetching travel time and distance:", error);
    // Return a default value instead of throwing
    return {
      origin: origin,
      destination: destination,
      distance: "N/A",
      durationInSeconds: 0,
      duration: "N/A",
    };
  }
}

export const makePhoneCall = async (phoneNumber: string) => {
  // Format the phone number to remove any non-numeric characters
  const cleanPhoneNumber = phoneNumber.replace(/[^\d]/g, "");

  // Create the phone url based on platform
  const phoneUrl = Platform.select({
    ios: `telprompt:${cleanPhoneNumber}`,
    android: `tel:${cleanPhoneNumber}`,
  });

  // Check if device can make phone calls
  const canOpen = await Linking.canOpenURL(phoneUrl as string);

  if (!canOpen) {
    Alert.alert("Error", "Phone call feature is not available on this device", [
      { text: "OK" },
    ]);
    return;
  }

  try {
    await Linking.openURL(phoneUrl as string);
  } catch (error) {
    Alert.alert("Error", "Failed to make phone call. Please try again.", [
      { text: "OK" },
    ]);
  }
};
