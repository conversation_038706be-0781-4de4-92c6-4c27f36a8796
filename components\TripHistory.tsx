import React from 'react';
import { View, Text, FlatList, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { services } from '@/services';
import { router } from 'expo-router';
import { format, parseISO, isToday, isYesterday, startOfDay, isSameDay } from 'date-fns';

interface TripHistoryItem {
  id: string;
  status: string;
  type: string;
  passengers: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    isVerified: boolean;
    verification?: {
      carDetails?: {
        make: string;
        model: string;
        colour: string;
        yearOfManufacture: string;
        chassisNumber: string;
        passengerCapacity: number;
        plateNumber: string;
      };
    };
  }>;
  origin: {
    name: string;
    lng: number;
    lat: number;
  };
  destination: {
    name: string;
    lng: number;
    lat: number;
  };
  stops: Array<{
    name: string;
    lng: number;
    lat: number;
  }>;
  timestamp: string;
  noOfPassengers: number;
  pricePerSeat: number;
  mode: string;
  preferences: Array<{
    desc: string;
    value: boolean;
  }>;
}

interface GroupedTrips {
  [key: string]: TripHistoryItem[];
}

interface TripHistoryResponse {
  data: TripHistoryItem[];
  pagination: {
    totalRecords: number;
    totalPages: number;
    page: number;
    pageSize: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

interface TripHistoryProps {
  role: 'taken' | 'given';
}

const TripHistory: React.FC<TripHistoryProps> = ({ role }) => {
  const {
    data: tripsResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<TripHistoryResponse>({
    queryKey: ["tripHistory", role],
    queryFn: () => services.getAllTrips(role),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });





  const trips = (tripsResponse?.data || []).filter(trip => {
    const isValid = trip?.timestamp && trip?.origin && trip?.destination;
    if (!isValid) {
      console.warn('Filtering out invalid trip:', trip);
    }
    return isValid;
  });

  // Group trips by date
  const groupTripsByDate = (trips: TripHistoryItem[]): GroupedTrips => {
    const grouped: GroupedTrips = {};

    trips.forEach((trip) => {
      // Check if timestamp exists
      if (!trip?.timestamp) {
        console.warn('Trip missing timestamp:', trip);
        return; // Skip this trip
      }

      try {
        const tripDate = parseISO(trip.timestamp);
        const dateKey = format(tripDate, 'yyyy-MM-dd');

        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(trip);
      } catch (error) {
        console.warn('Error parsing trip date:', trip.timestamp, error);
      }
    });

    // Sort trips within each date group by time (newest first)
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => {
        const timeA = a?.timestamp ? new Date(a.timestamp).getTime() : 0;
        const timeB = b?.timestamp ? new Date(b.timestamp).getTime() : 0;
        return timeB - timeA;
      });
    });

    return grouped;
  };

  const formatDateHeader = (dateString: string): string => {
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return 'Today';
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      return format(date, 'EEE d, MMM');
    }
  };

  const formatTripTime = (timestamp: string): string => {
    try {
      const date = parseISO(timestamp);
      return format(date, 'h:mm a');
    } catch (error) {
      console.warn('Error formatting trip time:', timestamp, error);
      return 'Invalid time';
    }
  };

  const handleTripPress = (trip: TripHistoryItem) => {
    if (!trip) {
      console.warn('Cannot navigate to trip summary: missing trip data');
      return;
    }

    router.push({
      pathname: "/(ride)/TripSummary",
      params: {
        trip: encodeURIComponent(JSON.stringify(trip)),
      },
    });
  };

  const renderTripCard = ({ item }: { item: TripHistoryItem }) => {
    // Safety check for required data
    if (!item) {
      return null;
    }

    return (
      <View
        className="bg-white rounded-[6px] p-4 flex-row items-center"
      >

        <View className='rounded-full p-2 bg-[#473bf024] mr-3'>
          <Image source={require("../assets/images/blueCar.png")} className='w-[20px] h-[20px]' />
        </View>

        <View className='flex-row items-center justify-between flex-1'>
          <View className='flex-1'>
               <View className="flex-row items-center mb-2">
                <View className="flex-1 min-w-0">
                  <View className="flex-row items-center">
                    <Text className="font-semibold text-[15px] flex-1" numberOfLines={1}>
                      {item.origin?.name.split(",")[0] || 'Unknown origin'} to {item.destination?.name.split(",")[0] || 'Unknown destination'}
                    </Text>
                  </View>
                </View>
              </View>
              <View className="flex-row items-center justify-between">

                <Text className="font-normal text-[13px] text-[#787A80] flex-shrink-0">
                  {item.timestamp ? formatTripTime(item.timestamp) : 'No time'} -  ₦{(item.pricePerSeat || 0).toLocaleString()}
                </Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => handleTripPress(item)}>
            <Text className='text-[13px] font-medium bg-[#F4F4F4] py-1 px-[10px] rounded-full'>Rebook</Text>
          </TouchableOpacity>

          

        </View>
      </View>
    );
  };

  const renderDateSection = ({ item }: { item: [string, TripHistoryItem[]] }) => {
    const [dateKey, tripsForDate] = item;
    
    return (
      <View className="mb-6">
        <Text className="text-[13px] font-medium text-[#5B5B5B] mb-3 px-1">
          {formatDateHeader(dateKey)}
        </Text>
        {tripsForDate.map((trip, index) => (
          <View key={`${trip.id}-${index}`}>
            {renderTripCard({ item: trip })}
          </View>
        ))}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#473BF0" />
        <Text className="text-gray-500 mt-2">Loading trip history...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center px-4">
        <Image
          source={require('../assets/images/homme.png')}
          className="w-24 h-24 mb-4"
        />
        <Text className="text-lg font-medium mb-2 text-center">
          Unable to load trip history
        </Text>
        <Text className="text-gray-500 mb-4 text-center">
          Please check your connection and try again
        </Text>
        <TouchableOpacity
          onPress={() => refetch()}
          className="bg-[#473BF0] px-6 py-3 rounded-full"
        >
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (trips.length === 0) {
    return (
      <View className="flex-1 items-center justify-center px-4">
        <Image
          source={require('../assets/images/homme.png')}
          className="w-24 h-24 mb-4"
        />
        <Text className="text-lg font-medium mb-2 text-center">
          No trip history
        </Text>
        <Text className="text-gray-500 mb-4 text-center">
          {role === 'taken' 
            ? 'You haven\'t taken any rides yet' 
            : 'You haven\'t given any trips yet'
          }
        </Text>
      </View>
    );
  }

  const groupedTrips = groupTripsByDate(trips);
  const sortedDateKeys = Object.keys(groupedTrips).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  return (
    <View className="flex-1">
      <FlatList
        data={sortedDateKeys.map(dateKey => [dateKey, groupedTrips[dateKey]] as [string, TripHistoryItem[]])}
        renderItem={renderDateSection}
        keyExtractor={(item) => item[0]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 12,
          paddingVertical: 16,
          paddingBottom: 100
        }}
        refreshing={isLoading}
        onRefresh={refetch}
      />
    </View>
  );
};

export default TripHistory;
