import { View, Text, Image, ActivityIndicator } from "react-native";
import React, { useEffect } from "react";
import { Href, router, useLocalSearchParams } from "expo-router";
import Button from "@/components/Button";
import { useMutation, useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import { format, parseISO } from "date-fns";
import Toast from "react-native-toast-message";
import { useUser } from "@/context/UserContext";
import { calculateTravelTime, haversineDistance } from "@/utils/helpers";
import { useRide } from "@/context/RideProvider";

// Define TypeScript interfaces for better type safety
interface Location {
  lat: number;
  lng: number;
  name: string;
}

interface Driver {
  firstName: string;
}

interface TripData {
  id: string;
  origin: Location;
  destination: Location;
  timestamp: string;
  driver: Driver;
  pricePerSeat: number;
  mode: string;
}

interface RideTaken {
  tripData: TripData;
  requesData: {
    createdAt: string;
  };
}

const RideId = () => {
  const { rideId } = useLocalSearchParams();
  const { updateRide } = useUser();
  const { updatePrivateRideLocation } = useRide();

  const { mutate: getAvailableCoRides, isPending } = useMutation({
    mutationFn: services.getAvailableCoRides,
    onSuccess: (data: any) => {
      if (!singleTrip?.tripData) {
        Toast.show({
          type: "error",
          text1: "Trip data not available",
        });
        return;
      }

      updateRide("pickup", singleTrip.tripData.origin);
      updateRide("dropoff", singleTrip.tripData.destination);
      updatePrivateRideLocation("origin", singleTrip.tripData.origin);
      updatePrivateRideLocation("destination", singleTrip.tripData.destination);

      if (data.status) {
        singleTrip.tripData.mode === "carpool"
          ? router.push({
              pathname: "/(ride)/BookRide",
              params: { rides: JSON.stringify(data.data) },
            } as Href)
          : router.push({
              pathname: "/(ride)/PrivatePassengerMap",
              params: { rides: JSON.stringify(data.data) },
            } as Href);
      }
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const {
    data: tripsResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<{ data: RideTaken[] }>({
    queryKey: ["trips"],
    queryFn: () => services.getTrips("taken"),
  });

  useEffect(() => {
    refetch();
  }, []);

  const trips = tripsResponse?.data || [];
  const singleTrip = trips.find((trip) => trip?.tripData?.id === rideId);

  // Loading state
  if (isLoading) {
    return (
      <View className="bg-white h-full w-full items-center justify-center">
        <ActivityIndicator color="#000" size="small" />
      </View>
    );
  }

  // Error state
  if (isError) {
    return (
      <View className="bg-white h-full w-full items-center justify-center">
        <Text>Error: {(error as Error).message}</Text>
      </View>
    );
  }

  // Calculate distance and travel time
  const point1 = {
    latitude: singleTrip?.tripData.origin.lat,
    longitude: singleTrip?.tripData.origin.lng,
  };
  const point2 = {
    latitude: singleTrip?.tripData.destination.lat,
    longitude: singleTrip?.tripData.destination.lng,
  };

  const distance = haversineDistance(point1, point2);
  const averageSpeed = 60; // Average speed in km/h
  const travelTime = calculateTravelTime(distance, averageSpeed);

  // const formattedDate = format(
  //   parseISO(singleTrip?.tripData?.timestamp),
  //   "EEE, dd MMM yyyy"
  // );

  const formatTravelTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    let timeString = "";

    if (hours > 0) {
      timeString += `${hours} hour${hours > 1 ? "s" : ""}`;
    }

    if (mins > 0) {
      timeString += `${timeString ? " " : ""}${mins} minute${
        mins > 1 ? "s" : ""
      }`;
    }

    return timeString || "0 minutes";
  };

  const handleRebook = () => {
    if (!singleTrip?.tripData.origin || !singleTrip?.tripData.destination) {
      Toast.show({
        type: "error",
        text1: "Origin or destination not available",
      });
      return;
    }

    getAvailableCoRides({
      dataBody: {
        pickup: singleTrip.tripData.origin,
        dropoff: singleTrip.tripData.destination,
        mode: singleTrip.tripData.mode === "carpool" ? "carpool" : "private",
      },
    });
  };

  const formattedDate = singleTrip
    ? format(
        parseISO(
          singleTrip.tripData.timestamp ||
            singleTrip.requesData?.createdAt ||
            ""
        ),
        "EEE, dd MMM yyyy"
      )
    : "-";
  return (
    <View className="h-full w-full bg-white">
      <View className="w-[90%] h-full mx-auto mt-4 relative">
        <View className="flex-row justify-between items-center">
          <View className="space-y-1">
            {singleTrip?.tripData.mode === "carpool" ? (
              <Text className="text-[20px] text-[#151B2D] font-semibold">
                Ride with {singleTrip?.tripData.driver.firstName}
              </Text>
            ) : (
              <Text className="text-[20px] text-[#151B2D] font-semibold">
                Private Ride with {singleTrip?.tripData.driver.firstName}
              </Text>
            )}

            <Text className="text-[14px] text-[#151B2D] my-0.5">
              {formattedDate}
            </Text>
            <View className="bg-[#F4F4F4] rounded-[100px] py-[3px] px-[6px] self-start">
              <Text className="text-[#151B2D] text-[12px]">
                {distance.toFixed(2)}m, {formatTravelTime(travelTime)}
              </Text>
            </View>
          </View>

          <Image
            source={require("../../../assets/images/ProfilePic.png")}
            className="w-[60px] h-[60px]"
            resizeMode="contain"
          />
        </View>

        <View className="mt-6">
          <Image
            source={require("../../../assets/images/MapView.png")}
            className="w-full h-[145px]"
            resizeMode="cover"
          />
        </View>

        <View className="mt-6">
          <Text className="text-[18px] text-[#151B2D] font-semibold">
            Route
          </Text>
          <View className="w-full flex-row items-center mt-3">
            <View className="items-center">
              <Image
                source={require("../../../assets/images/from2.png")}
                className="w-[12px] h-[12px]"
                resizeMode="contain"
              />
              <View className="h-[23px] w-[1px] bg-[#D9D9D9]" />
              <Image
                source={require("../../../assets/images/to2.png")}
                className="w-[13px] h-[13px]"
                resizeMode="contain"
              />
            </View>
            <View className="gap-y-[18px] ml-3">
              <View>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[15px] text-[#151B2D]"
                >
                  {singleTrip?.tripData.origin.name}
                </Text>
              </View>
              <View>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="text-[15px] text-[#151B2D]"
                >
                  {singleTrip?.tripData.destination.name}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View className="mt-6">
          <Text className="text-[18px] text-[#151B2D] font-semibold">
            Payment
          </Text>

          <View className="space-y-4">
            <View className="flex-row justify-between mt-2">
              <Text className="text-[#151B2D] text-[14px]">Fare</Text>
              <Text className="text-[#151B2D] text-[14px]">
                {singleTrip?.tripData.pricePerSeat} x3
              </Text>
            </View>
            <View className="flex-row justify-between mt-2">
              <Text className="text-[#151B2D] text-[14px]">Booking fee</Text>
              <Text className="text-[#151B2D] text-[14px]">N120</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-[#473BF0] text-[14px]">Discount</Text>
              <Text>N0</Text>
            </View>
          </View>

          <View className="mt-4">
            <View className="flex-row justify-between">
              <Text className="text-[#151B2D] text-[16px] font-semibold">
                Total
              </Text>
              <Text className="text-[#151B2D] text-[16px] font-semibold">
                {singleTrip?.tripData.pricePerSeat}
              </Text>
            </View>
          </View>
        </View>

        <View className="absolute bottom-10 w-full">
          <Button
            isLoading={isPending}
            buttonDisabled={isPending}
            onClick={handleRebook}
            text="Rebook"
            buttonClassName="bg-[#EDEDED]"
            textClassName="text-[#151B2D]"
          />
        </View>
      </View>
    </View>
  );
};

export default RideId;
