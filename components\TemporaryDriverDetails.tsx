import { View, Text } from 'react-native'
import React from 'react'
import useCurrentUser from '@/hooks/useCurrentUser';
import { useRide } from '@/context/RideProvider';
import { useTripStore } from '@/app/store/tripStore';

const TemporaryDriverDetails = () => {

    const { data: user } = useCurrentUser();
    const { ride } = useRide();
    const { driverNote } = useTripStore();
    const displayDriverNote = ride.driverNote || driverNote
    //  || 'Please keep to time';

  return (
    <View className='px-4 mt-2'>
        <View className='bg-white p-4 rounded-[6px]'>
            <View className='flex flex-row gap-3 items-center'>
                <View className='h-10 w-10 bg-black rounded-full' />
                <View className='flex-1 flex-col'>
                    <Text className='font-semibold text-base'>
                            {user?.firstName} {user?.lastName}
                    </Text>
                    <View className='flex flex-row items-center'>
                        <Text className='font-light text-sm text-[#787A80]'>
                                {`${user?.verification.carDetails?.make}${user?.verification.carDetails.model}, ${user?.verification.carDetails.colour}`}
                        </Text>
                        <Text className='font-medium ml-2 text-xs p-1 rounded-md text-black bg-[#EAEAEA]'>
                                {user?.verification.carDetails?.plateNumber}
                        </Text>
                        </View>
                </View>
            </View>
            
            <View className='mt-4'>
                <Text className='text-sm font-medium text-[#787A80] mb-2'>Driver's Note</Text>
                <Text className='bg-[#F4F4F4] p-3 rounded-md text-sm'>
                    {displayDriverNote}
                </Text>
            </View>
        </View>
    </View>
  )
}

export default TemporaryDriverDetails