import {
  View,
  StatusBar,
  SafeAreaView,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
} from "react-native";
import React, { useEffect, useState } from "react";

import Input from "@/components/Input";
import Button from "@/components/Button";
import { router } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { z } from "zod";

const forgotPasswordSchema = z.object({
  emailOrPhone: z.string().refine((value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\+?[1-9]\d{1,14}$/; // Basic regex for international phone numbers
    return emailRegex.test(value) || phoneRegex.test(value);
  }, "Invalid email address or phone number"),
});

type ForgortPasswordForm = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPassword() {
  const [formState, setFormState] = useState<ForgortPasswordForm>({
    emailOrPhone: "",
  });

  const updateField = (value: string, key: keyof ForgortPasswordForm) => {
    setFormState((prev) => ({ ...prev, [key]: value }));
  };

  const [errors, setErrors] = useState<
    Partial<Record<keyof ForgortPasswordForm, string[]>>
  >({});
  const [isFormValid, setIsFormValid] = useState(false);

  useEffect(() => {
    const result = forgotPasswordSchema.safeParse(formState);
    if (result.success) {
      setErrors({});
      setIsFormValid(true);
    } else {
      setErrors(result.error.flatten().fieldErrors);
      setIsFormValid(false);
    }
  }, [formState]);

  const { mutate, isPending } = useMutation({
    mutationFn: () => services.requestPasswordReset(formState.emailOrPhone),
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Request Sent",
        text2: `If an account exists for ${formState.emailOrPhone}, a password reset PIN has been sent.`,
      });
      router.push({
        pathname: "/VerifyOTP",
        params: {
          emailOrPhone: formState.emailOrPhone,
        },
      });
    },
    onError: () => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "An error occurred. Please try again later.",
      });
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            <AuthHeader
              header="Reset password"
              subHeader="Please enter your email address to reset your "
              subbHeader="password"
            />
            <View className="mt-8">
              <Input
                text="Email address"
                type="text"
                placeHolder="<EMAIL>"
                value={formState.emailOrPhone}
                error={errors.emailOrPhone?.[0]}
                setValue={updateField}
                ObjKey="emailOrPhone"
              />
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                text={"Continue"}
                isLoading={isPending}
                buttonDisabled={!formState.emailOrPhone || isPending}
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  if (isFormValid) mutate();
                }}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
