import React, { useMemo, useRef, useState } from "react";
import { View, Text, TouchableOpacity, Image, Dimensions } from "react-native";
import { router } from "expo-router";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { useRide } from "@/context/RideProvider";
import { useUser } from "@/context/UserContext";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import useCurrentUser from "@/hooks/useCurrentUser";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}

const PrivatePaymentMode: React.FC<Props> = ({ step, setStep }) => {
  const { ride, updateRide, displayCurrentAddress, initialRegion } = useUser();
  const { privateRide } = useRide();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { data } = useCurrentUser();

  // console.log(ride.rideId);

  const screenHeight = Dimensions.get("window").height;
  const dynamicSnapPoint = useMemo(() => {
    const minHeight = 400; // Minimum height in pixels
    const percentHeight = screenHeight * 0.45;
    return Math.max(minHeight, percentHeight);
  }, [screenHeight]);

  const customSnapPoints = useMemo(
    () => [dynamicSnapPoint],
    [dynamicSnapPoint]
  );

  const { mutate: request, isPending } = useMutation({
    mutationFn: services.requestPrivateRide,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setStep(step + 1);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const answersData = [
    {
      id: "0",
      label: "Cash",
      value: "cash",
      image: require("../assets/images/cash.png"),
    },
    {
      id: "2",
      label: "Debit card ending with XX99",
      value: "card",
      image: require("../assets/images/bank.png"),
    },
    {
      id: "3",
      label: "Apple Pay or GPay",
      value: "gpay",
      image: require("../assets/images/wallet.png"),
    },
  ];

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  return (
    <Modal
      handleGoBack={handleGoBack}
      goBack
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative">
        <View className="flex-1 w-[90%] mx-auto">
          <View className="gap-y-1 py-2">
            <Text className="text-[#151B2D] font-semibold text-xl">
              Mode of payment
            </Text>
            <View className="flex-row justify-between items-center">
              <Text className="text-[#151B2D] text-[16px]">
                Select mode of payment for your ride
              </Text>
              <Text className="text-[#151B2D] text-[14px] font-semibold">
                {ride.price}
              </Text>
            </View>
          </View>
          <View className="flex-wrap justify-between mt-2">
            {answersData.map((datum) => {
              return (
                <TouchableOpacity
                  activeOpacity={0.9}
                  key={datum.id}
                  className={`w-full h-[60px] border border-[#F9F9F9] shadow-sm mb-2 p-4 rounded-[6px] items-center flex-row justify-between ${
                    ride.modeOfPayment === datum.value
                      ? "bg-[#473BF01A] border border-[#473BF0]"
                      : "bg-[#FFFFFF]"
                  } `}
                  onPress={() => {
                    updateRide("modeOfPayment", datum.value);
                  }}
                >
                  <View className="flex-row items-center gap-x-2">
                    <Image
                      source={datum.image}
                      className="w-[22px] h-[22px]"
                      resizeMode="contain"
                    />
                    <Text>{datum.label}</Text>
                  </View>
                  <View
                    className={`w-[20px] h-[20px] rounded-[15px] border justify-center items-center ${
                      ride.modeOfPayment === datum.value
                        ? "border-[#473BF0]"
                        : "border-black"
                    } `}
                  >
                    {ride.modeOfPayment === datum.value ? (
                      <View className="w-[12px] h-[12px] rounded-[11px] bg-[#473BF0]" />
                    ) : null}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
        <View className="absolute bottom-[3%] left-0 right-0 px-5 bg-white">
          <Button
            buttonDisabled={!ride.modeOfPayment || isPending}
            isLoading={isPending}
            text="Continue"
            textClassName="text-white"
            buttonClassName="bg-[#473BF0] w-full mb-2"
            onClick={() => {
              request({
                dataBody: {
                  driverId: ride.driverId,
                  estimatedPrice: ride.price,
                  modeOfPayment: ride.modeOfPayment,
                  currentLocation: {
                    name: displayCurrentAddress,
                    lat: initialRegion.latitude,
                    lng: initialRegion.longitude,
                  },
                  pickup: privateRide.origin,
                  dropoff: privateRide.destination,
                  cardId: data?.cards[0]?.id,
                },
              });
            }}
          />
        </View>
      </View>
    </Modal>
  );
};

export default PrivatePaymentMode;
