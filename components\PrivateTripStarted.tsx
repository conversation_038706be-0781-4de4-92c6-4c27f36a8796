import React, { useRef, useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import { router } from "expo-router";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}

const PrivateTripStarted: React.FC<Props> = ({ step, setStep }) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const optionSheetRef = useRef<BottomSheet>(null);

  const customSnapPoints = ["25%"];
  const optionsSnapPoints = ["33%"];

  const [showOptions, setShowOptions] = useState(false);

  return (
    <>
      {showOptions ? (
        <Modal
          customSnapPoints={optionsSnapPoints}
          index={0}
          ref={optionSheetRef}
        >
          <View className="flex-1 w-full relative bg-[#FFFFFF] pt-3">
            <View className="flex-1 w-[90%] mx-auto">
              <View className="flex-row justify-between items-center">
                <Text className="text-[#151B2D] text-[20px] font-semibold">
                  Safety options
                </Text>
                <TouchableOpacity
                  activeOpacity={0.9}
                  onPress={() => setShowOptions(false)}
                >
                  <Image
                    source={require("../assets/images/close_line.png")}
                    className="h-[24px] w-[24px]"
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              </View>

              <View className="gap-y-2 mt-4">
                <View className="p-3 border border-[#473bf014] shadow-sm mt-5 flex-row items-center h-[60px] rounded-[6px]">
                  <Image
                    source={require("../assets/images/share.png")}
                    className="w-[24px] h-[24px]"
                    resizeMode="contain"
                  />
                  <Text className="ml-4 text-[#151B2D] text-[15px] font-medium">
                    Share trip details
                  </Text>
                </View>

                <View className="p-3 border border-[#473bf014] shadow-sm mt-5 flex-row items-center h-[60px] rounded-[6px]">
                  <Image
                    source={require("../assets/images/emergency.png")}
                    className="w-[24px] h-[24px]"
                    resizeMode="contain"
                  />
                  <Text className="ml-4 text-[#151B2D] text-[15px] font-medium">
                    Call emergency number
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      ) : (
        <Modal
          customSnapPoints={customSnapPoints}
          index={0}
          ref={bottomSheetRef}
        >
          <View className="flex-1 w-full relative bg-[#FFFFFF]">
            <View className="flex-1 w-[90%] mx-auto">
              <View className="flex-row justify-between items-center mt-2">
                <View className="gap-y-[6px]">
                  <Text className="text-[#151B2D] text-[20px] font-semibold">
                    Trip has started
                  </Text>
                  <View className="flex-row gap-x-3 items-center">
                    <Text className="text-[#151B2D] text-[15px]">
                      Toyota Corolla, Black
                    </Text>
                    <View className="bg-[#EAEAEA] rounded-[3px] p-[3px]">
                      <Text className="text-[#151B2D] text-[13px] font-medium">
                        ECF 256
                      </Text>
                    </View>
                  </View>
                </View>
                <View>
                  <Image
                    source={require("../assets/images/ProfilePic.png")}
                    className="h-[44px] w-[44px]"
                    resizeMode="contain"
                  />
                </View>
              </View>

              <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

              <View className="flex-row justify-between w-full px-4">
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => router.push(`/${"chat"}`)}
                  className="items-center justify-center gap-y-1"
                >
                  <Image
                    source={require("../assets/images/Chat.png")}
                    className="w-[44px] h-[44px]"
                    resizeMode="contain"
                  />
                  <Text className="text-[#151B2D] font-medium text-xs">
                    Chat
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  activeOpacity={0.9}
                  onPress={() => setShowOptions(true)}
                >
                  <View className="items-center justify-center gap-y-1">
                    <Image
                      source={require("../assets/images/Option.png")}
                      className="w-[44px] h-[44px]"
                      resizeMode="contain"
                    />

                    <Text className="text-[#151B2D] font-medium text-xs">
                      Option
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </>
  );
};

export default PrivateTripStarted;
