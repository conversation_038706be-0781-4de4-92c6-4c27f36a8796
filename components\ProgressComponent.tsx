import { View, Text } from "react-native";
import React from "react";

interface Props {
  step: number;
}

const ProgressComponent = ({ step }: Props) => {
  const stepInPercentage = (step / 3) * 100;

  return (
    <View className="absolute flex-1 justify-center items-center w-full">
      <View className="w-[100px] rounded-full">
        <View className="relative bg-[#D9D9D9] h-1 w-full">
          <View
            className="absolute bg-[#473BF0] h-1 items-center justify-center"
            style={{ width: `${stepInPercentage}%` }}
          >
            <View className="h-2 w-2 rounded-[100px] bg-[#E5850C] -right-1 absolute"></View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ProgressComponent;
