import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
} from "react-native";
import React from "react";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import RNPickerSelect from "react-native-picker-select";
import { useRide } from "@/context/RideProvider";
import CustomPickerDropdown from "@/components/CustomPickerDropdown";

interface FormData {
  allowLuggage: boolean | null;
  numberOfPassengers: string | null;
  allowSmoking: boolean | null;
  allowPets: boolean | null;
  allowBikes: boolean | null;
}

interface AnswerData {
  value: boolean;
  text: string;
}

const TripPreference = () => {
  const { updatePreference, preferences, ride, updateRide } = useRide();
  const preferenecesSelected = preferences.every(
    (preference) => preference.value !== null
  );

  console.log(preferenecesSelected);

  const answersData: AnswerData[] = [
    { value: true, text: "Yes" },
    { value: false, text: "No" },
  ];

  //Options for the number of passengers
  const options = [
    { label: "1", value: 1 },
    { label: "2", value: 2 },
    { label: "3", value: 3 },
  ];

  //This renders a radio button
  const renderOptionButtons = (desc: string, selectedValue: boolean | null) => (
    <View className="flex-row flex-wrap justify-between mt-2">
      {answersData.map((datum, index) => (
        <TouchableOpacity
          key={index}
          activeOpacity={0.9}
          className={`w-[48%] mb-4 p-4 rounded-[6px] items-center flex-row justify-between ${
            preferences.find((preference) => preference.desc === desc)
              ?.value === datum.value
              ? "bg-[#473BF01A] border border-[#473BF0]"
              : "bg-[#F6F6F6]"
          }`}
          onPress={() => updatePreference(desc, datum.value)}
        >
          <Text>{datum.text}</Text>
          <View
            className={`w-[20px] h-[20px] rounded-[15px] border justify-center items-center ${
              preferences.find((preference) => preference.desc === desc)
                ?.value === datum.value
                ? "border-[#473BF0]"
                : "border-black"
            }`}
          >
            {preferences.find((preference) => preference.desc === desc)
              ?.value === datum.value && (
              <View className="w-[12px] h-[12px] rounded-[11px] bg-[#473BF0]" />
            )}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="w-full h-full bg-white">
          <StatusBar barStyle="dark-content" backgroundColor="transparent" />
          <View className="w-[90%] h-full mx-auto mt-4 relative">
            {/* Allow Luggage */}
            <View className="mt-2">
              <Text className="text-[#4A4C50] font-medium text-[15px]">
                Allow luggage
              </Text>
              {renderOptionButtons(
                "Allow luggage",
                preferences.find((p) => p.desc === "Allow luggage")?.value ??
                  null
              )}
            </View>

            {/* No. of Passengers */}
            <View className="">
              
              <CustomPickerDropdown
                label="No. of passengers"
                items={options}
                onValueChange={(value) => updateRide("noOfPassengers", value)}
                value={Number(ride.noOfPassengers)}
              />
              
            </View>

            {/* Allow Smoking */}
            <View className="mt-4">
              <Text className="text-[#4A4C50] font-medium text-[15px]">
                Allow smoking/drinking
              </Text>
              {renderOptionButtons(
                "Allow Smoking/Drinking",
                preferences.find((p) => p.desc === "Allow Smoking/Drinking")
                  ?.value ?? null
              )}
            </View>

            {/* Allow Bikes */}
            <View>
              <Text className="text-[#4A4C50] font-medium text-[15px]">
                Allow bikes in boot
              </Text>
              {renderOptionButtons(
                "Allow Bikes",
                preferences.find((p) => p.desc === "Allow Bikes")?.value ?? null
              )}
            </View>

            {/* Allow Pets */}
            <View>
              <Text className="text-[#4A4C50] font-medium text-[15px]">
                Allow pets
              </Text>
              {renderOptionButtons(
                "Allow Pets",
                preferences.find((p) => p.desc === "Allow Pets")?.value ?? null
              )}
            </View>

            <Button
              buttonDisabled={!preferenecesSelected || !ride.noOfPassengers}
              onClick={() => router.push("/Pricing" as Href)}
              text="Next"
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] absolute bottom-[3%]"
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default TripPreference;
