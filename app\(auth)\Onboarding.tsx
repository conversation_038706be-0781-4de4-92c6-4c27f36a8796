import {
  Text,
  View,
  ImageBackground,
  Image,
  TouchableOpacity,
  Animated,
} from "react-native";
import React, { useState, useEffect } from "react";
import { Href, router } from "expo-router";

// Create an animated version of ImageBackground
const AnimatedImageBackground = Animated.createAnimatedComponent(ImageBackground);

const Onboarding = () => {
  const [fadeAnim] = useState(new Animated.Value(1)); // Initial opacity is 1
  const [imageIndex, setImageIndex] = useState(0);

  const images = [
    require("../../assets/images/Onboarding.png"),
    require("../../assets/images/Onboarding2.png"),
    require("../../assets/images/Onboarding3.png"),
  ];

  // Array for corresponding text content
  const texts = [
    { title: "Carpool to", subtitle: "your destination" },
    { title: "Affordable", subtitle: "Private ride" },
    { title: "Earn while", subtitle: "you drive" },
  ];

  // Line colors for each animation index
  const lineColors = ["#ffffff", "#cccccc", "#666666"]; // Customize colors as needed

  const animateTransition = () => {
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0, // Fade out
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1, // Fade in
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useEffect(() => {
    const interval = setInterval(() => {
      animateTransition();
      setImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 3000); // Transition every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <View className="h-full flex-1 w-full">
      {/* Animated Background Image */}
      <AnimatedImageBackground
        source={images[imageIndex]} // Dynamically change image
        className="h-full w-full"
        resizeMode="cover"
        style={{ opacity: fadeAnim }} // Apply fade animation to the image
      >
        {/* Dark Overlay */}
        <View className="absolute top-0 left-0 right-0 bottom-0 bg-black opacity-10" />

        {/* Logo Section */}
        <View className="absolute top-[5%] left-[5%]">
          <Image
            source={require("../../assets/images/CoRide.png")}
            className="w-[120px] h-[40px]"
            resizeMode="contain"
          />
        </View>

        {/* Center Text Section */}
        <Animated.View
          style={{ opacity: fadeAnim }}
          className="flex-1 items-start justify-end mb-[10%] px-5"
        >
          {/* Animated Lines */}
          <View className="flex flex-row gap-[5px] mb-4">
            {lineColors.map((color, index) => (
              <View
                key={index}
                className={`h-[6px] rounded-full`}
                style={{
                  width: index === imageIndex ? 20 : 6,
                  backgroundColor: index === imageIndex ? "#ffffff" : "#827f7f",
                  opacity: index === imageIndex ? 1 : 0.5,
                }}
              />
            ))}
          </View>

          {/* Animated Text */}
          <Text className="text-white text-5xl font-semibold text-center">
            {texts[imageIndex].title}
          </Text>
          <Text className="text-white text-5xl font-semibold text-center">
            {texts[imageIndex].subtitle}
          </Text>
        </Animated.View>

        {/* Buttons Section */}
        <View className="w-[90%] flex-row justify-between items-center mx-auto mb-[10%]">
          <TouchableOpacity
            onPress={() => router.push("/SignIn" as Href)}
            activeOpacity={0.8}
            className="flex-[1] bg-white py-3 rounded-full items-center mr-3"
          >
            <Text className="text-black text-sm font-semibold">Log in</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => router.push("/SignUp" as Href)}
            activeOpacity={0.8}
            className="flex-[1.5] bg-[#473BF0] py-3 rounded-full items-center ml-3"
          >
            <Text className="text-white text-sm font-semibold">Sign up</Text>
          </TouchableOpacity>
        </View>
      </AnimatedImageBackground>
    </View>
  );
};

export default Onboarding;
