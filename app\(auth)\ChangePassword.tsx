import {
  View,
  StatusBar,
  SafeAreaView,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
} from "react-native";
import React, { useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { router, useLocalSearchParams } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import Toast from "react-native-toast-message";
import axios from "axios";

export default function ChangePassword() {
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { token } = useLocalSearchParams();
  const tokenString = Array.isArray(token) ? token[0] : token;

  const resetPassword = async (newPassword: string, authToken: string) => {
    try {
      setIsLoading(true);
      const response = await axios.post(
        `${process.env.EXPO_PUBLIC_BASE_URL}/auth/reset-password`,
        { newPassword: newPassword },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
          timeout: 10000,
        }
      );

      Toast.show({
        type: "success",
        text1: "Password reset successful",
      });

      router.replace("/(auth)/SignIn");

      return response.data;
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          throw new Error(`${error.response.data?.message || "Bad Request"}`);
        } else if (error.response?.status === 401) {
          throw new Error("Authentication expired. Please log in again.");
        }
        Toast.show({
          type: "error",
          text1: error.response
            ? error.response.data.description || error.response.data.message
            : error.message,
        });
      }
      setIsLoading(false);
      throw new Error(`Failed to reset password: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            <AuthHeader
              header="Set a new password"
              subHeader="Create a new password to secure your account"
            />
            <View className="mt-8">
              <Input
                text="New password"
                type="password"
                placeHolder="Enter your password"
                value={confirmNewPassword}
                setValue={setConfirmNewPassword}
                showPassword={showNewPassword}
                setShowPassword={setShowNewPassword}
              />
              <View className="h-5" />
              <Input
                text="Confirm new password"
                type="password"
                placeHolder="Enter your password"
                value={newPassword}
                setValue={setNewPassword}
                showPassword={showConfirmPassword}
                setShowPassword={setShowConfirmPassword}
              />
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                buttonDisabled={!newPassword || !confirmNewPassword}
                isLoading={isLoading}
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  if (newPassword !== confirmNewPassword) {
                    Toast.show({
                      type: "error",
                      text1: "Password does not match",
                    });
                  } else resetPassword(newPassword, tokenString);
                }}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
