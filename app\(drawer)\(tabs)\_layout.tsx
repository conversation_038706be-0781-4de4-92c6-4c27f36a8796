import { TabBarIcon } from "@/components/navigation/TabBarIcon";
import { Ionicons } from "@expo/vector-icons";
import { Tabs } from "expo-router";

import React from "react";
import { Text, View, StatusBar, Image, ImageSourcePropType } from "react-native";

interface TabIconProps {
  icon?: keyof typeof Ionicons.glyphMap;
  color: string;
  name: string;
  focused: boolean;
  activeColor: string;
  source?: ImageSourcePropType;
}

const TabIcon: React.FC<TabIconProps> = ({
  icon,
  color,
  name,
  focused,
  activeColor,
  source,
}) => {
  return (
    <View className="items-center justify-center w-full">
      {/* <TabBarIcon name={icon} color={focused ? activeColor : color} /> */}
      <Image
      source={source}
      tintColor={focused ? "#473BF0" : "#787A80"}
      resizeMode="contain"
      className="w-[24px] h-[24px]"
    />
      <Text
        style={{ color: color, overflow: "hidden" }}
        className={`${focused ? "font-bold" : "font-normal"} text-[9px] mt-1`}
      >
        {name}
      </Text>
    </View>
  );
};

export default function TabLayout() {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: "#473BF0",
          tabBarInactiveTintColor: "#787A80",
          headerShown: false,
          tabBarShowLabel: false,
          tabBarStyle: {
            backgroundColor: "#FFFFFF",
            height: 80,
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
            paddingTop: 10,
            borderTopColor: "#F6F6FF",
            shadowColor: "#000000",
            elevation: 2,
          },
        }}
      >
        <Tabs.Screen
          name="Home"
          options={{
            headerShown: false,
            title: "Home",
            tabBarIcon: ({ color, focused }) => (
              <TabIcon
                source={require('../../../assets/images/HomeNew.png')}
                color={color}
                activeColor={"#473BF0"}
                name="Home"
                focused={focused}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="Trips"
          options={{
            title: "Trips",
            // headerShown: true,
            tabBarIcon: ({ color, focused }) => (
              <TabIcon
                source={require('../../../assets/images/CalenderNew.png')}
                color={color}
                activeColor={"#473BF0"}
                name="Trips"
                focused={focused}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="Profile"
          options={{
            title: "Profile",
            tabBarIcon: ({ color, focused }) => (
              <TabIcon
                source={require('../../../assets/images/ProfileNewest.png')}
                color={color}
                activeColor={"#473BF0"}
                name="Profile"
                focused={focused}
              />
            ),
          }}
        />
      </Tabs>
    </>
  );
}
