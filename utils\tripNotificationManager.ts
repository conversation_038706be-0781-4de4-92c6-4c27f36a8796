import { pushNotificationHelpers } from './pushNotificationService';
import { tripReminderHelpers, TripReminderData } from './tripReminderScheduler';

interface Trip {
  id: string;
  origin: { name: string };
  destination: { name: string };
  timestamp: string;
  driver: { id: string; firstName: string; lastName: string };
  passengers: Array<{ id: string; firstName: string; lastName: string }>;
  price?: string;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
}

/**
 * Comprehensive trip notification manager that handles all types of notifications
 * including role-based and time-based notifications
 */
class TripNotificationManager {
  
  /**
   * Initialize the notification system
   */
  async initialize(): Promise<void> {
    try {
      await pushNotificationHelpers.initialize();
      console.log('✅ Trip Notification Manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Trip Notification Manager:', error);
    }
  }

  /**
   * Schedule all trip reminders when a trip is created or booked
   * @param trip - Trip data
   * @param currentUser - Current user (to determine role)
   */
  async scheduleTripReminders(trip: Trip, currentUser: User): Promise<void> {
    try {
      const userRole = this.getUserRole(trip, currentUser);
      const tripStartTime = new Date(trip.timestamp);
      
      const tripReminderData: TripReminderData = {
        tripId: trip.id,
        userRole,
        tripDetails: {
          origin: trip.origin.name.split(',')[0],
          destination: trip.destination.name.split(',')[0],
          time: tripStartTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          driverName: `${trip.driver.firstName} ${trip.driver.lastName}`,
          passengerCount: trip.passengers.length,
        },
        tripStartTime,
      };

      await tripReminderHelpers.scheduleReminders(tripReminderData);
      console.log(`📅 Scheduled trip reminders for ${userRole}:`, trip.id);
    } catch (error) {
      console.error('❌ Failed to schedule trip reminders:', error);
    }
  }

  /**
   * Cancel trip reminders when a trip is cancelled or completed
   * @param tripId - Trip ID
   */
  async cancelTripReminders(tripId: string): Promise<void> {
    try {
      await tripReminderHelpers.cancelReminders(tripId);
      console.log('📅 Cancelled trip reminders for:', tripId);
    } catch (error) {
      console.error('❌ Failed to cancel trip reminders:', error);
    }
  }

  /**
   * Send trip started notification based on user role
   * @param trip - Trip data
   * @param currentUser - Current user
   */
  async notifyTripStarted(trip: Trip, currentUser: User): Promise<void> {
    try {
      const userRole = this.getUserRole(trip, currentUser);
      
      if (userRole === 'driver') {
        await pushNotificationHelpers.notifyTripStartedForDriver(
          trip.id, 
          trip.passengers.length
        );
      } else {
        await pushNotificationHelpers.notifyTripStartedForPassenger(
          trip.id, 
          `${trip.driver.firstName} ${trip.driver.lastName}`
        );
      }
      
      console.log(`🚗 Sent trip started notification for ${userRole}:`, trip.id);
    } catch (error) {
      console.error('❌ Failed to send trip started notification:', error);
    }
  }

  /**
   * Send trip ended notification based on user role
   * @param trip - Trip data
   * @param currentUser - Current user
   * @param earnings - Optional earnings for driver
   */
  async notifyTripEnded(trip: Trip, currentUser: User, earnings?: string): Promise<void> {
    try {
      const userRole = this.getUserRole(trip, currentUser);
      
      if (userRole === 'driver') {
        await pushNotificationHelpers.notifyTripEndedForDriver(trip.id, earnings);
      } else {
        await pushNotificationHelpers.notifyTripEndedForPassenger(
          trip.id, 
          `${trip.driver.firstName} ${trip.driver.lastName}`
        );
      }
      
      // Cancel any remaining reminders since trip is ended
      await this.cancelTripReminders(trip.id);
      
      console.log(`🎉 Sent trip ended notification for ${userRole}:`, trip.id);
    } catch (error) {
      console.error('❌ Failed to send trip ended notification:', error);
    }
  }

  /**
   * Send booking accepted notification (for passengers)
   * @param trip - Trip data
   * @param passengerUser - Passenger who got accepted
   */
  async notifyBookingAccepted(trip: Trip, passengerUser: User): Promise<void> {
    try {
      await pushNotificationHelpers.notifyBookingAccepted(
        trip.id,
        `${trip.driver.firstName} ${trip.driver.lastName}`
      );
      
      // Schedule trip reminders for the passenger
      await this.scheduleTripReminders(trip, passengerUser);
      
      console.log('✅ Sent booking accepted notification and scheduled reminders:', trip.id);
    } catch (error) {
      console.error('❌ Failed to send booking accepted notification:', error);
    }
  }

  /**
   * Send booking declined notification (for passengers)
   * @param trip - Trip data
   */
  async notifyBookingDeclined(trip: Trip): Promise<void> {
    try {
      await pushNotificationHelpers.notifyBookingDeclined(
        trip.id,
        `${trip.driver.firstName} ${trip.driver.lastName}`
      );
      
      console.log('❌ Sent booking declined notification:', trip.id);
    } catch (error) {
      console.error('❌ Failed to send booking declined notification:', error);
    }
  }

  /**
   * Send pickup confirmed notification (for drivers)
   * @param trip - Trip data
   * @param passengerName - Name of passenger who was picked up
   */
  async notifyPickupConfirmed(trip: Trip, passengerName: string): Promise<void> {
    try {
      await pushNotificationHelpers.notifyPickupConfirmed(trip.id, passengerName);
      console.log('✅ Sent pickup confirmed notification:', trip.id);
    } catch (error) {
      console.error('❌ Failed to send pickup confirmed notification:', error);
    }
  }

  /**
   * Handle trip cancellation - send notifications and cancel reminders
   * @param trip - Trip data
   * @param currentUser - User who cancelled
   * @param reason - Cancellation reason
   */
  async handleTripCancellation(trip: Trip, currentUser: User, reason?: string): Promise<void> {
    try {
      const userRole = this.getUserRole(trip, currentUser);
      
      // Cancel all scheduled reminders
      await this.cancelTripReminders(trip.id);
      
      // Send cancellation notifications to other users
      if (userRole === 'driver') {
        // Notify all passengers
        for (const passenger of trip.passengers) {
          // You might want to implement a trip cancelled notification
          console.log(`📱 Would notify passenger ${passenger.firstName} about trip cancellation`);
        }
      } else {
        // Notify driver about passenger cancellation
        console.log(`📱 Would notify driver ${trip.driver.firstName} about passenger cancellation`);
      }
      
      console.log(`🚫 Handled trip cancellation for ${userRole}:`, trip.id);
    } catch (error) {
      console.error('❌ Failed to handle trip cancellation:', error);
    }
  }

  /**
   * Determine user role in the trip
   * @param trip - Trip data
   * @param user - User to check role for
   * @returns 'driver' or 'passenger'
   */
  private getUserRole(trip: Trip, user: User): 'driver' | 'passenger' {
    return trip.driver.id === user.id ? 'driver' : 'passenger';
  }

  /**
   * Get notification status
   */
  isInitialized(): boolean {
    return pushNotificationHelpers.isInitialized();
  }

  /**
   * Get scheduled reminders for debugging
   */
  getScheduledReminders(): Map<string, string[]> {
    return tripReminderHelpers.getScheduled();
  }

  /**
   * Clear all scheduled reminders (for cleanup)
   */
  async clearAllReminders(): Promise<void> {
    await tripReminderHelpers.clearAll();
  }
}

// Export singleton instance
export const tripNotificationManager = new TripNotificationManager();

// Helper functions for easy access
export const tripNotificationHelpers = {
  initialize: () => tripNotificationManager.initialize(),
  scheduleTripReminders: (trip: Trip, currentUser: User) => 
    tripNotificationManager.scheduleTripReminders(trip, currentUser),
  cancelTripReminders: (tripId: string) => 
    tripNotificationManager.cancelTripReminders(tripId),
  notifyTripStarted: (trip: Trip, currentUser: User) => 
    tripNotificationManager.notifyTripStarted(trip, currentUser),
  notifyTripEnded: (trip: Trip, currentUser: User, earnings?: string) => 
    tripNotificationManager.notifyTripEnded(trip, currentUser, earnings),
  notifyBookingAccepted: (trip: Trip, passengerUser: User) => 
    tripNotificationManager.notifyBookingAccepted(trip, passengerUser),
  notifyBookingDeclined: (trip: Trip) => 
    tripNotificationManager.notifyBookingDeclined(trip),
  notifyPickupConfirmed: (trip: Trip, passengerName: string) => 
    tripNotificationManager.notifyPickupConfirmed(trip, passengerName),
  handleTripCancellation: (trip: Trip, currentUser: User, reason?: string) => 
    tripNotificationManager.handleTripCancellation(trip, currentUser, reason),
  isInitialized: () => tripNotificationManager.isInitialized(),
  getScheduledReminders: () => tripNotificationManager.getScheduledReminders(),
  clearAllReminders: () => tripNotificationManager.clearAllReminders(),
};

export type { Trip, User };
