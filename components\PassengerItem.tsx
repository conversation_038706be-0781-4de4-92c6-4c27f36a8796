import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface PassengerItemProps {
  item: any; // You might want to replace 'any' with a more specific type
  displayCurrentAddress: string;
  loadingRequestId: { id: string | null; type: "accept" | "reject" | null };
  handleAcceptRideRequest: (requestId: string) => void;
  handleRejectRideRequest: (requestId: string) => void;
}

const PassengerItem: React.FC<PassengerItemProps> = ({
  item,
  displayCurrentAddress,
  loadingRequestId,
  handleAcceptRideRequest,
  handleRejectRideRequest,
}) => {
  const isAcceptLoading =
    loadingRequestId.id === item.requestId && loadingRequestId.type === "accept";
  const isRejectLoading =
    loadingRequestId.id === item.requestId && loadingRequestId.type === "reject";

  const { travelData } = useTravelTimeAndDistance(
    displayCurrentAddress,
    item?.pickup?.name
  );

  return (
    <TouchableOpacity
      key={item.id} // Key prop is important for lists
      activeOpacity={0.9}
      className="mb-3 bg-[#FFFFFF] border border-[#FFFFFF] shadow-sm rounded-[6px] p-4"
    >
      <View className="flex-row w-full">
        <Image
          source={require("../assets/images/ProfilePic.png")}
          className="w-[40px] h-[40px]"
          resizeMode="contain"
        />
        <View className="ml-2 flex-1 justify-center">
          <Text className="text-[16px] font-medium text-[#151B2D]">
            {`${item?.firstName} ${item?.lastName}`}
          </Text>
          <View className="flex-row items-center mt-1.5">
            <View className="items-center">
              <Image
                source={require("../assets/images/from2.png")}
                className="w-[12px] h-[12px]"
                resizeMode="contain"
              />
              <View className="h-[12px] w-[1px] bg-[#D9D9D9]" />
              <Image
                source={require("../assets/images/to2.png")}
                className="w-[13px] h-[13px]"
                resizeMode="contain"
              />
            </View>
            <View className="flex-1 ml-1 gap-y-[8px]">
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="text-[12px] text-[#787A80]"
              >
                {item?.pickup?.name}
              </Text>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="text-[12px] text-[#787A80]"
              >
                {item?.dropoff?.name}
              </Text>
            </View>
          </View>
          <Text className="text-[#151B2D] text-[13px] font-medium mt-2">
            {travelData?.duration} away
          </Text>
        </View>
      </View>

      <View className="flex-row justify-between mt-4">
        <TouchableOpacity
          onPress={() => handleRejectRideRequest(item.requestId)}
          disabled={isRejectLoading || isAcceptLoading}
          activeOpacity={0.9}
          className="bg-[#E05859] items-center justify-center w-[48%] rounded-[100px] py-[8px] px-[10px]"
        >
          {isRejectLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text className="text-[#FFFFFF] text-[12px] font-semibold">
              Decline
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleAcceptRideRequest(item.requestId)}
          disabled={isAcceptLoading || isRejectLoading}
          activeOpacity={0.9}
          className="bg-[#473BF0] items-center justify-center w-[48%] rounded-[100px] py-[8px] px-[10px]"
        >
          {isAcceptLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text className="text-[#FFFFFF] text-[12px] font-semibold">
              Accept
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default PassengerItem; 