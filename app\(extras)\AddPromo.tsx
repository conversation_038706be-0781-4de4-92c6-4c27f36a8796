import {
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
  StatusBar,
} from "react-native";
import React, { useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const AddPromo = () => {
  const [code, setCode] = useState("");

  const { mutate: applyPromoCode, isPending } = useMutation({
    mutationFn: services.applyPromoCode,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Promo code applied!",
        text2: data?.message,
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full pt-5 mx-auto relative">
            <View className="mt-2">
              <Input
                text="Promo code"
                type="text"
                placeHolder="CO-XXXX-1234"
                value={code}
                setValue={(value) => setCode(value)}
              />

              <Text className="text-[#4A4C50] text-[15px] font-medium mt-3">
                This code will apply to your next ride
              </Text>
            </View>

            <View className="absolute bottom-[2%] w-full">
              <Button
                buttonDisabled={!code || isPending}
                isLoading={isPending}
                text="Apply"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  applyPromoCode({ dataBody: { code } });
                }}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default AddPromo;
