import { View, StatusBar } from "react-native";
import React, { useMemo, useState } from "react";
import { useLocalSearchParams } from "expo-router";

import { useUser } from "../../context/UserContext";

import TripRoute from "@/components/TripRoute";
import LookingForPassengers from "@/components/LookingForPassengers";
import NoPassengerAvailable from "@/components/NoPassengerAvailable";
import AvailablePassengers from "@/components/AvailablePassengers";
import DriverTripStarted from "@/components/DriverTripStarted";
import TripProgress from "@/components/TripProgress";
import DriverTripEnded from "@/components/DriverTripEnded";
import DriverCancelRide from "@/components/DriverCancelRide";
import PassengersDetails from "@/components/PassengersDetails";
import PassengerInfo from "@/components/PassengerInfo";

import { useQuery } from "@tanstack/react-query";
import { services } from "@/services";

import { TripRequestsResponse } from "@/utils/types";
import DriverTripMap from "@/components/DriverTripMap";
import useActiveTrip from "@/hooks/useActiveTrip";

const PostRide = () => {
  //This is for everything related to directions
  const [direction, setDirection] = useState(false);
  const [directionOrigin, setDirectionOrigin] = useState({
    latitude: 0,
    longitude: 0,
  });
  const [directionDestination, setDirectionDestination] = useState({
    latitude: 0,
    longitude: 0,
  });

  //This is for the ride flow
  const [cancelRide, setCancelRide] = useState(false);
  const [showPassengers, setShowPassengers] = useState(false);
  const [showPassenger, setShowPassenger] = useState(false);
  const [noPassengerAvailable, setNoPassengerAvailable] = useState(false);
  const [step, setStep] = useState(1);

  //The is for the ride on Driver's end
  const { initialRegion, updateRide } = useUser();

  const { trip: tripParams } = useLocalSearchParams();
  const trip = JSON.parse(decodeURIComponent(tripParams as string));

  //Get Carpool requests
  const { data, refetch } = useQuery({
    queryKey: ["requests"],
    queryFn: () => services.getCarpoolRequests("carpool", trip?.id),
  });

  //Store carpool requests in a variable and add type
  const rideRequests: TripRequestsResponse = useMemo(() => data, [data]);

  //Origin for direction is the users current location, destination would be where they're going to, also update rideId
  const setMarkerDirection = (
    latitude: number,
    longitude: number,
    id: string
  ) => {
    setDirectionOrigin({
      latitude: initialRegion.latitude,
      longitude: initialRegion.longitude,
    });
    setDirectionDestination({
      latitude: latitude,
      longitude: longitude,
    });
    setDirection(true);
    updateRide("rideId", id);
  };

  const { data: activeTrip, refetchActive } = useActiveTrip();

  const ActiveTrip = useMemo(() => activeTrip.data, [activeTrip.data]);

  return (
    <View className="h-full w-full bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="transparent" />

      <DriverTripMap
        step={step}
        markers={rideRequests}
        direction={direction}
        directionOrigin={directionOrigin}
        directionDestination={directionDestination}
        setMarkerDirection={setMarkerDirection}
        activeTrip={ActiveTrip || trip}
      />

      <TripRoute from={trip?.origin?.name} to={trip?.destination?.name} />

      {step === 1 &&
        (!noPassengerAvailable ? (
          <LookingForPassengers
            step={step}
            setStep={setStep}
            rideRequests={rideRequests}
            noPassengerAvailable={noPassengerAvailable}
            setNoPassengerAvailable={setNoPassengerAvailable}
            refetch={refetch}
            refetchActive={refetchActive}
            tripId={trip.id}
            activeTrip={ActiveTrip || trip}
          />
        ) : (
          <NoPassengerAvailable
            step={step}
            setStep={setStep}
            noPassengerAvailable={noPassengerAvailable}
            setNoPassengerAvailable={setNoPassengerAvailable}
          />
        ))}

      {step === 2 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} tripId={trip.id} />
        ) : (
          <AvailablePassengers
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            rideRequests={rideRequests}
            trip={trip}
            refetch={refetch}
            refetchActive={refetchActive}
            activeTrip={activeTrip}
          />
        ))}

      {step === 3 &&
        (cancelRide ? (
          <DriverCancelRide setCancelRide={setCancelRide} tripId={trip.id} />
        ) : showPassengers ? (
          showPassenger ? (
            <PassengerInfo
              setShowPassenger={setShowPassenger}
              trip={ActiveTrip}
            />
          ) : (
            <PassengersDetails
              setShowPassengers={setShowPassengers}
              setShowPassenger={setShowPassenger}
              trip={ActiveTrip}
            />
          )
        ) : (
          <DriverTripStarted
            step={step}
            setStep={setStep}
            setCancelRide={setCancelRide}
            setShowPassengers={setShowPassengers}
            trip={ActiveTrip}
          />
        ))}

      {step === 4 && (
        <TripProgress step={step} setStep={setStep} trip={ActiveTrip} />
      )}

      {step === 5 && (
        <DriverTripEnded
          step={step}
          setStep={setStep}
          trip={ActiveTrip}
          preferences
        />
      )}
    </View>
  );
};

export default PostRide;
