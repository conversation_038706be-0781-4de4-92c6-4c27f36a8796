import { View, Text } from "react-native";
import React from "react";
import VerificationStatus from "@/components/VerificationStatus";
import useCurrentUser from "@/hooks/useCurrentUser";

const Status = () => {
  const { data } = useCurrentUser();
  // console.log('user', data)
  return (
    <>
      {data?.verificationStatus === "IN_PROGRESS" ||
      data?.verificationStatus === "PENDING" ? (
        <VerificationStatus
          image={require("../../assets/images/Awaiting.png")}
          text="Awaiting approval"
          description="Your details have been received and will be approved in a few minutes"
        />
      ) : data?.isVerified === true ? (
        <VerificationStatus
          image={require("../../assets/images/accepted.png")}
          text="Verification successful!"
          description="Congratulations! Your identity has been approved, start earning!"
        />
      ) : (
        <VerificationStatus
          image={require("../../assets/images/failedVerification.png")}
          text="Verification failed"
          description="Your verfication failed"
        />
      )}
    </>
  );
};

export default Status;
