import { TouchableOpacity, Image } from "react-native";
import React from "react";
import { router } from "expo-router";

interface GoBackProps {
  color?: string;
}

const GoBack = ({ color = "#F6F6F6" }: GoBackProps) => {
  return (
    <TouchableOpacity
      activeOpacity={0.1}
      onPress={() => router.back()}
      className={`bg-[${color}] rounded-full p-2 items-center justify-center w-[40px] h-[40px]`}
    >
      <Image
        source={require("../assets/images/Back.png")}
        className="w-[24px] h-[24px]"
        resizeMode="contain"
      />
    </TouchableOpacity>
  );
};

export default GoBack;