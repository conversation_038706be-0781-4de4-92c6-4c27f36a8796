{"expo": {"name": "CoRide", "slug": "co<PERSON>", "version": "1.0.0", "platforms": ["ios", "android", "web"], "orientation": "portrait", "icon": "./assets/images/ios-light.png", "scheme": "com.samuelsu55.coride", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash-icon-light.png", "resizeMode": "contain", "backgroundColor": "#473BF0"}, "ios": {"newArchEnabled": true, "supportsTablet": true, "bundleIdentifier": "com.samuelsu55.coride", "buildNumber": "1.0.0"}, "android": {"config": {"googleMaps": {"apiKey": "AIzaSyDh7ZsB4Qh4NYdbsp8dSl9dYuT5AkdCs1I"}}, "ArchEnabled": true, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#473BF0"}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECORD_AUDIO"], "package": "com.samuelsu55.coride"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/ear.png"}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], "expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow CoRide to access your location to display your current location and allow the app work properly."}], ["expo-notifications", {"icon": "./assets/images/ios-light.png", "color": "#ffffff", "defaultChannel": "default", "mode": "production"}], ["@stripe/stripe-react-native", {"merchantIdentifier": "", "enableGooglePay": true}], ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.47727200055-a12d1b2lrf782s8nrtr4g8igjrupnhsf"}], "expo-font", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "03037d2f-c74c-41ac-b9fb-d3a585940e32"}}, "owner": "sleepy<PERSON><PERSON>ret<PERSON>"}}