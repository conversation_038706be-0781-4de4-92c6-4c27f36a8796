import React, { useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import Modal from "@/components/Modal";
import BottomSheet, { BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { Trip, TripRequestsResponse } from "@/utils/types";
import { services } from "@/services";
import {
  QueryObserverResult,
  RefetchOptions,
  useMutation,
} from "@tanstack/react-query";
import Toast from "react-native-toast-message";
import TripInformation from "./TripInformation";
import { useRide } from "@/context/RideProvider";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";
import { useUser } from "@/context/UserContext";
import { Realtime } from "ably";
import useGetAblyToken from "@/hooks/useGetAblyToken";
import PassengerItem from "./PassengerItem";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  rideRequests: TripRequestsResponse;
  trip: Trip;
  refetch: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
  activeTrip: Trip;
  refetchActive: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<any, Error>>;
}

const AvailablePassengers: React.FC<Props> = ({
  step,
  setStep,
  setCancelRide,
  rideRequests,
  trip,
  refetch,
  activeTrip,
  refetchActive,
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { displayCurrentAddress } = useUser();
  const { ablyToken } = useGetAblyToken();
  const ablyRef = useRef<Realtime | null>(null);

  const [loadingRequestId, setLoadingRequestId] = useState<{
    id: string | null;
    type: "accept" | "reject" | null;
  }>({ id: null, type: null });

  const customSnapPoints = ["45%", "65%", "95%"];

  const { mutate: acceptRideRequest } = useMutation({
    mutationFn: services.acceptRideRequest,
    onSuccess: (data) => {
      // Publish ride_accepted event to the passenger's channel
      if (ablyRef.current) {
        const channel = ablyRef.current.channels.get(trip.id);
        channel.publish("ride-accepted", {
          id: trip.id,
          status: "accepted",
          message: "Your ride request has been accepted"
        });
      }

      Toast.show({
        type: "success",
        text1: data.message,
      });
      setLoadingRequestId({ id: null, type: null });
      refetch();
      refetchActive();
      setTimeout(() => {
        setStep(step + 1);
      }, 1000);
    },
    onError: (error: any) => {
      setLoadingRequestId({ id: null, type: null });
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const { mutate: rejectRideRequest } = useMutation({
    mutationFn: services.rejectRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      setLoadingRequestId({ id: null, type: null });
      setTimeout(() => {
        refetch();
      }, 500);
    },
    onError: (error: any) => {
      setLoadingRequestId({ id: null, type: null });
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const handleAcceptRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "accept" });
    acceptRideRequest({ dataBody: { requestId } });
  };

  const handleRejectRideRequest = (requestId: string) => {
    setLoadingRequestId({ id: requestId, type: "reject" });
    rejectRideRequest({ dataBody: { requestId } });
  };

  const renderItem = ({ item }: { item: any }) => {
    return (
      <PassengerItem
        item={item}
        displayCurrentAddress={displayCurrentAddress}
        loadingRequestId={loadingRequestId}
        handleAcceptRideRequest={handleAcceptRideRequest}
        handleRejectRideRequest={handleRejectRideRequest}
      />
    );
  };

  // Initialize Ably connection
  React.useEffect(() => {
    if (!ablyToken?.clientId) {
      console.warn("No Ably client ID available");
      return;
    }

    try {
      // Clean up existing connection if any
      if (ablyRef.current) {
        ablyRef.current.close();
      }

      // Create new Ably instance with proper authentication
      ablyRef.current = new Realtime({
        key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
        clientId: ablyToken.clientId,
        autoConnect: true,
      });

      // Set up connection state handling
      ablyRef.current.connection.on((stateChange) => {
        console.log("Connection state changed:", stateChange.current);
      });

      return () => {
        if (ablyRef.current) {
          ablyRef.current.close();
        }
      };
    } catch (error) {
      console.error("Error setting up Ably:", error);
    }
  }, [ablyToken?.clientId]);

  // useEffect(() => {
  //   if (!rideRequests?.data) {
  //     setStep(step - 1);
  //   }
  // }, []);

  console.log(activeTrip);

  // useEffect(() => {
  //   activeTrip?.passengers?.length === activeTrip?.noOfPassengers && setStep(3);
  //   activeTrip.status === "ongoing" && setStep(4);
  // }, [activeTrip]);

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 w-[90%] mx-auto -mt-5">
        <BottomSheetFlatList
          showsVerticalScrollIndicator={false}
          data={rideRequests?.data}
          ListHeaderComponent={() => (
            <View className="gap-y-1 mb-4">
              <Text className="text-[#151B2D] font-semibold text-xl">
                Available Passengers
              </Text>
              <Text className="text-[#151B2D] text-[16px]">
                Accept or decline requests
              </Text>
            </View>
          )}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ flexGrow: 1 }}
          style={{ marginTop: 30 }}
          ListFooterComponent={() => (
            <TripInformation trip={trip} setCancelRide={setCancelRide} />
          )}
        />
      </View>
    </Modal>
  );
};

export default AvailablePassengers;
