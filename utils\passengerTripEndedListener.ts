import { Realtime, RealtimeChannel } from 'ably';
import { passengerTripEndedHelpers, TripEndedData } from '@/app/store/passengerTripEndedStore';

interface TripEndedEventData {
  tripId: string;
  driverName: string;
  driverPhoto?: string;
  origin: string;
  destination: string;
  duration?: string;
  distance?: string;
  cost?: string;
  endReason?: 'completed' | 'early' | 'cancelled' | 'issue';
  endTime: string;
  passengerId?: string; // To ensure event is for this passenger
}

class PassengerTripEndedListener {
  private ably: Realtime | null = null;
  private channels: Map<string, RealtimeChannel> = new Map();
  private currentUserId: string | null = null;
  private isInitialized: boolean = false;

  /**
   * Initialize the listener with Ably instance and user ID
   */
  initialize(ably: Realtime, userId: string): void {
    this.ably = ably;
    this.currentUserId = userId;
    this.isInitialized = true;
    console.log('🎧 PassengerTripEndedListener initialized for user:', userId);
  }

  /**
   * Start listening for trip-ended events on a specific trip
   */
  startListening(tripId: string): void {
    if (!this.isInitialized || !this.ably || !this.currentUserId) {
      console.warn('⚠️ PassengerTripEndedListener not initialized');
      return;
    }

    // Don't create duplicate listeners
    if (this.channels.has(tripId)) {
      console.log('🎧 Already listening for trip-ended events on trip:', tripId);
      return;
    }

    try {
      const channelName = `trip-${tripId}`;
      const channel = this.ably.channels.get(channelName);
      
      // Subscribe to trip-ended events
      channel.subscribe('trip-ended', (message) => {
        this.handleTripEndedEvent(message.data as TripEndedEventData);
      });

      // Store channel reference for cleanup
      this.channels.set(tripId, channel);
      
      console.log('🎧 Started listening for trip-ended events on:', channelName);
    } catch (error) {
      console.error('❌ Failed to start listening for trip-ended events:', error);
    }
  }

  /**
   * Stop listening for trip-ended events on a specific trip
   */
  stopListening(tripId: string): void {
    const channel = this.channels.get(tripId);
    if (channel) {
      try {
        channel.unsubscribe('trip-ended');
        this.channels.delete(tripId);
        console.log('🔇 Stopped listening for trip-ended events on trip:', tripId);
      } catch (error) {
        console.error('❌ Failed to stop listening for trip-ended events:', error);
      }
    }
  }

  /**
   * Stop listening on all trips
   */
  stopAllListening(): void {
    this.channels.forEach((channel, tripId) => {
      try {
        channel.unsubscribe('trip-ended');
        console.log('🔇 Stopped listening for trip-ended events on trip:', tripId);
      } catch (error) {
        console.error('❌ Failed to stop listening for trip-ended events on trip:', tripId, error);
      }
    });
    this.channels.clear();
    console.log('🔇 Stopped all trip-ended event listeners');
  }

  /**
   * Handle incoming trip-ended event
   */
  private handleTripEndedEvent(eventData: TripEndedEventData): void {
    try {
      console.log('📨 Received trip-ended event:', eventData);

      // Validate event data
      if (!eventData.tripId || !eventData.driverName) {
        console.warn('⚠️ Invalid trip-ended event data:', eventData);
        return;
      }

      // Check if this event is for the current user (if passengerId is provided)
      if (eventData.passengerId && eventData.passengerId !== this.currentUserId) {
        console.log('🚫 Trip-ended event not for current user, ignoring');
        return;
      }

      // Convert event data to modal data format
      const modalData: TripEndedData = {
        tripId: eventData.tripId,
        driverName: eventData.driverName,
        driverPhoto: eventData.driverPhoto,
        origin: eventData.origin || 'Unknown',
        destination: eventData.destination || 'Unknown',
        duration: eventData.duration,
        distance: eventData.distance,
        cost: eventData.cost,
        endReason: eventData.endReason || 'completed',
        endTime: eventData.endTime || new Date().toISOString(),
      };

      // Show the modal
      passengerTripEndedHelpers.showModal(modalData);
      
      // Stop listening for this trip since it's ended
      this.stopListening(eventData.tripId);
      
      console.log('✅ Displayed passenger trip-ended modal for trip:', eventData.tripId);
    } catch (error) {
      console.error('❌ Failed to handle trip-ended event:', error);
    }
  }

  /**
   * Get current listening status
   */
  getListeningTrips(): string[] {
    return Array.from(this.channels.keys());
  }

  /**
   * Check if listening for a specific trip
   */
  isListeningForTrip(tripId: string): boolean {
    return this.channels.has(tripId);
  }

  /**
   * Get initialization status
   */
  isReady(): boolean {
    return this.isInitialized && this.ably !== null && this.currentUserId !== null;
  }

  /**
   * Cleanup all resources
   */
  cleanup(): void {
    this.stopAllListening();
    this.ably = null;
    this.currentUserId = null;
    this.isInitialized = false;
    console.log('🧹 PassengerTripEndedListener cleaned up');
  }
}

// Export singleton instance
export const passengerTripEndedListener = new PassengerTripEndedListener();

// Helper functions for easy access
export const passengerTripEndedListenerHelpers = {
  initialize: (ably: Realtime, userId: string) => 
    passengerTripEndedListener.initialize(ably, userId),
  startListening: (tripId: string) => 
    passengerTripEndedListener.startListening(tripId),
  stopListening: (tripId: string) => 
    passengerTripEndedListener.stopListening(tripId),
  stopAll: () => 
    passengerTripEndedListener.stopAllListening(),
  getListening: () => 
    passengerTripEndedListener.getListeningTrips(),
  isListening: (tripId: string) => 
    passengerTripEndedListener.isListeningForTrip(tripId),
  isReady: () => 
    passengerTripEndedListener.isReady(),
  cleanup: () => 
    passengerTripEndedListener.cleanup(),
};

export type { TripEndedEventData };
