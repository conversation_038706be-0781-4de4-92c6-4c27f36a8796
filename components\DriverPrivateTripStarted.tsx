import React, { useRef, useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { router } from "expo-router";
import { Trip } from "@/utils/types";
import { format } from "date-fns/format";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  setCancelRide: React.Dispatch<React.SetStateAction<boolean>>;
  setShowPassengers: React.Dispatch<React.SetStateAction<boolean>>;
  trip: Trip;
}

const PrivateDriverTripStarted: React.FC<Props> = ({
  step,
  setStep,
  setCancelRide,
  setShowPassengers,
  trip,
}) => {
  const [confirmedPickups, setConfirmedPickups] = useState<number>(0);

  const { mutate: confirmPickUp, isPending } = useMutation({
    mutationFn: services.confirmPickUp,
    onSuccess: (data) => {
      // Increment confirmed pickups counter
      const newConfirmedCount = confirmedPickups + 1;
      setConfirmedPickups(newConfirmedCount);
      setStep(step + 1)
      Toast.show({
        type: "success",
        text1: data.message,
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["30%", "45%", "75%", "80%"];

  console.log("Private Trip", trip);

  return (
    <Modal customSnapPoints={customSnapPoints} index={0} ref={bottomSheetRef}>
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto">
          <View className="flex-row justify-between items-center mt-2">
            <View className="gap-y-[6px]">
              <Text className="text-[#151B2D] text-[20px] font-semibold">
                Trip has started ({confirmedPickups}/{trip?.noOfPassengers})
              </Text>
              <View className="flex-row gap-x-3 items-center">
                <Text className="text-[#151B2D] text-[15px]">
                  Picking up {trip?.passengers[0]?.firstName} in 2 minutes
                </Text>
              </View>
            </View>
            <TouchableOpacity
              className="flex-row -space-x-3"
              activeOpacity={0.7}
              onPress={() => setShowPassengers(true)}
            >
              {trip?.passengers?.map((passenger, index) => (
                <View key={index} style={{ marginLeft: index === 0 ? 0 : -20 }}>
                  <Image
                    source={require("../assets/images/ProfilePic.png")}
                    className="h-[44px] w-[44px]"
                    resizeMode="contain"
                  />
                  {index < confirmedPickups && (
                    <View className="absolute bottom-0 right-0">
                      <Image
                        source={require("../assets/images/accepted.png")}
                        className="h-[16px] w-[16px]"
                        resizeMode="contain"
                      />
                    </View>
                  )}
                </View>
              ))}
            </TouchableOpacity>
          </View>

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View className="flex-row justify-between w-full px-4">
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => router.push(`/${"chat"}`)}
              className="items-center justify-center gap-y-1"
            >
              <Image
                source={require("../assets/images/Chat.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">Chat</Text>
            </TouchableOpacity>

            <View className="items-center justify-center gap-y-1">
              <Image
                source={require("../assets/images/Call.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] font-medium text-xs">Call</Text>
            </View>

            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => setCancelRide(true)}
            >
              <View className="items-center justify-center gap-y-1">
                <Image
                  source={require("../assets/images/CancelRide.png")}
                  className="w-[44px] h-[44px]"
                  resizeMode="contain"
                />
                <Text className="text-[#151B2D] font-medium text-xs">
                  Cancel trip
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          <View className="w-full h-[1px] bg-[#E3E3E3] my-4"></View>

          <View className="mt-2 mb-2 flex-1">
            <Text className="text-[#151B2D] font-semibold text-[20px] mb-2">
              Trip Information
            </Text>
            <View className="bg-[#FFFFFF] px-2 py-4 rounded-[6px]">
              <View className="flex-row w-full justify-between">
                <Text className="text-[#151B2D] text-[17px] font-semibold">
                  My route
                </Text>
              </View>

              <View className="w-full flex-row items-center mt-3">
                <View className="items-center">
                  <Image
                    source={require("../assets/images/from2.png")}
                    className="w-[12px] h-[12px]"
                    resizeMode="contain"
                  />
                  <View className="h-[21px] w-[1px] bg-[#D9D9D9]" />
                  <Image
                    source={require("../assets/images/to2.png")}
                    className="w-[13px] h-[13px]"
                    resizeMode="contain"
                  />
                </View>
                <View className="flex-1 gap-y-[18px] ml-3">
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.origin?.name}
                  </Text>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.destination?.name}
                  </Text>
                </View>
              </View>

              <View className="w-full h-[1px] bg-[#E3E3E3] my-3"></View>

              <View>
                <View className="flex-row w-full justify-between mb-2">
                  <Text className="text-[#151B2D] text-[17px] font-semibold">
                    Ride details
                  </Text>
                </View>

                <View className="flex-row flex-wrap justify-between gap-y-2">
                  {/* <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Date and time:
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {format(trip?.timestamp, "MMMM dd, yyyy hh:mm a")}
                    </Text>
                  </View> */}
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      No. of passengers
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {confirmedPickups}/{trip?.noOfPassengers}
                    </Text>
                  </View>
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Price per seat
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {trip?.pricePerSeat}
                    </Text>
                  </View>
                </View>

                {/* <View className="mt-2">
                  <Text className="text-[#787A80] text-[14px] font-medium">
                    Preferences
                  </Text>
                  <View className="flex-row flex-wrap justify-between">
                    {trip.preferences.map((preference, index) => (
                      <View
                        key={index}
                        className="py-1 justify-between mb-2 flex-row items-center gap-x-[4px]"
                      >
                        {preference?.value === true ? (
                          <Image
                            source={require("../assets/images/accepted.png")}
                            className="h-[12px] w-[12px]"
                            resizeMode="contain"
                          />
                        ) : (
                          <Image
                            source={require("../assets/images/cancel.png")}
                            className="h-[12px] w-[12px]"
                            resizeMode="contain"
                          />
                        )}
                        <Text className="text-[#4A4C50] text-[11px] font-medium">
                          {preference.desc}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View> */}
              </View>
            </View>
          </View>

          <View className="absolute bottom-[4%] w-full">
            <Button
              text={`Confirm pick up for ${trip?.passengers[0]?.firstName}`}
              isLoading={isPending}
              buttonDisabled={isPending}
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] w-full"
              onClick={() =>
                confirmPickUp({
                  dataBody: { requestId: trip?.passengers[0]?.requestId },
                })
              }
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PrivateDriverTripStarted;
