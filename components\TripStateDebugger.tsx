import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTripStore } from '@/app/store/tripStore';
import { useNotificationStore } from '@/app/store/notificationStore';
import { notificationHelpers } from '@/utils/notificationHelpers';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';

const TripStateDebugger: React.FC = () => {
  const {
    currentTripId,
    tripStarted,
    requestStatuses,
    setTripStarted,
    resetTripState
  } = useTripStore();

  const {
    currentUserRole,
    currentUserId,
    activeModals,
    showModal
  } = useNotificationStore();

  return (
    <View className="bg-yellow-100 p-4 m-4 rounded-lg">
      <Text className="font-bold text-lg mb-2">Trip State Debug</Text>
      <Text className="text-sm mb-1">Current Trip ID: {currentTripId || 'None'}</Text>
      <Text className="text-sm mb-1">Trip Started: {tripStarted ? 'Yes' : 'No'}</Text>
      <Text className="text-sm mb-1">User Role: {currentUserRole || 'None'}</Text>
      <Text className="text-sm mb-1">User ID: {currentUserId || 'None'}</Text>
      <Text className="text-sm mb-1">Active Modals: {activeModals.length}</Text>
      <Text className="text-sm mb-2">Request Statuses: {Object.keys(requestStatuses).length} items</Text>

      <View className="flex-row flex-wrap gap-2 mb-2">
        <TouchableOpacity
          onPress={() => setTripStarted(!tripStarted)}
          className="bg-blue-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">Toggle Trip</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={resetTripState}
          className="bg-red-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">Reset State</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (currentTripId) {
              realtimeNotificationHelpers.notifyTripStarted(currentTripId, 'Test Driver');
            }
          }}
          className="bg-green-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">RT Trip Start</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (currentTripId) {
              realtimeNotificationHelpers.notifyPickupConfirmed(currentTripId, 'Test Passenger');
            }
          }}
          className="bg-purple-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">RT Pickup</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (currentTripId) {
              realtimeNotificationHelpers.sendCustomMessage(
                currentTripId,
                'Test Message',
                'This is a realtime test message!',
                currentUserRole === 'driver' ? 'passenger' : 'driver',
                'Test User'
              );
            }
          }}
          className="bg-pink-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">RT Message</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            showModal({
              type: 'custom',
              title: 'Test Modal',
              content: 'This is a test modal for the current user role',
              targetRole: currentUserRole || 'all',
              tripId: currentTripId,
              buttons: [{ text: 'OK', action: 'close', style: 'primary' }]
            });
          }}
          className="bg-orange-500 px-3 py-1 rounded"
        >
          <Text className="text-white text-xs">Test Modal</Text>
        </TouchableOpacity>
      </View>
      
      {Object.keys(requestStatuses).length > 0 && (
        <View className="mt-2">
          <Text className="font-semibold text-xs">Request Statuses:</Text>
          {Object.entries(requestStatuses).map(([requestId, status]) => (
            <Text key={requestId} className="text-xs">
              {requestId.slice(-8)}: {status.status} {status.pickupConfirmed ? '✓' : '✗'}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

export default TripStateDebugger;
