import { getTravelTimeAndDistance } from "@/utils/helpers";
import { useEffect, useState } from "react";

interface Details {
  destination: string;
  distance: string;
  duration: string;
  origin: string;
  durationInSeconds: number;
}

const useTravelTimeAndDistance = (origin: string, destination: string) => {
  const [travelData, setTravelData] = useState<Details | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTravelData = async () => {
      try {
        const result = await getTravelTimeAndDistance(
          origin || "",
          destination || ""
        );
        setTravelData(result);
      } catch (err) {
        console.error("Error fetching travel data:", err);
        setError("Failed to retrieve travel data");
      }
    };

    fetchTravelData();
  }, [origin, destination]);

  return { travelData };
};

export default useTravelTimeAndDistance;
