import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router, Href } from 'expo-router';
import useCurrentUser from "@/hooks/useCurrentUser";
import TripHistory from '@/components/TripHistory';
import ActiveRides from '@/components/ActiveRides';
import useActiveTrip from '@/hooks/useActiveTrip';
import ActiveTabTrips from '@/components/ActiveTabTrips';
import RideRequestsTab from '@/components/RideRequestsTab';

const Trips = () => {
  const [activeTab, setActiveTab] = useState('active');
  const [historyTab, setHistoryTab] = useState<'taken' | 'given'>('taken');
  const { data: user } = useCurrentUser();
  const { hasActiveTrip } = useActiveTrip();

  return (
    <View className="flex-1 bg-[#E9E9E9] px-4 py-4">
      {/* Header */}
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold flex-shrink">Trips</Text>
        <View className="flex-row flex-shrink-0 gap-2">
          <TouchableOpacity
            onPress={() => router.push("/(ride)/FindRideScreen" as Href)}
            className="bg-white rounded-full px-3 py-2 flex-row items-center min-w-0">
            <Image
              source={require('../../../assets/images/loc.png')}
              className="w-4 h-4 mr-1"
            />
            <Text className="text-xs text-gray-800 flex-shrink" numberOfLines={1}>Find ride</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() =>
              user?.isVerified
                ? router.push("/(ride)/OfferRide" as Href)
                : router.push("/(verifyDriver)/Verification" as Href)
            }
            className="bg-white rounded-full px-3 py-2 flex-row items-center min-w-0">
            <Image
              source={require('../../../assets/images/drive.png')}
              className="w-4 h-4 mr-1"
            />
            <Text className="text-xs text-gray-800 flex-shrink" numberOfLines={1}>Post trip</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tabs */}
      <View className="flex-row justify-center mb-4 bg-[#d8d6d6] mx-2 rounded-full px-1 py-1">
        {['active', 'requests', 'history'].map((tab) => (
          <TouchableOpacity
            key={tab}
            className={`flex-1 h-10 rounded-full justify-center items-center mx-0.5 ${
              activeTab === tab ? 'bg-white' : 'bg-[#d8d6d6]'
            }`}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              className={`text-center text-[15px] font-semibold ${
                activeTab === tab ? 'text-black' : 'text-gray-600'
              }`}
              numberOfLines={1}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>



      {/* Tab Content */}
      <View className="flex-1">
        {activeTab === 'active' && (
          <View className="flex-1">
            {hasActiveTrip ? (
              <ActiveTabTrips />
            ) : (
              <View className="flex-1 items-center justify-center rounded-lg p-6 bg-white">
                <Text className="text-lg font-medium mb-2">No active trips</Text>
                <Text className="text-gray-500 mb-4">
                  Post or request a ride to get started
                </Text>
                <Image
                  source={require('../../../assets/images/homme.png')}
                  className="w-24 h-24"
                />
              </View>
            )}
          </View>
        )}

        {activeTab === 'requests' && (
          <RideRequestsTab />
        )}

        {activeTab === 'history' && (
          <View className="flex-1 bg-white rounded-lg">
            <View className="flex-row justify-center mb-4 bg-[#f5f5f5] mx-3 mt-4 rounded-full py-1">
              <TouchableOpacity
                className={`flex-1 h-10 rounded-full justify-center items-center mx-0.5 ${
                  historyTab === 'taken' ? 'bg-[#473BF0]' : 'bg-transparent'
                }`}
                onPress={() => setHistoryTab('taken')}
              >
                <Text
                  className={`text-center font-medium text-sm ${
                    historyTab === 'taken' ? 'text-white' : 'text-gray-600'
                  }`}
                  numberOfLines={1}
                >
                  Rides Taken
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                className={`flex-1 h-10 rounded-full justify-center items-center mx-0.5 ${
                  historyTab === 'given' ? 'bg-[#473BF0]' : 'bg-transparent'
                }`}
                onPress={() => setHistoryTab('given')}
              >
                <Text
                  className={`text-center font-medium text-sm ${
                    historyTab === 'given' ? 'text-white' : 'text-gray-600'
                  }`}
                  numberOfLines={1}
                >
                  Trips Given
                </Text>
              </TouchableOpacity>
            </View>
            <TripHistory role={historyTab} />
          </View>
        )}
      </View>
    </View>
  );
};

export default Trips;
