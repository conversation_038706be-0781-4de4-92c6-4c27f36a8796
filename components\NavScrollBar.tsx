import { Text, FlatList, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import React from "react";

type Props = {
  scrollData: { name: string }[];
};

const NavScrollBar = ({ scrollData }: Props) => {
  return (
    <FlatList
      data={scrollData}
      renderItem={({ item }) => (
        <TouchableOpacity
          // onPress={() => router.push("/category_view")}
          className="rounded-[30px] bg-[#F9F9F9] px-3 mr-3 py-2.5"
        >
          <Text className="text-[#333333] font-tensor text-base font-normal">
            {item.name}
          </Text>
        </TouchableOpacity>
      )}
      keyExtractor={(item) => item.name}
      horizontal
    />
  );
};

export default NavScrollBar;
