import {
  DarkTheme,
  De<PERSON><PERSON><PERSON>heme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import { UserProvider } from "../context/UserContext";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { RideProvider } from "@/context/RideProvider";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/providers";
import Toast, {
  BaseToast,
  BaseToastProps,
  ErrorToast,
} from "react-native-toast-message";
import GlobalModalManager from "@/components/GlobalModalManager";
import { pushNotificationHelpers } from "@/utils/pushNotificationService";
import PassengerTripEndedModal from "@/components/PassengerTripEndedModal";

// import { GoogleSignin } from "@react-native-google-signin/google-signin";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({});

  const toastConfig = {
    success: (props: JSX.IntrinsicAttributes & BaseToastProps) => (
      <BaseToast
        {...props}
        style={{
          borderLeftColor: "#4BAF4F",
          backgroundColor: "#fff",
          zIndex: 9999,
        }}
        text1Style={{
          fontWeight: "400",
          color: "#000",
        }}
        text2Style={{ fontSize: 12 }}
      />
    ),
    error: (props: JSX.IntrinsicAttributes & BaseToastProps) => (
      <ErrorToast
        {...props}
        style={{
          borderLeftColor: "red",
          backgroundColor: "#fff",
        }}
        text1Style={{
          fontWeight: "400",
          color: "#000",
        }}
        text2Style={{
          fontSize: 12,
        }}
      />
    ),
  };
  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Initialize push notifications
  useEffect(() => {
    const initializePushNotifications = async () => {
      try {
        const token = await pushNotificationHelpers.initialize();
        if (token) {
          console.log('✅ Push notifications initialized successfully');
        } else {
          console.warn('⚠️ Push notifications not available on this device');
        }
      } catch (error) {
        console.error('❌ Failed to initialize push notifications:', error);
      }
    };

    initializePushNotifications();
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
        <GestureHandlerRootView style={{ flex: 1, pointerEvents: "box-none" }}>
          <UserProvider>
            <RideProvider>
              <Stack>
                <Stack.Screen
                  name="index"
                  options={{ headerShown: false, title: "Onboarding" }}
                />
                <Stack.Screen
                  name="(auth)"
                  options={{ headerShown: false, title: "Auth" }}
                />
                {/* <Stack.Screen
                  name="(tabs)"
                  options={{ headerShown: false, title: "Tabs" }}
                /> */}
                <Stack.Screen
                  name="(ride)"
                  options={{ headerShown: false, title: "Find a ride" }}
                />
                <Stack.Screen
                  name="(verifyDriver)"
                  options={{ headerShown: false, title: "Verification" }}
                />
                <Stack.Screen
                  name="(drawer)"
                  options={{ headerShown: false, title: "Verification" }}
                />
                <Stack.Screen
                  name="(extras)"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="(profile)"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="+not-found" />
              </Stack>

              <Toast config={toastConfig} />
              <GlobalModalManager />

              {/* Global Passenger Trip Ended Modal */}
              <PassengerTripEndedModal />
            </RideProvider>
          </UserProvider>
        </GestureHandlerRootView>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
