import GoBack from "@/components/GoBack";
import { Stack } from "expo-router";

export default function VerificationLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="Verification"
        options={{
          title: "",
          headerShown: true,
          headerLeft: () => <GoBack />,
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="Upload"
        options={{ headerShown: false, title: "Upload" }}
      />
      <Stack.Screen
        name="Status"
        options={{ headerShown: false, title: "Status" }}
      />
    </Stack>
  );
}


