import { View, Text, Image, FlatList, TouchableOpacity } from "react-native";
import React, { useCallback, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { services } from "@/services";
import dayjs from "dayjs";
import { trips } from "@/libs/data";

type Transaction = {
  ref: string;
  type: string;
  amount: string;
  userId: string;
  status: string;
  tripId: string;
  createdAt: string;
  updatedAt: string;
}[];

const TransactionHistory = () => {
  const [durationFilter, setDurationFilter] = useState(false);
  const [statusFilter, setStatusFilter] = useState(false);

  // Filters options for duration and status
  const durationOptions = [
    { label: "Last 30 Days", value: 30 },
    { label: "Last 60 Days", value: 60 },
    { label: "Custom", value: 10 },
  ];
  const statusOptions = [
    { label: "All", value: "all" },
    { label: "Success", value: "SUCCESS" },
    { label: "Pending", value: "PENDING" },
    { label: "Failed", value: "FAILED" },
    { label: "Reversed", value: "REVERSED" },
  ];

  const [selectedDuration, setSelectedDuration] = useState(durationOptions[0]);
  const [selectedStatus, setSelectedStatus] = useState(statusOptions[0]);

  const { data, refetch, isLoading } = useQuery({
    queryKey: ["transactions", selectedDuration, selectedStatus],
    queryFn: () => services.getTransactions(),
    staleTime: 5000,
  });

  const Transactions = data?.data;

  // Filter transactions based on selected duration and status
  const filteredTransactions = Transactions?.filter((transaction: any) => {
    const transactionDate = dayjs(transaction.createdAt);
    const currentDate = dayjs();
    const isWithinDuration =
      currentDate.diff(transactionDate, "day") <= selectedDuration.value;
    const isStatusMatch =
      selectedStatus.value === "all" ||
      transaction.status === selectedStatus.value;

    return isWithinDuration && isStatusMatch;
  });

  return (
    <View className="h-full w-full bg-white">
      <View className="w-[90%] mx-auto mt-2">
        {/* Filters */}
        <View className="flex-row justify-between">
          {/* Duration Filter */}
          <View
            className={`flex-row items-center p-[4px] space-x-2 ${
              durationFilter ? "bg-[#F2F2F2] rounded-[6px]" : "bg-[white]"
            }`}
          >
            <Text className="text-[13px] font-medium text-[#787A80]">
              Duration:
            </Text>
            <TouchableOpacity
              onPress={() => {
                setStatusFilter(false);
                setDurationFilter(!durationFilter);
              }}
              activeOpacity={0.8}
              className="flex-row items-center"
            >
              <Text className="text-[#151B2D] text-[13px] font-medium">
                {selectedDuration.label}
              </Text>
              <Image
                source={require("../../assets/images/down_small_line.png")}
                className="w-[18px] h-[18px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>

          {/* Status Filter */}
          <View
            className={`flex-row items-center p-[4px] space-x-2 ${
              statusFilter ? "bg-[#F2F2F2] rounded-[6px]" : "bg-[white]"
            }`}
          >
            <Text className="text-[13px] font-medium text-[#787A80]">
              Transaction status:
            </Text>
            <TouchableOpacity
              onPress={() => {
                setStatusFilter(!statusFilter);
                setDurationFilter(false);
              }}
              activeOpacity={0.8}
              className="flex-row items-center"
            >
              <Text className="text-[#151B2D] text-[13px] font-medium">
                {selectedStatus.label}
              </Text>
              <Image
                source={require("../../assets/images/down_small_line.png")}
                className="w-[18px] h-[18px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Duration Options */}
        {durationFilter && (
          <View className="h-[70px] mt-2 justify-center">
            {durationOptions.map((dur, i) => (
              <TouchableOpacity
                key={i}
                activeOpacity={0.8}
                onPress={() => {
                  setSelectedDuration(dur);
                  setDurationFilter(false);
                }}
                className="flex-row items-center justify-between mt-2"
              >
                <Text
                  className={`font-normal ${
                    dur.label === selectedDuration.label
                      ? "text-[#473BF0]"
                      : "text-[#151B2D]"
                  }`}
                >
                  {dur.label}
                </Text>
                {dur.label === selectedDuration.label && (
                  <Image
                    source={require("../../assets/images/tick.png")}
                    className="h-[18px] w-[18px]"
                    resizeMode="contain"
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Status Options */}
        {statusFilter && (
          <View className="h-[100px] mt-4 justify-center">
            {statusOptions.map((stat, i) => (
              <TouchableOpacity
                key={i}
                activeOpacity={0.8}
                onPress={() => {
                  setSelectedStatus(stat);
                  setStatusFilter(false);
                }}
                className="flex-row items-center justify-between mt-2"
              >
                <Text
                  className={`font-normal ${
                    stat.label === selectedStatus.label
                      ? "text-[#473BF0]"
                      : "text-[#151B2D]"
                  }`}
                >
                  {stat.label}
                </Text>
                {stat.label === selectedStatus.label && (
                  <Image
                    source={require("../../assets/images/tick.png")}
                    className="h-[18px] w-[18px]"
                    resizeMode="contain"
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Transactions List */}
        <FlatList
          data={filteredTransactions}
          renderItem={({ item }) => (
            <View className="mb-8">
              <View className="flex-row justify-between items-center">
                <View className="flex-row items-center space-x-3">
                  <Image
                    source={require("../../assets/images/outward.png")}
                    className="w-[36px] h-[36px]"
                    resizeMode="contain"
                  />
                  <View>
                    <Text className="text-[#151B2D] text-[13px]">
                      {item.type}
                    </Text>
                    <Text className="text-[12px] text-[#656565]">
                      {dayjs(item.createdAt).format("MMMM D, YYYY")}
                    </Text>
                  </View>
                </View>
                <View>
                  <Text className="text-[#151B2D] text-[13px]">
                    {item.amount}
                  </Text>
                </View>
              </View>
            </View>
          )}
          keyExtractor={(item) => item.ref}
          ListEmptyComponent={() => (
            <View className="items-center justify-center h-[70vh]">
              <Text>No Transactions Found</Text>
            </View>
          )}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          style={{ marginTop: 30 }}
        />
      </View>
    </View>
  );
};

export default TransactionHistory;
