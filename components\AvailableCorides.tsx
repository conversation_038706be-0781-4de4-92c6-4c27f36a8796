import React, { useEffect } from "react";
import { View, Text, TouchableOpacity, Image, SafeAreaView, ScrollView } from "react-native";
import { router } from "expo-router";
import Button from "./Button";
import { RideShare, Trip } from "@/utils/types";
import { extractTimeFromISO } from "@/utils/helpers";
import { useUser } from "@/context/UserContext";
import useCurrentUser from "@/hooks/useCurrentUser";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  availableCoride: RideShare[];
  setMarkerDirection: (
    latitude: number,
    longitude: number,
    id: string,
    price: number
  ) => void;
  trip: Trip | null;
  setTrip: React.Dispatch<React.SetStateAction<Trip | null>>;
}

const AvailableCorides: React.FC<Props> = ({
  step,
  setStep,
  availableCoride,
  setMarkerDirection,
  trip,
  setTrip,
}) => {
  const {
    ride: selectedRide,
    displayCurrentAddress,
  } = useUser();

  console.log("AvailableCorides rendered with:", {
    step,
    availableCorideLength: availableCoride?.length,
    displayCurrentAddress,
  });

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const { data: user } = useCurrentUser();

  useEffect(() => {
    const passengerExists = trip?.passengers?.some(
      (passenger) => passenger?.id === user?.id
    );

    if (trip?.status === "pending" && passengerExists) {
      setStep(4);
    } else if (trip?.status === "pending" && !passengerExists) {
      setStep(3);
    } else if (trip?.status === "ongoing") {
      setStep(5);
    }
  }, [trip, user]);

  const travelDataList = availableCoride?.map((ride) =>
    useTravelTimeAndDistance(displayCurrentAddress || "", ride?.origin?.name || "")
  );

  const formatDate = (timestamp: string): string => {
    const date = new Date(timestamp);
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    
    const dayOfWeek = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];
    
    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12;
    
    return `${dayOfWeek}, ${day} ${month} at ${hours}:${minutes} ${ampm}`;
  };

  const handleContinue = () => {
    const selectedTrip = availableCoride?.find(ride => ride.id === selectedRide.rideId);
    if (selectedTrip) {
      const tripData = {
        ...selectedTrip,
        status: "pending",
        passengers: [],
        userId: user?.id,
        mode: "carpool" as const
      };
      setStep(step + 1);
      setTrip(tripData);
    }
  };

  if (!availableCoride || availableCoride.length === 0) {
    console.log("No rides available to display");
    return null;
  }


  return (
    <SafeAreaView className="flex-1 bg-[#F4F4F4]">
      <View className="flex-1">
        <View className="px-5 pt-4">
          <View className="flex-row items-center mb-7">
            <TouchableOpacity onPress={handleGoBack} className="">
              <Image
                source={require("../assets/images/Back.png")}
                className="w-6 h-6"
                resizeMode="contain"
              />
            </TouchableOpacity>
            <View className="flex items-center justify-center flex-1">
              <View className="flex-row items-center">
                <Text 
                  className="text-center font-semibold text-base" 
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{ width: 100 }}
                >
                  {selectedRide.pickup.name.split(",")[0]}
                </Text>
                <Text className="text-center font-semibold text-base mx-1"> - </Text>
                <Text 
                  className="text-center font-semibold text-base" 
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={{ width: 100 }}
                >
                  {selectedRide.dropoff.name.split(",")[0]}
                </Text>
              </View>
            </View>

          </View>
          <View className="gap-y-1 mb-4">
            <Text className="text-[#151B2D] font-semibold text-xl">
              Available CoRides
            </Text>
          </View>
        </View>

        <ScrollView showsVerticalScrollIndicator={false} className="flex-1 px-5">
          {availableCoride.map((item, index) => {
            const travelData = travelDataList[index]?.travelData;
            return (
              <View
                key={item.id}
                className={`p-4 mb-3 rounded-[6px] bg-white shadow-sm`}
              >
                <View>
                  <View className="flex-row justify-between items-center">
                    <View className="flex-row items-center">
                      <Image
                        source={require("../assets/images/ProfilePic.png")}
                        className="h-[50px] w-[50px] rounded-full mr-3"
                        resizeMode="cover"
                      />
                      <View>
                        <Text className="text-[#151B2D] font-semibold text-[16px]">
                          {`${item.driver.firstName} ${item.driver.lastName}`}
                        </Text>
                        <Text className="text-[#787A80] text-[13px]">
                          {`${item.driver.carDetails.make} ${item.driver.carDetails.model}, ${item.driver.carDetails.colour}`}
                        </Text>
                      </View>
                    </View>
                    <View className="items-end">
                      <Text className="text-[#473BF0] font-semibold text-[18px]">
                        N{item.pricePerSeat}
                      </Text>
                      <Text className="text-xs text-[#4A4C50]">{item.noOfPassengers - (item.passengers.length)} Seat left</Text>
                    </View>
                  </View>
                  <View className="my-3 w-full h-[1px] bg-[#F0F0F0]" />
                  <View className="">
                  <View>
                      <Text className="text-[14px] font-semibold">
                            {formatDate(item.timestamp)}
                      </Text>
                  </View>
                  <View className="flex-row items-center mt-2">
                     <View className="items-center">
                       <Image source={require("../assets/images/radioNew.png")} className="h-[10px] w-[10px]" />
                       <View className="bg-[#B3B3B3] h-[14px] w-[1.5px] my-[1px]" />
                       <Image source={require("../assets/images/blueLocationNew.png")} className="h-[12px] w-[12px]" />
                     </View>
                     <View className="flex-1 ml-2">
                       <View className="flex-row items-center">
                         <Text className="flex-shrink-0 font-semibold text-sm">
                           {item.origin.name.split(",")[0]},
                         </Text>
                         <Text className="text-[#787A80] font-normal text-sm flex-1" numberOfLines={1} ellipsizeMode="tail">
                           {item.origin.name.split(",").slice(1).join(",")}
                         </Text>
                       </View>
                       <View className="flex-row items-center mt-2">
                         <Text className="flex-shrink-0 font-semibold text-sm">
                           {item.destination.name.split(",")[0]},
                         </Text>
                         <Text className="text-[#787A80] font-normal text-sm flex-1" numberOfLines={1} ellipsizeMode="tail">
                           {item.destination.name.split(",").slice(1).join(",")}
                         </Text>
                       </View>
                     </View>
                   </View>

                   <View className="my-3 w-full h-[1px] bg-[#F0F0F0]" />

                   <View className="flex-row items-center justify-between">
                      <View className="flex-row space-x-[11px]">
                          {item.preferences.map((preference, index) => (
                              <View key={index} className='flex flex-row justify-between items-center'>
                              <View className='flex-row items-center'>
                                  {preference.desc === "Allow luggage" ? ( preference.value === true ? (
                                  <Image
                                      source={require("../assets/images/luggageNew.png")}
                                      className="h-[26px] w-[26px]"
                                      resizeMode="contain"
                                      tintColor={preference.value ? "#34A853" : "#787A80"}
                                  /> ) : (
                                    <Image
                                      source={require("../assets/images/luggageNew.png")}
                                      className="h-[26px] w-[26px]"
                                      resizeMode="contain"
                                      tintColor={preference.value ? "#34A853" : "#787A80"}
                                  /> 
                                  )
                                  ) : (
                                  preference.desc === "Allow Smoking/Drinking" ? (preference.value === true ? (
                                    <View className="bg-[#34A8531A] p-[5px] rounded-full">
                                      <Image
                                        source={require("../assets/images/smokingOutlineNew.png")}
                                        className="h-[18px] w-[18px]"
                                        resizeMode="contain"
                                        tintColor={preference.value ? "#34A853" : "#787A80"}
                                      />
                                  </View>) : (
                                      <View className="bg-[#787A801A] p-[5px] rounded-full">
                                      <Image
                                        source={require("../assets/images/smokingOutlineNew.png")}
                                        className="h-[18px] w-[18px]"
                                        resizeMode="contain"
                                        tintColor={preference.value ? "#34A853" : "#787A80"}
                                      />
                                  </View>
                                    )
                                  ) : (
                                      preference.desc === "Allow Pets" ? (
                                        preference.value === true ? (
                                          <View className="bg-[#34A8531A] p-[5px] rounded-full">
                                            <Image
                                              source={require("../assets/images/petOutlineNew.png")}
                                              className="h-[18px] w-[18px]"
                                              resizeMode="contain"
                                              tintColor={preference.value ? "#34A853" : "#787A80"}
                                            />
                                        </View> ) : (
                                            <View className="bg-[#787A801A] p-[5px] rounded-full">
                                            <Image
                                              source={require("../assets/images/petOutlineNew.png")}
                                              className="h-[18px] w-[18px]"
                                              resizeMode="contain"
                                              tintColor={preference.value ? "#34A853" : "#787A80"}
                                            />
                                        </View>
                                          )
                                      ) : (preference.value === true ? (
                                        <View className="bg-[#34A8531A] p-[5px] rounded-full">
                                            <Image
                                              source={require("../assets/images/bikeOutlineNew.png")}
                                              className="h-[18px] w-[18px]"
                                              resizeMode="contain"
                                              tintColor={preference.value ? "#34A853" : "#787A80"}
                                            />
                                        </View>
                                         ) : (
                                          <View className="bg-[#787A801A] p-[5px] rounded-full">
                                            <Image
                                              source={require("../assets/images/bikeOutlineNew.png")}
                                              className="h-[18px] w-[18px]"
                                              resizeMode="contain"
                                              tintColor={preference.value ? "#34A853" : "#787A80"}
                                            />
                                        </View>
                                        )
                                      )
                                  )
                                  )}
                              </View>
                              
                            </View>
                          ))}
                      </View>

                      <Button
                        text="Book now"
                        textClassName="text-white"
                        width="w-[116px]"
                        height="h-[30px]"
                        buttonClassName="bg-[#151B2D] w-full mb-2"
                        onClick={() => {
                          setMarkerDirection(
                            item.origin.lat,
                            item.origin.lng,
                            item.id,
                            item.pricePerSeat
                          );
                          const tripData = {
                            ...item,
                            status: "pending",
                            passengers: [],
                            userId: user?.id,
                            mode: "carpool" as const
                          };
                          setStep(step + 1);
                          setTrip(tripData);
                        }}
                      />
                   </View>





                  </View>
                </View>
              </View>
            );
          })}
        </ScrollView>

        {/* <View className="px-5 pb-5 bg-white">
          <Button
            buttonDisabled={!selectedRide.rideId}
            text="Continue"
            textClassName="text-white"
            buttonClassName="bg-[#473BF0] w-full mb-2"
            onClick={handleContinue}
          />
        </View> */}
      </View>
    </SafeAreaView>
  );
};

export default AvailableCorides;
