export const trips = [
  {
    id: "1",
    type: "given",
    date: "Today",
    trips: [
      {
        id: "10",
        driver: "<PERSON>",
        tripDate: "20 Mar",
        carColor: "Red",
        time: "10:00 AM",
        destination: "Downtown",
        price: "$25",
      },
      {
        id: "11",
        driver: "<PERSON>",
        tripDate: "21 Mar",
        carColor: "<PERSON>",
        time: "3:30 PM",
        destination: "Airport",
        price: "$45",
      },
    ],
  },
  {
    id: "2",
    date: "Yesterday",
    type: "given",
    trips: [
      {
        id: "12",
        driver: "<PERSON>",
        tripDate: "20 Jun",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
    ],
  },
  {
    id: "3",
    date: "13/09/2024",
    type: "given",
    trips: [
      {
        id: "13",
        driver: "<PERSON>",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "14",
        driver: "<PERSON>",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "15",
        driver: "<PERSON>",
        tripDate: "20 Mar",
        car<PERSON>olor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
    ],
  },
  {
    id: "4",
    date: "13/09/2024",
    type: "given",
    trips: [
      {
        id: "16",
        driver: "<PERSON> <PERSON>",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "17",
        driver: "Alice Johnson",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "18",
        driver: "Alice Johnson",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
    ],
  },
  {
    id: "5",
    date: "13/09/2024",
    type: "given",
    trips: [
      {
        id: "19",
        driver: "Alice Johnson",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "20",
        driver: "Alice Johnson",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
      {
        id: "21",
        driver: "Alice Johnson",
        tripDate: "20 Mar",
        carColor: "Green",
        time: "9:15 AM",
        destination: "Uptown",
        price: "$30",
      },
    ],
  },
  {
    id: "6",
    type: "taken",
    date: "Today",
    trips: [
      {
        id: "22",
        driver: "Bob Wilson",
        tripDate: "Sedan",
        carColor: "Black",
        time: "2:00 PM",
        destination: "Shopping Mall",
        price: "$20",
      },
    ],
  },
  {
    id: "7",
    type: "taken",
    date: "Yesterday",
    trips: [
      {
        id: "23",
        driver: "Carol Brown",
        tripDate: "SUV",
        carColor: "Silver",
        time: "11:30 AM",
        destination: "Beach",
        price: "$35",
      },
    ],
  },
];

export const availablePassengers = [
  {
    id: "0",
    profile: require("../assets/images/ProfilePic.png"),
    name: "Mariah Carey",
    from: "Plot 1230, Oka akoko street, Garki",
    to: "No 43, Adagez cresent ,Wuse 2",
    distance: "2 mins away",
  },
  {
    id: "1",
    profile: require("../assets/images/ProfilePic.png"),
    name: "Mariah Emily",
    from: "Plot 1230, Oka akoko street, Garki",
    to: "No 43, Adagez cresent ,Wuse 2",
    distance: "2 mins away",
  },
  {
    id: "2",
    profile: require("../assets/images/ProfilePic.png"),
    name: "Mariah Bill",
    from: "Plot 1230, Oka akoko street, Garki",
    to: "No 43, Adagez cresent ,Wuse 2",
    distance: "2 mins away",
  },
];

export const chats = [
  {
    __typename: "Chat",
    createdAt: "2024-09-27T10:40:47.766Z",
    id: "9b5b07ba-6752-473a-8041-74e99ec013cb",
    message: "booking request recieved",
    senderAgency: {
      __typename: "Agency",
      agencyName: "Fidelity Agency",
    },
    senderUser: null,
  },
  {
    __typename: "Chat",
    createdAt: "2024-09-27T10:41:04.546Z",
    id: "f9b186dd-63ad-4f74-93d4-073fbcbb483f",
    message: "ok, thank you",
    senderAgency: null,
    senderUser: {
      __typename: "User",
      firstName: "Janitor",
      lastName: "Pope",
    },
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T13:07:12.577Z",
    id: "62086e6e-345c-4c84-ae9e-819d396bb242",
    message: "Hello",
    senderAgency: null,
    senderUser: "senderUser",
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T13:21:59.010Z",
    id: "dbd8a778-25e6-4353-83ac-35fa2c57d2ba",
    message: "Hello",
    senderAgency: "senderUser",
    senderUser: null,
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T13:21:59.368Z",
    id: "0bc09601-3799-4b95-b534-935da6dd7e41",
    message: "Yes?",
    senderAgency: null,
    senderUser: null,
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T13:29:36.093Z",
    id: "dc9e4727-4347-439b-82a6-940e5b9b9ae4",
    message: "Test",
    senderAgency: null,
    senderUser: "senderUser",
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T13:29:46.364Z",
    id: "ee496f6a-1647-46e4-9cac-e0591275fb65",
    message: "That's ok",
    senderAgency: "senderUser",
    senderUser: null,
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T14:10:23.066Z",
    id: "378efba7-e14f-4471-9c4f-fd84cddc06d7",
    message: "Test",
    senderAgency: "senderUser",
    senderUser: null,
  },
  {
    __typename: "Chat",
    createdAt: "2024-10-04T14:10:35.191Z",
    id: "4d9f6a91-91f6-4952-94ea-8d5feb7c5c35",
    message: "Test",
    senderAgency: null,
    senderUser: "senderUser",
  },
];

export const transactionHistory = [
  {
    id: "1",
    date: "20/09/2024",
    trips: [
      {
        service: "Carpool ride",
        type: "outward",
        period: "20 Mar, 2024 at 11:31 AM",
        money: "-2,000",
        status: "Successful",
      },
      {
        service: "Payment from carpool trip",
        type: "inward",
        period: "20 Mar, 2024 at 11:31 AM",
        money: "+5,000",
        status: "Successful",
      },
    ],
  },
  {
    id: "2",
    date: "25/09/2024",
    trips: [
      {
        service: "Carpool ride",
        type: "outward",
        period: "25 Mar, 2024 at 10:15 AM",
        money: "-3,500",
        status: "Pending",
      },
      {
        service: "Payment from carpool trip",
        type: "inward",
        period: "25 Mar, 2024 at 10:15 AM",
        money: "+6,500",
        status: "Successful",
      },
    ],
  },
  {
    id: "3",
    date: "28/09/2024",
    trips: [
      {
        service: "Fuel purchase",
        type: "outward",
        period: "28 Mar, 2024 at 03:45 PM",
        money: "-4,000",
        status: "Failed",
      },
    ],
  },
  {
    id: "4",
    date: "30/09/2024",
    trips: [
      {
        service: "Payment from carpool trip",
        type: "inward",
        period: "30 Mar, 2024 at 02:10 PM",
        money: "+7,500",
        status: "Successful",
      },
      {
        service: "Carpool ride",
        type: "outward",
        period: "30 Mar, 2024 at 02:10 PM",
        money: "-2,500",
        status: "Successful",
      },
    ],
  },
  {
    id: "5",
    date: "02/10/2024",
    trips: [
      {
        service: "Car repair",
        type: "outward",
        period: "02 Oct, 2024 at 09:25 AM",
        money: "-15,000",
        status: "Reversed",
      },
    ],
  },
  {
    id: "6",
    date: "03/10/2024",
    trips: [
      {
        service: "Payment from carpool trip",
        type: "inward",
        period: "03 Oct, 2024 at 04:30 PM",
        money: "+8,200",
        status: "Failed",
      },
    ],
  },
  {
    id: "7",
    date: "04/10/2024",
    trips: [
      {
        service: "Parking fee",
        type: "outward",
        period: "04 Oct, 2024 at 11:00 AM",
        money: "-1,000",
        status: "Successful",
      },
      {
        service: "Carpool ride",
        type: "outward",
        period: "04 Oct, 2024 at 01:30 PM",
        money: "-3,200",
        status: "Successful",
      },
    ],
  },
  {
    id: "8",
    date: "05/10/2024",
    trips: [
      {
        service: "Payment from carpool trip",
        type: "inward",
        period: "05 Oct, 2024 at 10:45 AM",
        money: "+6,000",
        status: "Successful",
      },
    ],
  },
];
