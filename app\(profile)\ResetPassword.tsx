import {
  View,
  Text,
  StatusBar,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Image,
  TouchableOpacity,
} from "react-native";
import React, { useCallback, useRef, useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { router, useLocalSearchParams } from "expo-router";
import Toast from "react-native-toast-message";
import BottomSheet, { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import axios from "axios";

export default function ResetUserPassword() {
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");

  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [showModal, setShowModal] = useState(false);

  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["28%"];

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        {...props}
      />
    ),
    []
  );

  const handleClosePress = () => bottomSheetRef.current?.close();
  const handleOpenPress = () => bottomSheetRef.current?.expand();

  const { token } = useLocalSearchParams();
  const tokenString = Array.isArray(token) ? token[0] : token;
  const [isLoading, setIsLoading] = useState(false);

  const resetPassword = async (newPassword: string, authToken: string) => {
    try {
      setIsLoading(true);
      const response = await axios.post(
        `${process.env.EXPO_PUBLIC_BASE_URL}/auth/reset-password`,
        { newPassword: newPassword },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
          timeout: 10000,
        }
      );

      setShowModal(true);
      handleOpenPress();

      return response.data;
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          throw new Error(`${error.response.data?.message || "Bad Request"}`);
        } else if (error.response?.status === 401) {
          throw new Error("Authentication expired. Please log in again.");
        }
        Toast.show({
          type: "error",
          text1: error.response
            ? error.response.data.description || error.response.data.message
            : error.message,
        });
      }
      setIsLoading(false);
      throw new Error(`Failed to reset password: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            <View className="mt-4">
              <Input
                text="New password"
                type="password"
                placeHolder="New password"
                value={confirmNewPassword}
                setValue={setConfirmNewPassword}
                showPassword={showNewPassword}
                setShowPassword={setShowNewPassword}
              />
              <View className="h-4" />
              <Input
                text="Confirm new password"
                type="password"
                placeHolder="New password"
                value={newPassword}
                setValue={setNewPassword}
                showPassword={showConfirmPassword}
                setShowPassword={setShowConfirmPassword}
              />
            </View>

            <View className="absolute bottom-7 w-full">
              <Button
                buttonDisabled={
                  isLoading || !newPassword || !confirmNewPassword
                }
                isLoading={isLoading}
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-[#fff]"
                onClick={() => {
                  if (newPassword !== confirmNewPassword) {
                    Toast.show({
                      type: "error",
                      text1: "Password does not match",
                    });
                  } else resetPassword(newPassword, tokenString);
                }}
              />
            </View>
          </View>
          {showModal && (
            <BottomSheet
              ref={bottomSheetRef}
              index={0}
              snapPoints={customSnapPoints}
              enablePanDownToClose={true}
              containerStyle={{ zIndex: 100 }}
              handleIndicatorStyle={{ backgroundColor: "#fff" }}
              backgroundStyle={{ backgroundColor: "#fff" }}
              backdropComponent={renderBackdrop}
            >
              <View className="w-full h-full flex-1 relative">
                <TouchableOpacity
                  onPress={() => handleClosePress()}
                  className="z-20"
                >
                  <Image
                    source={require("../../assets/images/close_line.png")}
                    className="w-[24px] h-[24px] absolute right-8"
                    resizeMode="contain"
                  />
                </TouchableOpacity>
                <View className="w-full h-4"></View>
                <View className="w-[90%] mt-4 mx-auto gap-y-3 h-full flex-1 items-center">
                  <Image
                    source={require("../../assets/images/TripEnded.png")}
                    className="w-[50px] h-[50px]"
                    resizeMode="contain"
                  />
                  <Text className="my-4 text-[#151B2D] text-[20px] font-medium">
                    Password reset successfully
                  </Text>
                  <View className="w-full mt-3">
                    <Button
                      text="Book ride"
                      buttonClassName="bg-[#EDEDED]"
                      textClassName="text-[#151B2D]"
                      onClick={() => {
                        router.replace("/(ride)/BookRide");
                      }}
                    />
                  </View>
                </View>
              </View>
            </BottomSheet>
          )}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
