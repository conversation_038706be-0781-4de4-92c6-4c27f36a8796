/**
 * TRIP RATING SERVICE - USAGE EXAMPLES
 * 
 * This demonstrates how to use the submitTripRating service that matches your API endpoint
 */

import { services } from '../services';
import { useMutation } from '@tanstack/react-query';

/**
 * API Endpoint: {BASE}/carpool/{trip-id}/rate
 * Method: POST
 * Request Body: { "rating": 3, "review": "optional review text" }
 * Response: { "status": true, "message": "trip rated successfully" }
 */

// Example 1: Rate trip with just a rating (minimum required)
export const rateTrip_OnlyRating = async () => {
  try {
    const response = await services.submitTripRating({
      dataBody: {
        tripId: "trip_123",
        rating: 4, // 1-5 stars
      }
    });
    
    console.log('✅ Trip rated successfully:', response);
    // Expected response: { status: true, message: "trip rated successfully" }
    
    return response;
  } catch (error) {
    console.error('❌ Failed to rate trip:', error);
    throw error;
  }
};

// Example 2: Rate trip with rating and review text
export const rateTrip_WithReview = async () => {
  try {
    const response = await services.submitTripRating({
      dataBody: {
        tripId: "trip_123",
        rating: 5, // 1-5 stars
        review: "Great driver! Very punctual and friendly. Would definitely ride again." // Optional
      }
    });
    
    console.log('✅ Trip rated with review:', response);
    return response;
  } catch (error) {
    console.error('❌ Failed to rate trip with review:', error);
    throw error;
  }
};

// Example 3: Using the original rateTrip method (backward compatibility)
export const rateTrip_OriginalMethod = async () => {
  try {
    const response = await services.rateTrip({
      dataBody: {
        rating: 3,
        review: "Average experience" // Now supports review too
      },
      tripId: "trip_123"
    });
    
    console.log('✅ Trip rated using original method:', response);
    return response;
  } catch (error) {
    console.error('❌ Failed to rate trip (original method):', error);
    throw error;
  }
};

// Example 4: How it's used in PassengerTripEndedModal
export const rateTrip_FromModal = async (tripId: string, rating: number, reviewText: string) => {
  try {
    const response = await services.submitTripRating({
      dataBody: {
        tripId,
        rating,
        review: reviewText.trim() || undefined, // Only include review if not empty
      }
    });
    
    console.log('✅ Trip rated from modal:', response);
    return response;
  } catch (error) {
    console.error('❌ Failed to rate trip from modal:', error);
    throw error;
  }
};

// Example 5: Error handling with different scenarios
export const rateTrip_ErrorHandling = async () => {
  try {
    // Invalid rating (should be 1-5)
    await services.submitTripRating({
      dataBody: {
        tripId: "trip_123",
        rating: 0, // Invalid rating
      }
    });
  } catch (error: any) {
    console.error('❌ Invalid rating error:', error?.response?.data?.message);
  }

  try {
    // Non-existent trip
    await services.submitTripRating({
      dataBody: {
        tripId: "non_existent_trip",
        rating: 5,
      }
    });
  } catch (error: any) {
    console.error('❌ Trip not found error:', error?.response?.data?.message);
  }

  try {
    // Already rated trip
    await services.submitTripRating({
      dataBody: {
        tripId: "already_rated_trip",
        rating: 4,
      }
    });
  } catch (error: any) {
    console.error('❌ Already rated error:', error?.response?.data?.message);
  }
};

// Example 6: Integration with React component (like in PassengerTripEndedModal)
export const useInReactComponent = () => {
  const { mutate: submitRating, isPending: isSubmittingRating } = useMutation({
    mutationFn: services.submitTripRating,
    onSuccess: (data) => {
      console.log('✅ Rating submitted successfully:', data);
      // Show success toast
      // Close modal
      // Navigate to home
    },
    onError: (error: any) => {
      console.error('❌ Failed to submit rating:', error);
      // Show error toast
    },
  });

  const handleSubmitRating = (tripId: string, rating: number, review?: string) => {
    if (rating === 0) {
      console.error('❌ Please select a rating');
      return;
    }

    submitRating({
      dataBody: {
        tripId,
        rating,
        review: review?.trim() || undefined,
      }
    });
  };

  return { handleSubmitRating, isSubmittingRating };
};

/**
 * SERVICE DETAILS:
 * 
 * 1. submitTripRating() - New method that matches the modal's expected interface
 *    - Takes: { dataBody: { tripId, rating, review? } }
 *    - Calls: POST {BASE}/carpool/{tripId}/rate
 *    - Returns: { status: true, message: "trip rated successfully" }
 * 
 * 2. rateTrip() - Original method, now enhanced with review support
 *    - Takes: { dataBody: { rating, review? }, tripId }
 *    - Same endpoint and functionality
 *    - Maintained for backward compatibility
 * 
 * 3. Both methods support:
 *    - Required: rating (number 1-5)
 *    - Optional: review (string)
 *    - Automatic authentication via interceptors
 *    - Error handling with proper error messages
 * 
 * 4. The PassengerTripEndedModal now works perfectly with this service!
 */

// Re-export for convenience
export { services } from '../services';
