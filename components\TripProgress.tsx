import { View, Text, Animated, StyleSheet, Image } from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { router } from "expo-router";
import Button from "./Button";
import Modal from "./Modal";
import BottomSheet from "@gorhom/bottom-sheet";
import { format } from "date-fns/format";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { Trip } from "@/utils/types";
import { Realtime } from "ably";
import useTravelTimeAndDistance from "@/hooks/useTravelTimeAndDistance";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  trip: Trip;
}

const TripProgress: React.FC<Props> = ({ step, setStep, trip }) => {
  const { travelData } = useTravelTimeAndDistance(
    trip?.origin?.name,
    trip?.destination?.name
  );
  const ably = new Realtime({
    key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
    autoConnect: true,
  });

  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["20%", "40%", "70%"];
  const progressAnimation = useRef(new Animated.Value(0)).current;
  const [showConfirmPayment, setShowConfirmPayment] = useState(false);
  const [showEndTrip, setShowEndTrip] = useState(false);
  const [remainingTime, setRemainingTime] = useState<number | undefined>(0);

  const { mutate: confirmPayment, isPending: confirmPaymentPending } =
    useMutation({
      mutationFn: services.confirmPayment,
      onSuccess: (data) => {
        Toast.show({
          type: "success",
          text1: data?.message,
        });
        setShowConfirmPayment(false);
      },
      onError: (error: any) => {
        Toast.show({
          type: "error",
          text1: error.response
            ? error.response.data.description || error.response.data.message
            : error.message,
        });
      },
    });

  const { mutate: endTrip, isPending: endTripPending } = useMutation({
    mutationFn: services.endTrip,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message,
      });
      setTimeout(() => {
        setStep(step + 1);
      }, 1000);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  useEffect(() => {
    if (travelData?.durationInSeconds) {
      setRemainingTime(travelData.durationInSeconds * 1000);
      startTrip(travelData.durationInSeconds * 1000);
    }
  }, [travelData]);

  useEffect(() => {
    const interval = setInterval(() => {
      setRemainingTime((prev) => Math.max(0, prev! - 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Subscribe to ride channel
    const channel = ably.channels.get(trip?.id);

    // Handle payment confirmation
    channel.subscribe("confirm-payment", (message) => {
      if (message.data) {
        setShowConfirmPayment(true);
      }
    });

    // Cleanup on unmount
    return () => {
      channel.unsubscribe();
      channel.detach();
    };
  }, [trip]);

  const startTrip = (duration: number) => {
    Animated.timing(progressAnimation, {
      toValue: 100,
      duration: duration,
      useNativeDriver: false,
    }).start(({ finished }) => {
      if (finished) {
        setShowEndTrip(true);
      }
    });
  };

  const handleConfirmPayment = () => {
    confirmPayment({
      dataBody: {
        tripId: trip?.id,
        passengerId: trip.passengers[0]?.id, // Assuming first passenger
      },
    });
  };

  const handleEndTrip = () => {
    endTrip({ dataBody: { tripId: trip?.id } });
  };

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    return `${minutes} mins`;
  };

  return (
    <Modal
      handleGoBack={() => (step === 1 ? router.back() : setStep(step - 1))}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="w-full relative bg-[#FFFFFF] flex-1">
        <View className="w-[90%] mx-auto py-2">
          <Text className="text-[#151B2D] font-semibold text-xl">
            {showEndTrip
              ? "You've arrived at your destination"
              : `Arriving destination in ${formatTime(
                  remainingTime as number
                )}`}
          </Text>

          <Text className="w-full text-[#4A4C50] text-[15px] mt-2">
            {trip.destination.name}
          </Text>

          <View className="w-full my-5">
            <View className="bg-[#D9D9D9] h-[2px] relative">
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: progressAnimation.interpolate({
                      inputRange: [0, 100],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                ]}
              />
            </View>
          </View>

          {showConfirmPayment && (
            <Button
              isLoading={confirmPaymentPending}
              buttonDisabled={confirmPaymentPending}
              text="Confirm payment"
              buttonClassName="bg-[#473BF0] w-full mb-2"
              textClassName="text-white"
              onClick={handleConfirmPayment}
            />
          )}

          {showEndTrip && (
            <Button
              isLoading={endTripPending}
              buttonDisabled={endTripPending}
              text="End Trip"
              buttonClassName="bg-[#473BF0] w-full mb-2"
              textClassName="text-white"
              onClick={handleEndTrip}
            />
          )}

          <View className="my-2">
            <Text className="text-[#151B2D] font-semibold text-[20px]">
              Trip Information
            </Text>
            <View className="bg-[#FFFFFF] p-4 rounded-[6px]">
              <View className="flex-row w-full justify-between">
                <Text className="text-[#151B2D] text-[17px] font-semibold">
                  My route
                </Text>
              </View>

              <View className="w-full flex-row items-center mt-3">
                <View className="items-center">
                  <Image
                    source={require("../assets/images/from2.png")}
                    className="w-[12px] h-[12px]"
                    resizeMode="contain"
                  />
                  <View className="h-[20px] w-[1px] bg-[#D9D9D9]" />
                  <Image
                    source={require("../assets/images/to2.png")}
                    className="w-[13px] h-[13px]"
                    resizeMode="contain"
                  />
                </View>
                <View className="flex-1 gap-y-[18px] ml-2">
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.origin?.name}
                  </Text>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="text-[12px] text-[#151B2D] font-semibold"
                  >
                    {trip?.destination?.name}
                  </Text>
                </View>
              </View>

              <View className="w-full h-[1px] bg-[#E3E3E3] my-6"></View>

              <View className="">
                <View className="flex-row w-full justify-between mb-2">
                  <Text className="text-[#151B2D] text-[17px] font-semibold">
                    Ride details
                  </Text>
                </View>

                <View className="flex-row flex-wrap justify-between gap-y-2">
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Date and time:
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {format(trip?.timestamp, "MMMM dd, yyyy hh:mm a")}
                    </Text>
                  </View>
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      No. of passengers
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {trip?.passengers?.length}/{trip?.noOfPassengers}
                    </Text>
                  </View>
                  <View className="gap-y-1">
                    <Text className="text-[14px] text-[#787A80] font-medium">
                      Price per seat
                    </Text>
                    <Text className="text-[12px] text-[#151B2D] font-medium">
                      {trip?.pricePerSeat}
                    </Text>
                  </View>
                </View>

                <View className="mt-2">
                  <Text className="text-[#787A80] text-[14px] font-medium">
                    Preferences
                  </Text>
                  <View className="flex-row flex-wrap justify-between">
                    {trip.preferences.map((preference, index) => (
                      <View
                        key={index}
                        className="py-1 justify-between mb-2 flex-row items-center gap-x-[4px]"
                      >
                        {preference?.value === true ? (
                          <Image
                            source={require("../assets/images/accepted.png")}
                            className="h-[12px] w-[12px]"
                            resizeMode="contain"
                          />
                        ) : (
                          <Image
                            source={require("../assets/images/cancel.png")}
                            className="h-[12px] w-[12px]"
                            resizeMode="contain"
                          />
                        )}
                        <Text className="text-[#4A4C50] text-[11px] font-medium">
                          {preference.desc}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    backgroundColor: "#473BF0",
    height: 2,
    position: "absolute",
    bottom: 0,
  },
});

export default TripProgress;
