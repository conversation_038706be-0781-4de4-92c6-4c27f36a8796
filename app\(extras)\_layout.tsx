import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import GoBack from "@/components/GoBack";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function ExtrasLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="Promo"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="AddPromo"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "Enter promo code",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="Wallet"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="TransactionHistory"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "Transaction history",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="[rideId]"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "Ride details",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="ridesTaken/[rideId]"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "Ride details",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="NewCard"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          title: "New card",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="AddAddress"
        options={{
          presentation: "transparentModal",
          headerShown: true,
          title: "Add new address",
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
        }}
      />
      <Stack.Screen
        name="Withdraw"
        options={{
          headerShown: true,
          title: "Enter amount",
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
        }}
      />
      <Stack.Screen
        name="Account"
        options={{
          headerShown: true,
          title: "Account number",
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
        }}
      />
    </Stack>
  );
}
