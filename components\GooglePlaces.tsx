import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import React, { SetStateAction, useRef, useEffect } from "react";
import { GooglePlacesAutocomplete, GooglePlacesAutocompleteRef } from "react-native-google-places-autocomplete";
import { Ionicons } from "@expo/vector-icons";
import { useRide } from "@/context/RideProvider";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";

interface Props {
  placeholderText: string;
  defaultValue?: string; // Add this prop
  isFocused: boolean;
  setIsFocused: React.Dispatch<SetStateAction<boolean>>;
  leftFocusedImage: any;
  ObjKey: string;
  onPlaceSelected: (location: {
    name: string;
    lat: number;
    lng: number;
  }) => void;
}

const GooglePlaces = ({
  placeholderText,
  defaultValue = "", // Add default value prop
  isFocused,
  setIsFocused,
  leftFocusedImage,
  ObjKey,
  onPlaceSelected,
}: Props) => {
  console.log(uuidv4());
  const { setDetails } = useRide();
  const placesRef = useRef<GooglePlacesAutocompleteRef>(null);

  // Set the default value when component mounts or defaultValue changes
  useEffect(() => {
    if (defaultValue && placesRef.current) {
      placesRef.current.setAddressText(defaultValue);
    }
  }, [defaultValue]);

  const renderLeftButton = () => (
    <View className="justify-center h-full pl-2 bg-white">
      <Image
        source={
          isFocused ? require("../assets/images/search.png") : leftFocusedImage
        }
        className="w-[18px] h-[18px]"
        resizeMode="contain"
      />
    </View>
  );

  const renderRightButton = () => (
    <View className="flex-row items-center h-full pr-2 bg-white">
      {isFocused ? (
        <>
          <TouchableOpacity onPress={() => placesRef.current?.clear()}>
            <Image
              source={require("../assets/images/close.png")}
              className="w-[20px] h-[20px] mr-2"
              resizeMode="contain"
            />
          </TouchableOpacity>
          <Image
            source={require("../assets/images/map.png")}
            className="w-[28px] h-[28px]"
            resizeMode="contain"
          />
        </>
      ) : (
        <Image
          source={require("../assets/images/send.png")}
          className="w-[22px] h-[22px]"
          resizeMode="contain"
        />
      )}
    </View>
  );

  return (
    <View className="mt-1">
      <GooglePlacesAutocomplete
        placeholder={placeholderText}
        nearbyPlacesAPI="GooglePlacesSearch"
        debounce={1000}
        ref={placesRef}
        enableHighAccuracyLocation
        // filterReverseGeocodingByTypes={[
        //   "locality",
        //   "administrative_area_level_3",
        // ]}
        listLoaderComponent={
          <View className="py-5">
            <ActivityIndicator size="small" color="#000" />
          </View>
        }
        onPress={(data, details) => {
          setDetails(data.description, ObjKey);
          // console.log("data", data);
          if (details?.geometry?.location) {
            onPlaceSelected({
              name: data.description,
              lat: details.geometry.location.lat,
              lng: details.geometry.location.lng,
            });
            console.log("data", details.geometry.location.lat);
          }
        }}
        styles={{
          container: {
            flex: 0,
          },
          textInputContainer: {
            borderWidth: 1,
            borderColor: isFocused ? "#473BF0" : "#F6F6F6",
            borderRadius: 6,
            backgroundColor: isFocused ? "#F6F6F6" : "#FFF",
            height: 48,
            overflow: "hidden",
            marginBottom: 10, // Adds space below the input
          },
          textInput: {
            color: "#000",
            height: "100%",
            paddingVertical: 0,
            paddingLeft: 16,
          },
          listView: {
            backgroundColor: "#FFF",
            borderWidth: 1,
            borderColor: "#ddd",
            maxHeight: 250,
          },
          row: {
            padding: 10,
            height: 44,
            flexDirection: "row",
            alignItems: "center",
          },
          separator: {
            height: 1,
            backgroundColor: "#ddd",
          },
          description: {
            color: "#444",
          },
        }}
        renderRow={(rowData) => (
          <View className="flex-row items-center" style={{ zIndex: 100 }}>
            <Ionicons name="location" size={24} style={{ marginRight: 10 }} />
            <Text>{rowData.description}</Text>
          </View>
        )}
        listEmptyComponent={
          <View className="items-center justify-center py-2">
            <Text>No result.</Text>
          </View>
        }
        renderLeftButton={renderLeftButton}
        renderRightButton={renderRightButton}
        fetchDetails={true}
        enablePoweredByContainer={false}
        minLength={2}
        query={{
          key: process.env.EXPO_PUBLIC_GOOGLE_API_KEY,
          language: "en",
          // types: "(streets)",
        }}
        textInputProps={{
          placeholderTextColor: "#444444",
          onFocus: () => setIsFocused(true),
          onBlur: () => setIsFocused(false),
        }}
      />
    </View>
  );
};

export default GooglePlaces;