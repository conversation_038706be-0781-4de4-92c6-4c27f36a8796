import {
  View,
  Text,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import React from "react";
import { Href, router } from "expo-router";
import Button from "@/components/Button";

const PasswordChanged = () => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="relative flex-1 items-center justify-center bg-white w-full h-full">
          <View className="w-[60%] mx-auto items-center gap-y-4">
            <Image
              source={require("../../assets/images/Success.png")}
              className="h-[61px] w-[61px]"
              resizeMode="contain"
            />
            <Text className="text-center text-[#151B2D] font-semibold text-[24px]">
              New password set!
            </Text>
            <Text className="text-center text-[#787A80]">
              Go back and log in with the new password you just created.
            </Text>
          </View>

          <View className="absolute bottom-10 w-[90%] mx-auto">
            <Button
              text="Continue"
              buttonClassName="bg-[#473BF0]"
              textClassName="text-[#FFFFFF]"
              onClick={() => router.push("/SignIn" as Href)}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default PasswordChanged;
