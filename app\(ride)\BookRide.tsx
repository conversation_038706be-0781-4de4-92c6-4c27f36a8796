import { View, StatusBar, Alert } from "react-native";
import React, { useEffect, useState } from "react";
import AvailableCorides from "@/components/AvailableCorides";
import PaymentMode from "@/components/PaymentMode";
import DriverConnect from "@/components/DriverConnect";
import DriverArrived from "@/components/DriverArrived";
import TripStarted from "@/components/TripStarted";
import TripEnded from "@/components/TripEnded";
import TripRoute from "@/components/TripRoute";
import DriverCancelRide from "@/components/DriverCancelRide";

import { router, useLocalSearchParams } from "expo-router";
import TripMap from "@/components/TripMap";
import { useUser } from "@/context/UserContext";
import Toast from "react-native-toast-message";
import { Trip } from "@/utils/types";

import DriverInformation from "../../components/DriverInformation";
import useActiveTrip from "@/hooks/useActiveTrip";
import TripPreview from "@/components/TripPreview";

let Realtime: typeof import('ably').Realtime;
try {
  const { Realtime: AblyRealtime } = require("ably");
  Realtime = AblyRealtime;
} catch (error) {
  console.error("Failed to import Ably:", error);
}

const BookRide = () => {
  const { data: activeTrip, isLoading, refetchActive } = useActiveTrip();

  const { trip: tripParams } = useLocalSearchParams();
  let trip = null;
  try {
    trip = tripParams ? JSON.parse(decodeURIComponent(tripParams as string)) : null;
  } catch (error) {
    console.error("Error parsing trip data:", error);
    trip = null;
  }

  const { rides: ridesParam } = useLocalSearchParams();
  let rides = [];
  let ridesParseError = false;
  
  try {
    rides = ridesParam ? JSON.parse(ridesParam as string) : [];
    console.log("Successfully parsed rides:", rides);
  } catch (error) {
    console.error("Error parsing rides data:", error, ridesParam);
    ridesParseError = true;
    Toast.show({
      type: "error",
      text1: "Error loading ride data",
      text2: "Please try again",
    });
  }

  const { ride: userRide, initialRegion, updateRide } = useUser();

  const [step, setStep] = useState(1);
  const [cancelRide, setCancelRide] = useState(false);
  const [showPassenger, setShowPassenger] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState<Trip | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Debug logging
  useEffect(() => {
    console.log("BookRide Debug Info:", {
      step,
      ridesLength: rides?.length,
      ridesParseError,
      initialRegion: !!initialRegion,
      isInitialized
    });
  }, [step, rides, ridesParseError, initialRegion, isInitialized]);

  useEffect(() => {
    if (!Realtime) {
      console.error("Ably Realtime is not available");
      return;
    }
    
    let ably: import('ably').Realtime | null = null;
    try {
      const ablyKey = process.env.EXPO_PUBLIC_ABLY_KEY;
      if (!ablyKey) {
        console.error("Ably key is not defined in environment variables");
        return;
      }
      
      ably = new Realtime({
        key: ablyKey,
        autoConnect: true,
      });
      
      const rideId = userRide?.rideId || (trip && trip.id);
      if (!rideId) {
        console.warn("No ride ID available for Ably subscription");
        return;
      }
      const channel = ably.channels.get(rideId);

      channel.subscribe("ride-cancelled", (message) => {
        console.log("Ride Cancelled", message.data);
        Toast.show({
          type: "success",
          text1: "Driver Cancelled the ride",
        });
        setTimeout(() => {
          router.replace("/(drawer)/(tabs)/Home");
        }, 1000);
      });

      return () => {
        channel.unsubscribe();
        channel.detach();
        if (ably && ably.close) {
          ably.close();
        }
      };
    } catch (error) {
      console.error("Error setting up Ably:", error);
    }
  }, [userRide?.rideId, trip?.id]);

  const [direction, setDirection] = useState(false);
  const [directionOrigin, setDirectionOrigin] = useState({
    latitude: initialRegion?.latitude || 0,
    longitude: initialRegion?.longitude || 0,
  });

  const [directionDestination, setDirectionDestination] = useState({
    latitude: 0,
    longitude: 0,
  });

  const setMarkerDirection = (
    latitude: number,
    longitude: number,
    id: string,
    price: number
  ) => {
    if (!initialRegion) {
      console.warn("Initial region is not available");
      return;
    }
    
    setDirectionOrigin({
      latitude: initialRegion.latitude,
      longitude: initialRegion.longitude,
    });
    setDirectionDestination({
      latitude: latitude,
      longitude: longitude,
    });
    setDirection(true);
    updateRide("rideId", id);
    updateRide("price", price);
  };

  // Initialize component state
  useEffect(() => {
    if (initialRegion && !isInitialized) {
      setIsInitialized(true);
    }
  }, [initialRegion, isInitialized]);

  // FIXED: Only redirect if there's a parse error OR if we have no rides after initialization
  useEffect(() => {
    if (ridesParseError) {
      setTimeout(() => {
        router.back();
      }, 2000);
      return;
    }

    // Only check for empty rides after component is initialized and step is 1
    if (isInitialized && step === 1 && Array.isArray(rides) && rides.length === 0) {
      console.warn("No rides available to display");
      Toast.show({
        type: "info",
        text1: "No rides available",
        text2: "Please try different search criteria",
      });
      
      setTimeout(() => {
        router.back();
      }, 2000);
    }
  }, [step, rides, ridesParseError, isInitialized]);

  // Don't render anything until initialized (prevent premature redirects)
  if (!isInitialized) {
    return (
      <View className="h-full w-full bg-white flex justify-center items-center">
        <StatusBar barStyle="dark-content" backgroundColor="transparent" />
        <Toast />
      </View>
    );
  }

  return (
    <View className="h-full w-full bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="transparent" />

      <View style={{ flex: 1, position: 'relative' }}>
        <TripMap
          step={step}
          markers={rides || []}
          pin={activeTrip?.data}
          direction={direction}
          directionOrigin={directionOrigin}
          directionDestination={directionDestination}
          setMarkerDirection={setMarkerDirection}
        />

        <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, pointerEvents: 'box-none' }}>
          <TripRoute
            from={userRide?.pickup?.name || (trip && trip.origin?.name) || "Loading..."}
            to={userRide?.dropoff?.name || (trip && trip.destination?.name) || "Loading..."}
          />

          {step === 1 && rides && rides.length > 0 && (
            <AvailableCorides
              step={step}
              setStep={setStep}
              availableCoride={rides}
              setMarkerDirection={setMarkerDirection}
              trip={selectedTrip}
              setTrip={setSelectedTrip}
            />
          )}
          
          {step === 2 && selectedTrip && (
            <TripPreview
              step={step}
              setStep={setStep}
              setShowPassenger={setShowPassenger}
              trip={selectedTrip}
              setCancelRide={setCancelRide}
            />
          )}
          
          {step === 3 &&
            (cancelRide ? (
              <DriverCancelRide
                setCancelRide={setCancelRide}
                tripId={userRide?.rideId}
              />
            ) : (
              <DriverConnect
                step={step}
                setStep={setStep}
                setCancelRide={setCancelRide}
                refetchActive={refetchActive}
                trip={activeTrip?.data}
              />
            ))}

          {step === 4 &&
            (showPassenger ? (
              <DriverInformation
                setShowPassenger={setShowPassenger}
                trip={activeTrip?.data}
              />
            ) : cancelRide ? (
              <DriverCancelRide setCancelRide={setCancelRide} />
            ) : (
              <DriverArrived
                step={step}
                setStep={setStep}
                setShowPassenger={setShowPassenger}
                setCancelRide={setCancelRide}
                trip={activeTrip?.data}
                isLoading={isLoading}
                refetchActive={refetchActive}
              />
            ))}
          
          {step === 5 &&
            (showPassenger ? (
              <DriverInformation
                setShowPassenger={setShowPassenger}
                trip={activeTrip?.data}
              />
            ) : (
              <TripStarted
                step={step}
                setStep={setStep}
                setShowPassenger={setShowPassenger}
                trip={activeTrip?.data}
              />
            ))}
          
          {step === 6 && (
            <TripEnded step={step} setStep={setStep} trip={activeTrip?.data} />
          )}
        </View>
      </View>
      
      <Toast />
    </View>
  );
};

export default BookRide;