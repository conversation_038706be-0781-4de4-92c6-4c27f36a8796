import {
  <PERSON>,
  Text,
  SafeAreaView,
  <PERSON><PERSON><PERSON>iew,
  StatusBar,
  Alert,
  Platform,
} from "react-native";
import React, { Dispatch, SetStateAction, useState, useEffect } from "react";
import * as ImagePicker from "expo-image-picker";
import axios from "axios";
import PickImage from "./PickImage";
import Button from "./Button";
import * as SecureStore from "expo-secure-store";
import Toast from "react-native-toast-message";
import useCurrentUser from "@/hooks/useCurrentUser";
import { router } from "expo-router";

interface Props {
  step: number;
  setStep: Dispatch<SetStateAction<number>>;
}

interface ImageState {
  driversLicenseFront: string;
  driversLicenseBack: string;
  vehicleLicense: string;
}

const UploadDocuments: React.FC<Props> = ({ step, setStep }) => {
  const { data } = useCurrentUser();
  const [images, setImages] = useState<ImageState>({
    driversLicenseFront: "",
    driversLicenseBack: "",
    vehicleLicense: "",
  });
  const [loading, setLoading] = useState(false);
  const [authToken, setAuthToken] = useState<string | null>(null);

  // Fetch and validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      try {
        const tokenData = await SecureStore.getItemAsync("authToken");
        if (!tokenData) {
          Toast.show({
            type: "error",
            text1: "Authentication error",
            text2: "Please log in again",
          });
          router.push("/(auth)/SignIn");
          return;
        }
        const parsedToken = JSON.parse(tokenData);
        setAuthToken(parsedToken.token);
      } catch (error) {
        console.error("Token validation error:", error);
        Toast.show({
          type: "error",
          text1: "Authentication error",
          text2: "Please log in again",
        });
      }
    };

    validateToken();
  }, []);

  const requestPermissions = async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Sorry, we need media library permissions to make this work!"
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error("Permission request error:", error);
      return false;
    }
  };

  const pickImageAsync = async (imageType: keyof typeof images) => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        // Handle platform-specific URI
        const uri =
          Platform.OS === "android"
            ? result.assets[0].uri
            : result.assets[0].uri.replace("file://", "");
        setImages((prev) => ({ ...prev, [imageType]: uri }));
      }
    } catch (error) {
      console.error("Image picking error:", error);
      Toast.show({
        type: "error",
        text1: "Failed to select image",
        text2: "Please try again",
      });
    }
  };

  const createFormData = (type: string, imageUri: string) => {
    const formData = new FormData();

    // Get file extension from URI
    const fileExtension = imageUri.split(".").pop() || "jpg";

    formData.append("type", type);
    formData.append("image", {
      uri: imageUri,
      type: `image/${fileExtension}`,
      name: `${type}.${fileExtension}`,
    } as any);

    return formData;
  };

  const uploadImage = async (type: string, imageUri: string) => {
    if (!authToken) {
      throw new Error("No authentication token available");
    }

    try {
      const formData = createFormData(type, imageUri);

      const response = await axios.post(
        `${process.env.EXPO_PUBLIC_BASE_URL}/user/verification/image`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${authToken}`,
          },
          timeout: 10000,
        }
      );

      return response.data;
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          throw new Error(`${error.response.data?.message || "Bad Request"}`);
        } else if (error.response?.status === 401) {
          throw new Error("Authentication expired. Please log in again.");
        }
      }
      throw new Error(`Failed to upload ${type}: ${error.message}`);
    }
  };

  const uploadAllImages = async () => {
    setLoading(true);

    try {
      const uploads = [
        uploadImage("driverLicense_front", images.driversLicenseFront),
        uploadImage("driverLicense_back", images.driversLicenseBack),
        uploadImage("vehicleLicense", images.vehicleLicense),
      ];

      await Promise.all(uploads);

      Toast.show({
        type: "success",
        text1: "Documents uploaded successfully",
      });

      setTimeout(() => setStep(step + 1), 400);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Upload failed",
        text2: error.message || "Please try again",
      });
      console.error("Upload error:", error);
    } finally {
      setLoading(false);
    }
  };

  const isReadyToUpload =
    images.driversLicenseBack &&
    images.driversLicenseFront &&
    images.vehicleLicense &&
    authToken;

  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="mt-4 pb-5">
          <View>
            <Text className="text-[#151B2D] font-semibold text-[20px]">
              Driver's license
            </Text>

            <PickImage
              image={images.driversLicenseFront}
              pickImage={() => pickImageAsync("driversLicenseFront")}
              text="Driver's License (Front)"
            />

            <PickImage
              image={images.driversLicenseBack}
              pickImage={() => pickImageAsync("driversLicenseBack")}
              text="Driver's License (Back)"
            />

            <View className="mt-4">
              <Text className="text-[#151B2D] font-semibold text-[20px]">
                Vehicle license
              </Text>

              <PickImage
                image={images.vehicleLicense}
                pickImage={() => pickImageAsync("vehicleLicense")}
                text="Vehicle License"
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={{ marginTop: "auto", width: "100%", marginBottom: 5 }}>
        <Button
          buttonDisabled={loading || !isReadyToUpload}
          isLoading={loading}
          text="Next"
          buttonClassName="bg-[#473BF0]"
          textClassName="text-white"
          onClick={uploadAllImages}
        />
      </View>
    </SafeAreaView>
  );
};

export default UploadDocuments;
