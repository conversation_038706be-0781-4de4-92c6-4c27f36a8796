import GoBack from "@/components/GoBack";
import { Stack } from "expo-router";

export default function ProfileLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: { backgroundColor: "#ffffff" },
        headerTintColor: "#000000",
        headerShadowVisible: true,
      }}
    >

      <Stack.Screen
        name="UserProfileScreen"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Your Profile",
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="EditProfileScreen"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Edit Porfile",
          headerShadowVisible: false,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="Security"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Security & Log in",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="Notification"
        options={{
          headerShown: false,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Notification",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="Promo"
        options={{
          headerShown: false,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Notification",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

<Stack.Screen
        name="EnterPromo"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Enter Promo Code",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />

      <Stack.Screen
        name="ChangePassword"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "Change password",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
      <Stack.Screen
        name="VerifyPhone"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,
          // title: "Security & Log in",
          headerTitle: "",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
        }}
      />
      <Stack.Screen
        name="ResetPassword"
        options={{
          headerShown: true,
          headerLeft: () => <GoBack />,

          headerTitle: "Reset password",
          headerShadowVisible: true,
          headerBackTitle: "Back",
          headerTintColor: "#000000",
          headerStyle: { backgroundColor: "#ffffff" },
          headerTitleStyle: {
            fontSize: 16,
            fontWeight: "600",
          },
        }}
      />
    </Stack>
  );
}
