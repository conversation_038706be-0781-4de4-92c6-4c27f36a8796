import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import Button from "@/components/Button";
import { router, useLocalSearchParams } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import { StyleSheet } from "react-native";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { useUser } from "@/context/UserContext";
import { OtpInput } from "react-native-otp-entry";

export default function VerifyPhone() {
  const { pinId } = useLocalSearchParams();
  const pinIdString = Array.isArray(pinId) ? pinId[0] : pinId;

  const [otp, setOtp] = useState("");
  const [countDown, setCountdown] = useState(10);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const { userInfo } = useUser();

  const startCountdown = () => {
    setIsResendDisabled(true);
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(interval);
          setCountdown(10);
          setIsResendDisabled(false);
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    startCountdown();
  }, []);

  useEffect(() => {
    if (!pinId) router.replace("/SignUp");
  }, []);

  const { mutate: verifyOTp, isPending: otpVerificationPending } = useMutation({
    mutationFn: services.verifyOTp,
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "OTP Verified",
      });
      router.push("/ProfileSetUp");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const { mutate: sendOTP, isPending } = useMutation({
    mutationFn: services.sendOtp,
    onSuccess: (data) => {
      startCountdown();
      Toast.show({
        type: "success",
        text1: "SMS sent!",
        text2: `${data.smsStatus} to ${data.phone_number}`,
      });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error sending OTP",
        text2: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            <AuthHeader
              header="Verify your phone number"
              subHeader={`Enter the verification code to this phone number ${userInfo.phoneNumber}`}
            />
            <View className="py-4">
              <OtpInput
                numberOfDigits={4}
                onTextChange={(code) => setOtp(code)}
                focusStickBlinkingDuration={500}
                theme={{
                  pinCodeContainerStyle: styles.underlineStyleBase,
                  pinCodeTextStyle: styles.pinCodeText,
                  focusedPinCodeContainerStyle:
                    styles.underlineStyleHighLighted,
                }}
                focusColor="black"
                onFilled={(code) => {
                  verifyOTp({ dataBody: { pin: code, pin_id: pinIdString } });
                }}
              />
            </View>
            <View>
              {isResendDisabled ? (
                <Text className="text-[#787A80]">
                  Didn't get code? Resend in{" "}
                  <Text className="text-[#151B2D] font-medium">
                    {countDown}
                  </Text>
                </Text>
              ) : (
                <View className="items-center flex-row">
                  <Text>Didn't get code? </Text>
                  <TouchableOpacity
                    disabled={otpVerificationPending}
                    activeOpacity={0.9}
                    onPress={() =>
                      sendOTP({
                        dataBody: { phoneNumber: userInfo.phoneNumber },
                      })
                    }
                  >
                    <Text className="text-[#473BF0] font-medium">
                      {isPending ? "Please wait" : "Resend"}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                text="Verify"
                isLoading={otpVerificationPending}
                buttonDisabled={otp.length < 4}
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  verifyOTp({ dataBody: { pin: otp, pin_id: pinIdString } });
                }}
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  underlineStyleBase: {
    width: 78,
    height: 48,
    borderWidth: 0,
    backgroundColor: "#F6F6F6",
    color: "#151B2D",
    fontWeight: "500",
    fontSize: 16,
    borderRadius: 8,
  },
  underlineStyleHighLighted: {
    borderColor: "#473BF0",
    borderWidth: 1,
    backgroundColor: "#FFFFFF",
  },
  pinCodeText: {
    fontSize: 20,
    color: "#151B2D", // Text color (if text is added)
  },
});
