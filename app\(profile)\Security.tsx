import { View, Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import { router } from "expo-router";
import useCurrentUser from "@/hooks/useCurrentUser";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const Security = () => {
  const { data } = useCurrentUser();

  const userEmail = data?.email;

  const { mutate, isPending } = useMutation({
    mutationFn: () => services.requestPasswordReset(userEmail),
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Request Sent",
        text2: `If an account exists for ${userEmail}, a password reset PIN has been sent.`,
      });
      router.push({
        pathname: "/VerifyPhone",
        params: {
          emailOrPhone: userEmail,
        },
      });
    },
    onError: () => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "An error occurred. Please try again later.",
      });
    },
  });

  return (
    <View className="h-full w-full bg-white">
      <View className="w-[90%] mx-auto mt-4">
        <TouchableOpacity
          onPress={() => router.push("/ChangePassword")}
          activeOpacity={0.8}
          className="flex-row justify-between items-center h-[48px] border border-slate-200 drop-shadow-sm px-2 rounded-[6px]"
        >
          <Text className="text-[#151B2D] text-[14px]">Change password</Text>
          <Image
            source={require("../../assets/images/right_line.png")}
            className="w-[18px] h-[18px]"
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            if (userEmail) {
              mutate();
            } else {
              Toast.show({
                type: "error",
                text1: "Error",
                text2: "User email is not available.",
              });
            }
          }}
          disabled={isPending || !userEmail}
          activeOpacity={0.8}
          className="mt-2 flex-row justify-between items-center h-[48px] border border-slate-200 drop-shadow-sm px-2 rounded-[6px]"
        >
          <Text className="text-[#151B2D] text-[14px]">Reset password</Text>
          <Image
            source={require("../../assets/images/right_line.png")}
            className="w-[18px] h-[18px]"
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Security;
