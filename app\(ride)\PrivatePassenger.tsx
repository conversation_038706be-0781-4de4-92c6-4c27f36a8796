import React, { useState } from "react";
import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  Image,
  ScrollView,
} from "react-native";
import { useRide } from "@/context/RideProvider";
import GooglePlaces from "@/components/GooglePlaces";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const PrivatePassenger = () => {
  const [activeInput, setActiveInput] = useState("");

  const {
    updatePrivateRideStop,
    privateRide,
    addPrivateRideStop,
    removePrivateRideStop,
    updatePrivateRideLocation,
  } = useRide();

  const { mutate: getAvailableCoRides, isPending } = useMutation({
    mutationFn: services.getAvailableCoRides,
    onSuccess: (data: any) => {
      if (data.status) {
        router.push({
          pathname: "/PrivatePassengerMap",
          params: { rides: JSON.stringify(data.data) },
        } as Href);
      }
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      // className="flex-1"
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="flex-1 bg-white">
          <ScrollView
            // horizontal={true}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ flexGrow: 1 }}

            // listViewDisplayed={false}
          >
            <StatusBar
              barStyle={"dark-content"}
              backgroundColor={"transparent"}
            />
            <View className="w-[90%] mx-auto mt-2 pb-5 relative">
              <GooglePlaces
                placeholderText={
                  activeInput === "origin" ? "Enter origin location" : "Origin"
                }
                isFocused={activeInput === "origin"}
                setIsFocused={(focused) =>
                  setActiveInput(focused ? "origin" : "")
                }
                ObjKey="origin"
                onPlaceSelected={(location) =>
                  updatePrivateRideLocation("origin", location)
                }
                leftFocusedImage={require("../../assets/images/radio.png")}
              />

              <GooglePlaces
                placeholderText={
                  activeInput === "destination"
                    ? "Enter destination location"
                    : "Destination"
                }
                isFocused={activeInput === "destination"}
                setIsFocused={(focused) =>
                  setActiveInput(focused ? "destination" : "")
                }
                ObjKey="destination"
                onPlaceSelected={(location) =>
                  updatePrivateRideLocation("destination", location)
                }
                leftFocusedImage={require("../../assets/images/location.png")}
              />

              <View className="my-3">
                <TouchableOpacity
                  onPress={() =>
                    addPrivateRideStop({ name: "", lat: 0, lng: 0 })
                  }
                >
                  <Text className="text-[#151B2D] text-[16px] font-medium">
                    + Add stop(s)
                  </Text>
                </TouchableOpacity>
                <Text className="mt-1 text-[#787A80] text-[13px] font-medium">
                  This helps you get more bookings along your trip
                </Text>

                <View>
                  {privateRide.stops.map((stop, i) => (
                    <View key={i} className="mt-4">
                      <Text className="font-medium text-[16px]">
                        Stop {i + 1}
                      </Text>

                      <GooglePlaces
                        placeholderText={
                          activeInput === `stop-${i}`
                            ? "Enter stop location"
                            : `Stop ${i + 1}`
                        }
                        isFocused={activeInput === `stop-${i}`}
                        setIsFocused={(focused) =>
                          setActiveInput(focused ? `stop-${i}` : "")
                        }
                        ObjKey={`stop-${i}`}
                        onPlaceSelected={(location) =>
                          updatePrivateRideStop(i, location)
                        }
                        leftFocusedImage={require("../../assets/images/radio.png")}
                      />

                      <TouchableOpacity
                        onPress={() => removePrivateRideStop(i)}
                      >
                        <Text className="font-semibold text-xs">Remove</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </ScrollView>

          <View className="w-[90%] mx-auto">
            <Button
              buttonDisabled={
                isPending ||
                !privateRide.origin.name ||
                !privateRide.destination.name
              }
              isLoading={isPending}
              onClick={() =>
                getAvailableCoRides({
                  dataBody: {
                    pickup: privateRide.origin,
                    dropoff: privateRide.destination,
                    mode: "private",
                  },
                })
              }
              text="Next"
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] mb-[3%]"
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default PrivatePassenger;
