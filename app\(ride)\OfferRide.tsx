import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StatusBar,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  TouchableOpacity,
  Image,
  ScrollView,
  TextInput,
} from "react-native";
import { useRide } from "@/context/RideProvider";
import GooglePlaces from "@/components/GooglePlaces";
import DateTimeSelector from "@/components/DateAndTimeSelector";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import { useTripStore } from "@/app/store/tripStore";

interface DateObject {
  day: string;
}

const OfferRide = () => {
  const [activeInput, setActiveInput] = useState("");
  const [date, setDate] = useState(new Date());
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [isTimePickerVisible, setTimePickerVisibility] = useState(false);
  const [tripType, setTripType] = useState("one-time");
  const [dates, setDates] = useState<DateObject[]>([]);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const { updateRide, updateLocation, ride, addStop, removeStop, updateStop } =
    useRide();
  const { setDriverNote } = useTripStore();
  const [isdateSelected, setIsDateSelected] = useState(false);
  const [isTimeSelected, setIsTimeSelected] = useState(false);

  useEffect(() => {
    const daysOfWeek: string[] = [
      "Every Sunday",
      "Every Monday",
      "Every Tuesday",
      "Every Wednesday",
      "Every Thursday",
      "Every Friday",
      "Every Saturday",
    ];
    setAvailableDates(daysOfWeek);
  }, []);

  const toggleDate = (day: string): void => {
    setDates((prevDates) => {
      const isSelected = prevDates.some((date) => date.day === day);
      if (isSelected) {
        return prevDates.filter((date) => date.day !== day);
      } else {
        return [...prevDates, { day }];
      }
    });
  };

  const isDateSelected = (day: string): boolean => {
    return dates.some((date) => date.day === day);
  };

  const handleDateConfirm = (selectedDate: Date) => {
    // Set the date without changing the time part
    setDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      return newDate;
    });
    hideDatePicker();
    setIsDateSelected(true);
  };

  const handleTimeConfirm = (selectedTime: Date) => {
    // Adjust the time part of the existing date
    const newDate = new Date(date);
    newDate.setHours(selectedTime.getHours());
    newDate.setMinutes(selectedTime.getMinutes());

    // Update the state
    setDate(newDate);

    // Update the ride with the combined date and time as a timestamp
    updateRide("timestamp", newDate.toISOString());

    setIsTimeSelected(true);
    hideTimePicker();
  };

  const handleDriverNoteChange = (text: string) => {
    // Update both ride context and trip store
    updateRide("driverNote", text);
    setDriverNote(text);
  };

  const handleNext = () => {
    router.push("/TripPreference" as Href);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const hideTimePicker = () => {
    setTimePickerVisibility(false);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      // className="flex-1"
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="flex-1 bg-white">
          <ScrollView
            // horizontal={true}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ flexGrow: 1 }}

            // listViewDisplayed={false}
          >
            <StatusBar
              barStyle={"dark-content"}
              backgroundColor={"transparent"}
            />
            <View className="w-[90%] mx-auto mt-2 pb-5 relative">
              <View className="flex-row mb-4 justify-between bg-[#F6F6F6] rounded-[8px] p-[6px] mt-4">
                <TouchableOpacity
                  activeOpacity={0.9}
                  onPress={() => {
                    setTripType("one-time");
                    updateRide("type", "one-time");
                  }}
                  className={`px-[26px] py-[8px] rounded-[4px] ${
                    tripType === "one-time" ? "bg-[#FFFFFF]" : "bg-[#F6F6F6]"
                  }`}
                >
                  <Text className="text-[#151B2D] text-[16px] font-medium">
                    One Time trip
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  activeOpacity={0.9}
                  onPress={() => {
                    setTripType("recurring");
                    updateRide("type", "recurring");
                  }}
                  className={`px-[26px] py-[8px] rounded-[4px] ${
                    tripType === "recurring" ? "bg-[#FFFFFF]" : "bg-[#F6F6F6]"
                  }`}
                >
                  <Text className="text-[#151B2D] text-[16px] font-medium">
                    Recurring trip
                  </Text>
                </TouchableOpacity>
              </View>

              <GooglePlaces
                placeholderText={
                  activeInput === "origin" ? "Enter origin location" : "Origin"
                }
                isFocused={activeInput === "origin"}
                setIsFocused={(focused) =>
                  setActiveInput(focused ? "origin" : "")
                }
                ObjKey="origin"
                onPlaceSelected={(location) =>
                  updateLocation("origin", location)
                }
                leftFocusedImage={require("../../assets/images/radio.png")}
              />

              <GooglePlaces
                placeholderText={
                  activeInput === "destination"
                    ? "Enter destination location"
                    : "Destination"
                }
                isFocused={activeInput === "destination"}
                setIsFocused={(focused) =>
                  setActiveInput(focused ? "destination" : "")
                }
                ObjKey="destination"
                onPlaceSelected={(location) =>
                  updateLocation("destination", location)
                }
                leftFocusedImage={require("../../assets/images/location.png")}
              />

              <View className="my-3">
                <TouchableOpacity
                  onPress={() => addStop({ name: "", lat: 0, lng: 0 })}
                >
                  <Text className="text-[#151B2D] text-[16px] font-medium">
                    + Add stop(s)
                  </Text>
                </TouchableOpacity>
                <Text className="mt-1 text-[#787A80] text-[13px] font-medium">
                  This helps you get more bookings along your trip
                </Text>

                <View>
                  {ride.stops.map((stop, i) => (
                    <View key={i} className="mt-4">
                      <Text className="font-medium text-[16px]">
                        Stop {i + 1}
                      </Text>

                      <GooglePlaces
                        placeholderText={
                          activeInput === `stop-${i}`
                            ? "Enter stop location"
                            : `Stop ${i + 1}`
                        }
                        isFocused={activeInput === `stop-${i}`}
                        setIsFocused={(focused) =>
                          setActiveInput(focused ? `stop-${i}` : "")
                        }
                        ObjKey={`stop-${i}`}
                        onPlaceSelected={(location) => updateStop(i, location)}
                        leftFocusedImage={require("../../assets/images/radio.png")}
                      />

                      <TouchableOpacity onPress={() => removeStop(i)}>
                        <Text className="font-semibold text-xs">Remove</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </View>

              <DateTimeSelector
                showDatePicker={() => setDatePickerVisibility(true)}
                showTimePicker={() => setTimePickerVisibility(true)}
                date={date}
                isDatePickerVisible={isDatePickerVisible}
                isTimePickerVisible={isTimePickerVisible}
                handleDateConfirm={handleDateConfirm}
                handleTimeConfirm={handleTimeConfirm}
                hideDatePicker={hideDatePicker}
                hideTimePicker={hideTimePicker}
                dateSelected={isdateSelected}
                timeSelected={isTimeSelected}
                textToBeShown="Select date"
              />

              {/* {ride.type === "one-time" && (
                <View className="mt-4">
                  <Text className="text-[#151B2D] text-[16px] font-medium">
                    + Add return trip
                  </Text>
                </View>
              )} */}

              {ride.type === "recurring" && (
                <View className="mt-5">
                  <Text className="text-[#4A4C50] text-[16px] font-medium">
                    Select all that apply
                  </Text>
                  <View className="gap-y-[1px] bg-[#F6F6F6] rounded-[6px] mt-3">
                    {availableDates.map((day) => (
                      <TouchableOpacity
                        activeOpacity={0.9}
                        key={day}
                        onPress={() => toggleDate(day)}
                        className="flex-row justify-between items-center h-[42px] border-t px-3 border-[#ECECEC] rounded-[6px]"
                      >
                        <Text className="text-[#151B2D] text-[15px] font-normal">
                          {day}
                        </Text>
                        {isDateSelected(day) && (
                          <Image
                            source={require("../../assets/images/Check.png")}
                            className="w-[24px] h-[24px]"
                            resizeMode="contain"
                          />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

              {/* Driver's Note Section */}
              <View className='mt-6'>
                <Text className='text-[15px] font-medium text-[#4A4C50] mb-2'>Note (Optional)</Text>
                <TextInput
                  className='bg-[#F4F4F4] p-3 rounded-md text-sm'
                  placeholder="Enter note text"
                  value={ride.driverNote}
                  onChangeText={handleDriverNoteChange}
                  multiline
                  numberOfLines={5}
                  textAlignVertical="top"
                />
              </View>
            </View>
          </ScrollView>

          <View className="w-[90%] mx-auto">
            <Button
              buttonDisabled={
                !ride.origin.name ||
                !ride.destination.name ||
                !isdateSelected ||
                !isTimeSelected
              }
              onClick={handleNext}
              text="Next"
              textClassName="text-white"
              buttonClassName="bg-[#473BF0] mb-[3%]"
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default OfferRide;
