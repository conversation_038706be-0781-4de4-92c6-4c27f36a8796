import { View, Text, Image, SafeAreaView, StatusBar } from "react-native";
import React from "react";
import Button from "@/components/Button";
import { Href, router } from "expo-router";
import GoBack from "@/components/GoBack";

const Promo = () => {
  return (
    <SafeAreaView className="h-full bg-white flex-1 w-full">
      <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />

      <View className="w-[90%] mx-auto h-full">
        <Text className="text-[22px] text-[#151B2D] font-semibold mt-5">
          Promo codes
        </Text>
        <View className="flex-1 items-center justify-center gap-y-5 -mt-20">
          <Image
            source={require("../../assets/images/sale-fill.png")}
            className="w-[60px] h-[60px]"
            resizeMode="contain"
          />
          <Text className="text-[#787A80] text-[14px]">
            You don’t have any promo codes
          </Text>
        </View>
        <View className="absolute bottom-[1%] w-full">
          <Button
            text="Continue"
            buttonClassName="bg-[#473BF0]"
            textClassName="text-white"
            onClick={() => router.push("/AddPromo" as Href)}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Promo;
