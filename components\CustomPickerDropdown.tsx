import React, { useState } from "react";
import {
  View, 
  Text, 
  TouchableOpacity, 
  Modal, 
  TouchableWithoutFeedback,
  ScrollView,
  StyleSheet,
  ViewStyle
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";

interface Item {
  label: string;
  value: string | number | null;
}

interface CustomPickerDropdownProps {
  label?: string;
  items: Item[];
  onValueChange: (value: string | number | null) => void;
  value: string | number | null;
  placeholder?: Item;
  style?: {
    button?: ViewStyle;
  };
}

const CustomPickerDropdown = ({
  label,
  items,
  onValueChange,
  value,
  placeholder = { label: "Please select", value: null },
  style = {}
}: CustomPickerDropdownProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  
  const selectedItem = value !== null && value !== undefined 
    ? items.find(item => item.value == value) 
    : null;
  
  const displayText = selectedItem 
    ? selectedItem.label 
    : placeholder.label;

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        style={[styles.pickerButton, style.button]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        <Text style={[styles.pickerText, !selectedItem && styles.placeholderText]}>
          {displayText}
        </Text>
        <MaterialIcons name="arrow-drop-down" size={24} color="#4A4C50" />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Select an option</Text>
                  <TouchableOpacity onPress={() => setModalVisible(false)}>
                    <MaterialIcons name="close" size={24} color="#151B2D" />
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={styles.optionsList}>
                  {placeholder && (
                    <TouchableOpacity
                      style={[
                        styles.option, 
                        value === placeholder.value && styles.selectedOption
                      ]}
                      onPress={() => {
                        onValueChange(placeholder.value);
                        setModalVisible(false);
                      }}
                    >
                      <Text style={[
                        styles.optionText,
                        value === placeholder.value && styles.selectedOptionText
                      ]}>
                        {placeholder.label}
                      </Text>
                      {value === placeholder.value && (
                        <MaterialIcons name="check" size={20} color="#473BF0" />
                      )}
                    </TouchableOpacity>
                  )}
                  
                  {items.map((item, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.option, 
                        item.value == value && styles.selectedOption
                      ]}
                      onPress={() => {
                        onValueChange(item.value);
                        setModalVisible(false);
                      }}
                    >
                      <Text style={[
                        styles.optionText,
                        item.value == value && styles.selectedOptionText
                      ]}>
                        {item.label}
                      </Text>
                      {item.value == value && (
                        <MaterialIcons name="check" size={20} color="#473BF0" />
                      )}
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    color: "#4A4C50",
    fontWeight: "500",
    fontSize: 15,
    marginBottom: 8,
  },
  pickerButton: {
    backgroundColor: "#F6F6F6",
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  pickerText: {
    fontSize: 16,
    color: "black",
  },
  placeholderText: {
    color: "#757575",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "white",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
    maxHeight: "50%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#151B2D",
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  selectedOption: {
    backgroundColor: "#F7F7FF",
  },
  optionText: {
    fontSize: 16,
    color: "#151B2D",
  },
  selectedOptionText: {
    color: "#473BF0",
    fontWeight: "500",
  },
});

export default CustomPickerDropdown;