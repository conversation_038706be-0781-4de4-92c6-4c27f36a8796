import React, { useState } from 'react';
import { View, Text, Switch } from 'react-native';
import { styled } from "nativewind";
import useCurrentUser from "@/hooks/useCurrentUser";
import GoBack from '@/components/GoBack';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledSwitch = styled(Switch);

const NotificationScreen = () => {
    const [pushNotification, setPushNotification] = useState(false);
    const [smsNotification, setSmsNotification] = useState(false);
    const [emailNotification, setEmailNotification] = useState(false);

    const { data: user } = useCurrentUser();

    return (
        <StyledView className="flex-1 p-5 bg-[#F4F4F4]">
            <View className="flex-row items-center my-4">
                 <GoBack />
            </View>

            <StyledText className="text-2xl font-bold">Notifications</StyledText>


            <View className=' bg-white p-5 my-7 rounded-lg'>
                <StyledView className="flex-row justify-between items-center mb-5">
                    <StyledView>
                        <StyledText className="text-lg font-semibold">Push notification</StyledText>
                        <StyledText className="text-sm text-gray-600">
                            For all activities, booking, messages, etc.
                        </StyledText>
                    </StyledView>

                    <StyledSwitch
                        value={pushNotification}
                        onValueChange={setPushNotification}
                        trackColor={{ false: "#444444", true: "#34D399" }}
                        thumbColor={pushNotification ? "#34D399" : "#FFFFFF"}
                    />
                </StyledView>

                <StyledView className="flex-row justify-between items-center mb-5">
                    <StyledView>
                        <StyledText className="text-lg font-semibold">SMS notification</StyledText>
                        <StyledText className="text-sm text-gray-600">For booking only</StyledText>
                    </StyledView>

                    <StyledSwitch
                        value={smsNotification}
                        onValueChange={setSmsNotification}
                        trackColor={{ false: "#444444", true: "#34D399" }}
                        thumbColor={smsNotification ? "#34D399" : "#FFFFFF"}
                    />
                </StyledView>

                <StyledView className="flex-row justify-between items-start mb-5">
                    <StyledView className="flex-1 mr-4">
                        <StyledText className="text-lg font-semibold">Email notification</StyledText>
                        <StyledText className="text-sm text-gray-600">
                        For booking, messages, reviews, and marketing
                        </StyledText>
                        <Text className="text-sm text-gray-800">Sent to <Text className="font-bold">{user?.email}</Text></Text>
                    </StyledView>

                    <View className="pt-1">
                        <StyledSwitch
                        value={emailNotification}
                        onValueChange={setEmailNotification}
                        trackColor={{ false: "#444444", true: "#34D399" }}
                        thumbColor={emailNotification ? "#34D399" : "#FFFFFF"}
                        />
                    </View>
                </StyledView>
            </View>

        </StyledView>
    );
};

export default NotificationScreen;
