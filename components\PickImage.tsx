import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ImageBackground,
} from "react-native";
import React from "react";

interface Props {
  image: string;
  pickImage: () => void;
  text: string;
}

const PickImage: React.FC<Props> = ({ image, pickImage, text }: Props) => {
  return (
    <View className="mt-3">
      <Text className="text-[#4A4C50] text-[15px] font-medium mb-2">
        {text}
      </Text>
      {!image ? (
        <TouchableOpacity
          onPress={pickImage}
          className="w-full border-2 rounded-[8px] border-dashed p-5 justify-center items-center border-[#0000004D]"
        >
          <Image
            source={require("../assets/images/IDcard.png")}
            className="w-[32px] h-[32px]"
            resizeMode="contain"
          />
          <Text className="text-center my-2 text-[#8F8F8F] text-[12px] font-medium">
            Upload an image of your driver’s license (.png,.jpeg or .jpg)
          </Text>
          <View className="flex-row items-center bg-[#EDEDED] rounded-[100px] p-1">
            <Image
              source={require("../assets/images/upload.png")}
              className="w-[12px] h-[12px]"
              resizeMode="contain"
            />
            <Text className="text-center text-[#8F8F8F] text-[12px] font-medium ml-1">
              Select Image
            </Text>
          </View>
        </TouchableOpacity>
      ) : (
        <ImageBackground
          source={{ uri: image }}
          className="w-[100%] mx-auto h-[150px] rounded-[8px] relative"
          resizeMode="contain"
        >
          <TouchableOpacity
            onPress={pickImage}
            className="w-[100%] rounded-[8px] absolute h-full bg-[#000000B2] border-2 border-dashed p-5 justify-center items-center border-[#0000004D]"
          >
            <Text className="text-white">Replace Image</Text>
          </TouchableOpacity>
        </ImageBackground>
      )}
    </View>
  );
};

export default PickImage;
