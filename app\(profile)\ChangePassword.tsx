import {
  View,
  Text,
  StatusBar,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Image,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Input from "@/components/Input";
import Button from "@/components/Button";
import { router } from "expo-router";
import { z } from "zod";

import BottomSheet, { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

const changePasswordSchema = z
  .object({
    oldPassword: z.string().min(6, "Password must be at least 6 characters"),
    newPassword: z.string().min(6, "Password must be at least 6 characters"),
    confirmNewPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Passwords don't match",
    path: ["confirmNewPassword"],
  });

type ChangePasswordForm = z.infer<typeof changePasswordSchema>;

export default function ChangePassword() {
  const [formState, setFormState] = useState<ChangePasswordForm>({
    oldPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });

  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const { mutate: handleChangePassword, isPending } = useMutation({
    mutationFn: services.changePassword,
    onSuccess: () => {
      setShowModal(true);
      handleOpenPress();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof ChangePasswordForm, string[]>>
  >({});
  const [isFormValid, setIsFormValid] = useState(false);

  const updateField = (value: string, key: keyof ChangePasswordForm) => {
    setFormState((prev) => ({ ...prev, [key]: value }));
  };

  useEffect(() => {
    const result = changePasswordSchema.safeParse(formState);
    if (result.success) {
      setErrors({});
      setIsFormValid(true);
    } else {
      setErrors(result.error.flatten().fieldErrors);
      setIsFormValid(false);
    }
  }, [formState]);

  const bottomSheetRef = useRef<BottomSheet>(null);
  const customSnapPoints = ["30"];

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        style={[StyleSheet.absoluteFillObject, styles.backdrop]}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        {...props}
      />
    ),
    []
  );

  const handleClosePress = () => bottomSheetRef.current?.close();
  const handleOpenPress = () => bottomSheetRef.current?.expand();

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] h-full mx-auto relative">
            <View className="mt-4">
              <Input
                text="Old password"
                type="password"
                placeHolder="Old password"
                ObjKey="oldPassword"
                value={formState.oldPassword}
                error={errors.oldPassword?.[0]}
                setValue={updateField}
                showPassword={showOldPassword}
                setShowPassword={setShowOldPassword}
              />
              <View className="h-4" />
              <Input
                text="New password"
                type="password"
                placeHolder="New password"
                ObjKey="newPassword"
                error={errors.newPassword?.[0]}
                value={formState.newPassword}
                setValue={updateField}
                showPassword={showNewPassword}
                setShowPassword={setShowNewPassword}
              />
              <View className="h-4" />
              <Input
                text="Confirm new password"
                type="password"
                placeHolder="New password"
                ObjKey="confirmNewPassword"
                error={errors.confirmNewPassword?.[0]}
                value={formState.confirmNewPassword}
                setValue={updateField}
                showPassword={showConfirmPassword}
                setShowPassword={setShowConfirmPassword}
              />
            </View>

            <View className="absolute bottom-7 w-full">
              <Button
                text="Continue"
                buttonDisabled={
                  isPending ||
                  !formState.oldPassword ||
                  !formState.newPassword ||
                  !formState.confirmNewPassword
                }
                isLoading={isPending}
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() => {
                  if (isFormValid)
                    handleChangePassword({
                      dataBody: {
                        oldPassword: formState.oldPassword,
                        newPassword: formState.newPassword,
                      },
                    });
                }}
              />
            </View>
          </View>
          {showModal && (
            <BottomSheet
              ref={bottomSheetRef}
              index={0}
              snapPoints={customSnapPoints}
              enablePanDownToClose={true}
              containerStyle={styles.bottomSheetContainer}
              handleIndicatorStyle={styles.bottomSheetIndicator}
              backgroundStyle={styles.bottomSheetBackground}
              backdropComponent={renderBackdrop}
            >
              <View className="w-full h-full flex-1 relative">
                <TouchableOpacity
                  onPress={() => handleClosePress()}
                  className="z-20"
                >
                  <Image
                    source={require("../../assets/images/close_line.png")}
                    className="w-[24px] h-[24px] absolute right-8"
                    resizeMode="contain"
                  />
                </TouchableOpacity>
                <View className="w-full h-4"></View>
                <View className="w-[90%] mt-4 mx-auto gap-y-3 h-full flex-1 items-center">
                  <Image
                    source={require("../../assets/images/TripEnded.png")}
                    className="w-[50px] h-[50px]"
                    resizeMode="contain"
                  />
                  <Text className="my-4 text-[#151B2D] text-[20px] font-medium">
                    Password changed successfully
                  </Text>
                  <View className="mt-3 w-full">
                    <Button
                      text="Book ride"
                      buttonClassName="bg-[#EDEDED]"
                      textClassName="text-[#151B2D]"
                      onClick={() => {
                        router.replace("/(tabs)/Home");
                      }}
                    />
                  </View>
                </View>
              </View>
            </BottomSheet>
          )}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: "white",
    width: "100%",
  },
  backdrop: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    top: -STATUSBAR_HEIGHT,
  },
  bottomSheetContainer: {
    zIndex: 1000,
  },
  bottomSheetIndicator: {
    backgroundColor: "#fff",
  },
  bottomSheetBackground: {
    backgroundColor: "#fff",
  },
});
